---
description: Overview about projects
globs: *
alwaysApply: false
---
# Project Overview

This is a Next.js-based monorepo containing multiple applications:

1. **Admin Portal** (`apps/admin-portal`) - Administration interface for managing the entire platform.
2. **Client Portal** (`apps/client-portal`) - Interface for clients to access the platform services.

## Core Technology Stack:

- **Framework**: Next.js (App Router)
- **State Management**: Zustand + React Query
- **Styling**: Tailwind CSS + Flowbite
- **Forms**: React Hook Form + Zod
- **API**: RESTful API integration via React Query

## Key Directories:

- `apps/admin-portal` - Admin interface application
- `apps/client-portal` - Client interface application
- `packages/shared-ui` - Shared UI components used across applications

## Project Architecture:

The project follows a standard Next.js App Router organization with:
- Route components within `app` directories
- Feature components in `components/features`
- UI components in `components/ui`
- API integrations in `lib/apis`
- Global state in `stores`
- Utility functions in `utils`

Each portal follows a similar structure but has domain-specific implementations.

## Architecture

This is a Next.js monorepo project managed with NX, containing two main applications and shared packages.

### Admin Portal ([apps/admin-portal](mdc:apps/admin-portal))

Administrative interface for managing the platform:

- User Management
- Role Management
- Study Management
- Site Management
- Scanner Management
- Assignment Management
- Settings Management

### Client Portal ([apps/client-portal](mdc:apps/client-portal))

Client-facing portal for study participants and site managers:

- Document Management
- Patient Management
- Study Management
- Task Management
- Protocol Explorer
- EDC (Electronic Data Capture)
- eSource Management
- ISF (Investigator Site File)

### Shared Package ([packages/shared-ui](mdc:packages/shared-ui))

Reusable UI components and utilities shared between applications:

- Form Components
- UI Components
- Utility Functions
- Common Types

## Technology Stack

- **Framework**: Next.js 13+ with App Router
- **Language**: TypeScript
- **State Management**: Zustand + React Context
- **Styling**: Tailwind CSS
- **Data Fetching**: React Query
- **Testing**: Jest + React Testing Library
- **Package Manager**: pnpm
- **Monorepo Tools**: NX

## Key Features

1. **Authentication & Authorization**
   - Role-based access control
   - Multi-tenant support
   - Secure authentication flow

2. **Study Management**
   - Protocol management
   - Visit scheduling
   - Document management
   - Patient tracking

3. **Document Management**
   - Version control
   - Document preview
   - Approval workflow
   - Audit trail

4. **Data Management**
   - Electronic Data Capture
   - eSource data collection
   - Data validation
   - Export capabilities

## Development Workflow

1. **Local Development**
- Monorepo managed by Nx with two main apps: client and admin portal
- Authentication handled by Clerk
- Follows modern Next.js best practices with TypeScript

# Client Portal Features
## Study Management
- Multi-site study dashboards
- Protocol version comparisons
- Enrollment progress tracking
- Site-specific study configurations
- Principal investigator assignments

## Task Management
- Priority-based task queues (High/Medium/Low)
- Patient-study-task relationships
- Due date tracking with visual alerts
- Assignee management with handoff workflows
- Task completion documentation

## Document Workflows
### eSource
- Direct data entry forms
- Real-time validation
- Patient-facing surveys
- Source data verification

### EDC (Electronic Data Capture)
- Case Report Form (CRF) management
- Query resolution tracking
- Data field validation
- Cross-form consistency checks

### ISF (Investigator Site File)
- Regulatory document storage
- Protocol version control
- Essential document checklist
- Expiration date tracking

### eCTMRS Integration
- Monitoring visit reports
- Site activation documents
- Centralized monitoring tools
- Corrective action tracking

### Document Exchange
- Cross-site file sharing
- Version-controlled transfers
- Audit-ready change logs
- eSignature workflows

## Patient Management
- Visit scheduling calendar
- Visit window compliance checks
- Procedure completion tracking
- Document collection status
- Adverse event reporting

## Quality Control
- Dual QC workflows (QC1/QC2)
- Status transition controls
- Rejection reason tracking
- Final approval gates
- Annotated feedback system

## Protocol Compliance
- Informed consent tracking
- Visit window adherence
- Missing document alerts
- Protocol deviation logs
- Audit trail generation

## System Integration
- PDF viewer with annotation
- Document metadata enforcement:
  - Type (PDF/Word/Excel)
  - Category (CRF/Protocol/etc)
  - Last modified timestamp
  - Ownership chain
- Mobile-optimized review
- Bulk export capabilities

## Security & Access
- Role-based document access
- Sensitive data redaction
- Activity audit trails
- Session timeout controls
- Two-factor authentication

## Implementation Evidence
- Dedicated components for each doc type
- Status state machines in UI
- Version comparison interfaces
- Mobile-first responsive layouts
- Complex document relationship mapping

# Admin Portal Features
## Core Entity Management
### Site Management
- Site metadata (name, address, status)
- Active/inactive status control
- Creation date tracking
- Geographic address formatting

### User Management
- User profile overview (name, email, phone)
- Status badges (active/inactive)
- Last login tracking
- Bulk edit capabilities

### Study Oversight
- Study-site relationships
- Status indicators (active/inactive)
- Principal investigator assignments
- Date tracking (created/start/end dates)

### Scanner Management
- Device display name configuration
- Activation status control
- Technical description fields
- Creation date tracking

## Operational Modules
### Sponsor Management
- Branding control (logo management)
- Address standardization
- Active/inactive status toggling
- Creation date audit trails

### Group Management
- Group type classifications
- Institution type categorization
- Site-group relationships
- Address formatting utilities

### Assignment Tracking
- Assignment type categorization
- Group-task relationships
- Creation date records
- Status tracking

### System Features
- Consistent edit modals for all entities
- Loading skeleton states for data fetching
- Tabbed interface for related entities
- CSV export-ready data tables
- Audit-ready creation timestamps

### Security & Compliance
- Clerk authentication integration
- Role-based edit permissions
- Activity tracking through timestamps
- Sensitive data redaction

### UI Patterns
- Standardized overview cards
- Status badge components
- Back navigation persistence
- Mobile-responsive grids
- Accessible form controls
