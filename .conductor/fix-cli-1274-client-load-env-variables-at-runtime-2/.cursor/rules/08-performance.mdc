---
description: Guidelines for performance optimization
globs:
alwaysApply: false
---

# Performance Optimization Guidelines

## React Component Optimization

- Use React.memo for expensive component renders
- Implement proper dependency arrays in hooks
- Use useCallback for event handlers passed as props
- Use useMemo for expensive calculations
- Avoid unnecessary re-renders with proper state management
- Use proper keys for list items (avoid index as key when possible)

## Data Fetching

- Implement proper caching with React Query
- Use stale-while-revalidate pattern
- Prefetch data when possible
- Implement pagination for large data sets
- Use optimistic updates for mutations
- Batch API requests when appropriate

## Rendering Optimization

- Implement code splitting with dynamic imports
- Use Next.js Image component for optimized images
- Lazy load non-critical components
- Use skeleton loading states
- Implement progressive loading for large pages
- Use appropriate suspense boundaries

## Bundle Optimization

- Keep dependencies to a minimum
- Use tree-shaking friendly imports
- Monitor bundle size with build analytics
- Use dynamic imports for code splitting
- Minimize CSS with proper Tailwind configuration
- Eliminate duplicate dependencies

## State Management

- Keep state as local as possible
- Use proper state selectors with Zustand
- Implement atomic state updates
- Avoid unnecessary global state
- Use proper React Query caching
- Implement proper state normalization

## Server Components

- Use Next.js Server Components for data-heavy components
- Keep client components lean
- Move data fetching to server components when possible
- Use proper streaming with React Suspense
- Implement incremental Static Regeneration for static pages
- Balance between static and dynamic content

## Monitoring and Analytics

- Implement proper performance monitoring
- Track core web vitals
- Use React DevTools Profiler for component performance
- Monitor API response times
- Track user-centric performance metrics
- Implement proper error tracking

## Code Splitting
- Use dynamic imports for code splitting
- Implement proper lazy loading
- Use Next.js automatic code splitting
- Avoid large bundles
- Monitor bundle sizes

## Image Optimization
- Use Next.js Image component
- Implement proper image sizing
- Use appropriate image formats
- Implement proper image loading strategies
- Use image placeholders for better UX

## CSS Optimization
- Use Tailwind's JIT mode
- Purge unused CSS
- Avoid large CSS files
- Use proper CSS loading strategies
- Implement proper CSS splitting

## Font Optimization
- Use Next.js font optimization
- Implement proper font loading strategies
- Use variable fonts when appropriate
- Implement proper font fallbacks
- Avoid font flashing

## JavaScript Optimization
- Avoid large dependencies
- Use tree-shaking
- Implement proper code splitting
- Avoid blocking JavaScript
- Use proper async/defer strategies

## Server-Side Optimization
- Implement proper caching strategies
- Use proper data fetching strategies
- Implement proper server-side rendering
- Use incremental static regeneration when appropriate
- Implement proper edge functions
