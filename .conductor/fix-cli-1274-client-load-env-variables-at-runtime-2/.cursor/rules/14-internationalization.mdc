---
description: Internationalization guidelines
globs: ["**/i18n/**", "**/translations/**", "**/messages/**"]
alwaysApply: false
---

# Internationalization Guidelines

## Core Libraries
- Use next-intl for internationalization
- Follow the Next.js internationalization patterns
- Implement proper locale detection
- Handle locale in routes
- Implement proper translations

## Translation Keys
- Use proper translation key naming
- Group related translation keys
- Use proper translation key hierarchy
- Document translation keys
- Implement proper translation key management

## Translation Files
- Organize translation files by language
- Use proper file naming conventions
- Group related translations
- Document translations
- Implement proper translation file management

## Translation Usage
- Use proper translation functions
- Handle pluralization properly
- Handle gender properly
- Handle formatting properly
- Implement proper fallbacks

## Date and Time
- Use proper date and time formatting
- Handle timezones properly
- Use proper date and time libraries
- Implement proper date and time localization
- Handle different calendar systems

## Numbers and Currency
- Use proper number formatting
- Handle currency properly
- Use proper number libraries
- Implement proper number localization
- Handle different number systems

## RTL Support
- Implement proper RTL support
- Use proper CSS for RTL
- Handle text direction properly
- Implement proper layout for RTL
- Test with RTL languages

## Locale Detection
- Implement proper locale detection
- Handle user preferences
- Use proper fallbacks
- Implement proper locale switching
- Handle locale in URLs

## Testing
- Test with different locales
- Test with different languages
- Test with different text directions
- Test with different date and time formats
- Test with different number formats
