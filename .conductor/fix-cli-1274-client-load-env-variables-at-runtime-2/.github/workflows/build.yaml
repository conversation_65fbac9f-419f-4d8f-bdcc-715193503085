name: Images CI

permissions:
  actions: write
  checks: write
  deployments: read
  contents: read
  packages: write
  pages: read
  pull-requests: read
  repository-projects: read
  statuses: read
  

on:
  push:
    branches:
      - develop
      - test*

env:
  CI: true
  GIT_BRANCH: ${{ github.head_ref || github.ref_name }}
  REPO: ghcr.io/clincove-eng
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  NX_BASE: origin/develop~1
  NX_HEAD: origin/develop
  NEXT_TELEMETRY_DISABLED: '1'
  APP_NAME: ''
  APP_REPO: ''
  RELEASE_VERSION: ''
  TAG: ''
  GIT_SHA_SHORT: ''
  BUILD_ALL_APPS: true # ${{ github.event.inputs.build_all_apps || 'false' }}

jobs:
  init:
    timeout-minutes: 20
    runs-on: ubuntu-latest
    if: ${{ github.triggering_actor != 'renovate[bot]' }}
    permissions:
      contents: 'read'
      actions: 'read'
      checks: 'write'
      pull-requests: 'write'
      packages: 'read'
    outputs:
      affected-apps: ${{ steps.affected.outputs.apps }}
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 10
          run_install: false

      - name: Setup Node.js
        id: setup-node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: 'pnpm'

      - name: Cache node modules
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-images-${{ env.cache-name }}-${{ steps.setup-node.outputs.node-version }}--${{ hashFiles('**/pnpm-lock.yaml') }}

      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: Install Deps (no cache)
        run: |
          pnpm config set //npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}
          pnpm install

      # Check which apps are affected by the changes
      - name: Check affected apps
        id: affected
        run: |
          # Get affected projects
            if [ "$BUILD_ALL_APPS" = "true" ]; then
              # Build all apps
              # ,{\"name\":\"config-panel\",\"repo\":\"clincove-config-panel\",\"dockerfile\":\"./apps/config-panel/ci/Dockerfile\"}
              MATRIX_APPS="{\"name\":\"admin-portal\",\"repo\":\"clincove-admin-portal\",\"dockerfile\":\"./apps/admin-portal/ci/Dockerfile\"},{\"name\":\"client-portal\",\"repo\":\"clincove-client-portal\",\"dockerfile\":\"./apps/client-portal/ci/Dockerfile\"}"
              echo "apps=[$MATRIX_APPS]" >> $GITHUB_OUTPUT
            else
              AFFECTED=$(pnpm nx show projects --affected --type=app --json)
              echo "Affected apps: $AFFECTED"
              MATRIX_APPS=""
              for app in admin-portal client-portal config-panel; do
                if echo "$AFFECTED" | grep -q "\"$app\""; then
                  if [ -n "$MATRIX_APPS" ]; then
                    MATRIX_APPS="$MATRIX_APPS," 
                  fi
                  case $app in
                    admin-portal)
                      MATRIX_APPS="$MATRIX_APPS{\"name\":\"admin-portal\",\"repo\":\"clincove-admin-portal\",\"dockerfile\":\"./apps/admin-portal/ci/Dockerfile\"}" 
                      ;;
                    client-portal)
                      MATRIX_APPS="$MATRIX_APPS{\"name\":\"client-portal\",\"repo\":\"clincove-client-portal\",\"dockerfile\":\"./apps/client-portal/ci/Dockerfile\"}" 
                      ;;
                    config-panel)
                      MATRIX_APPS="$MATRIX_APPS{\"name\":\"config-panel\",\"repo\":\"clincove-config-panel\",\"dockerfile\":\"./apps/config-panel/ci/Dockerfile\"}" 
                      ;;
                  esac
                fi
              done
              if [ -n "$MATRIX_APPS" ]; then
                echo "apps=[$MATRIX_APPS]" >> $GITHUB_OUTPUT
              else
                echo "apps=[]" >> $GITHUB_OUTPUT
              fi
            fi

      # - name: Audit Checker
      #   run: pnpm run audit-checker

  build-images-canary:
    name: Build Canary Images
    if: startsWith(github.ref, 'refs/heads/') && ${{ github.triggering_actor != 'renovate[bot]' }} && needs.init.outputs.affected-apps != '[]'
    needs: init
    runs-on: ubuntu-latest
    timeout-minutes: 20
    strategy:
      matrix:
        app: ${{ fromJSON(needs.init.outputs.affected-apps) }}
    steps:
      - uses: actions/checkout@v4
        with:
          # We need to fetch all branches and commits so that Nx affected has a base to compare against.
          fetch-depth: 0

      - uses: pnpm/action-setup@v4
        name: Install pnpm
        with:
          version: 10
          run_install: false
          
      - name: Setup Node.js
        id: setup-node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # cache: 'pnpm'

      - name: Get NPM Cache
        id: cache-npm
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: node_modules
          key: ${{ runner.os }}-images-${{ env.cache-name }}-${{ steps.setup-node.outputs.node-version }}--${{ hashFiles('**/pnpm-lock.yaml') }}

      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: Check for Cache Hit
        run: |
          echo "Something went wrong with the cache"
          exit 1

      - name: Setup Docker
        id: setup-buildx
        uses: docker/setup-buildx-action@v3

      # Setup the necessary environment variables
      - run: echo "github.event_name="${{ github.event_name }}
      - run: echo "GIT_BRANCH="$GIT_BRANCH
      # Setup TAG
      - run: |
          case $GIT_BRANCH in
            develop) echo "TAG=latest" >> $GITHUB_ENV ;;
            test*) echo "TAG=$GIT_BRANCH" >> $GITHUB_ENV ;;
            *) echo "TAG=test" >> $GITHUB_ENV ;;
          esac
      - run: echo "TAG="$TAG
      # Add GIT_SHA_SHORT env property with commit short sha
      - run: echo "GIT_SHA_SHORT=`echo ${GITHUB_SHA} | cut -c1-7`" >> $GITHUB_ENV
      - run: echo "GIT_SHA_SHORT="$GIT_SHA_SHORT

      # Build and push container images to GHCR
      # For Nx-managed containers, we build with Nx first then tag and push
      # For custom Dockerfiles, we use docker/build-push-action directly

      # Login to GHCR
      - name: Login to GHCR
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # Build with Nx if no dockerfile specified, otherwise use custom dockerfile
      - name: Build with Nx ${{ matrix.app.name }}
        if: ${{ matrix.app.dockerfile == '' || matrix.app.dockerfile == null }}
        run: npm run nx container ${{ matrix.app.name }} --configuration=ci --verbose
        env:
          DEPLOYMENT_ENV: automation
          GIT_COMMIT: ${{ env.GIT_SHA_SHORT }}
          GIT_REF: ${{ env.GIT_BRANCH }}

      # - name: Cache
      #   uses: actions/cache@v4
      #   id: cache
      #   with:
      #     path: cache-mount
      #     key: ${{ runner.os }}-${{ matrix.app.name }}-cache-mount-${{ hashFiles('**/pnpm-lock.yaml') }}
    
      - name: Cache
        uses: actions/cache@v4
        id: cache
        with:
          path: pnpm-cache
          key: pnpm-cache-${{ hashFiles('pnpm-lock.yaml') }}
          restore-keys: |
            pnpm-cache-
      - name: Inject cache into docker
        uses: reproducible-containers/buildkit-cache-dance@v3
        with:
          builder: ${{ steps.setup-buildx.outputs.name }}
          cache-map: |
            {
              "pnpm-cache": "/pnpm"
            }
          skip-extraction: ${{ steps.cache.outputs.cache-hit }}

      # - name: Restore Docker cache mounts
      #   uses: reproducible-containers/buildkit-cache-dance@v3
      #   with:
      #     builder: ${{ steps.setup-buildx.outputs.name }}
      #     cache-dir: cache-mount
      #     dockerfile: ${{ matrix.app.dockerfile }}
      #     skip-extraction: ${{ steps.cache.outputs.cache-hit }}
          # cache-map: |
          #   {
          #     "pnpm-build": {
          #       "target": "/pnpm/store",
          #       "id": "pnpm-build"
          #     },
          #     "pnpm-prod": {
          #       "target": "/pnpm/store", 
          #       "id": "pnpm-prod"
          #     },
          #     "pnpm-metadata": {
          #       "target": "/root/.local/share/pnpm",
          #       "id": "pnpm-metadata"
          #     }
          #   }

      # Build and push with docker/build-push-action for custom dockerfiles
      - name: Build and push ${{ matrix.app.name }}
        if: ${{ matrix.app.dockerfile != '' && matrix.app.dockerfile != null }}
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ matrix.app.dockerfile }}
          push: ${{ github.event_name == 'push' || startsWith(env.GIT_BRANCH, 'test') }}
          tags: |
            ${{ env.REPO }}/${{ matrix.app.repo }}:${{ env.TAG }}
            ${{ env.REPO }}/${{ matrix.app.repo }}:${{ env.TAG }}-${{ env.GIT_SHA_SHORT }}
          build-args: |
              GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}
              NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY_DEV }}
          cache-from: type=gha,scope=${{ matrix.app.name }}
# type=registry,ref=${{ env.REPO }}/${{ matrix.app.repo }}:buildcache
          cache-to: type=gha,mode=max,scope=${{ matrix.app.name }}
# type=registry,ref=${{ env.REPO }}/${{ matrix.app.repo }}:buildcache,mode=max
          platforms: linux/amd64
          provenance: false
          sbom: false
