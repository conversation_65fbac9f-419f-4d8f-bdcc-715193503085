name: Config Service - Production Environment

on:
  push:
    tags:
      - 'v*'
    paths:
      - '.github/**'
      - 'apps/config-panel/**'
      - 'packages/**'
      - 'package.json'
      - 'pnpm-lock.yaml'

env:
  SLACK_WEBHOOK_URL: ${{ secrets.ORG_SLACK_WEBHOOK_URL }}
  VERSION: ${{ github.ref_type == 'branch' && github.sha || github.ref_name }}
  SESSION_TIMEOUT: "60"
  INACTIVTY_TIMEOUT_MINUTES: "30"

jobs:
  deploy:
    runs-on: self-hosted
    environment:
      name: production-config-panel
    permissions:
      contents: "read"
      id-token: "write"
    env:
      IMAGE_REPOSITORY: ${{ vars.REGISTRY }}/${{ vars.CONFIG_SERVICE_IMAGE_NAME }}
      REPLICAS: ${{ vars.REPLICAS }}
      CPU: ${{ vars.CPU }}
      RAM: ${{ vars.RAM }}
      API_CONFIG_URL: ${{ vars.API_CONFIG_URL }}
      API_ADMIN_URL: ${{ vars.API_ADMIN_URL }}
      APP_ENV: ${{ vars.APP_ENV }}
    steps:
      - id: checkout-repo
        name: Checkout repository
        uses: actions/checkout@v4

      - uses: clincove-eng/github-actions/setup-docker@v2
        with:
          service_account: ${{ secrets.ORG_WORKLOAD_IDENTITY_SA_EMAIL }}
          workload_identity_provider: ${{ secrets.ORG_WORKLOAD_IDENTITY_PROVIDER_NAME }}
          registry: ${{ secrets.ORG_ARTIFACT_REGISTRY_BASE_URL }}

      - id: prepare-env-vars
        name: Prepare environment variables
        shell: bash
        run: |
          echo "NEXT_PUBLIC_API_CONFIG_URL=$API_CONFIG_URL" > .env
          echo "NEXT_PUBLIC_API_ADMIN_URL=$API_ADMIN_URL" >> .env
          echo "NEXT_PUBLIC_INACTIVITY_TIMEOUT=$INACTIVTY_TIMEOUT_MINUTES" >> .env
          echo "SESSION_TIMEOUT=$SESSION_TIMEOUT" >> .env
          echo "NEXT_PUBLIC_APP_ENV=$APP_ENV" >> .env
          echo "NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=${{ secrets.CLERK_PUBLISHABLE_KEY }}" >> .env
          echo "CLERK_SECRET_KEY=${{ secrets.CLERK_SECRET_KEY }}" >> .env

      - uses: clincove-eng/github-actions/build-image@v2
        with:
          image_name: ${{ vars.CONFIG_SERVICE_IMAGE_NAME }}
          registry: ${{ vars.REGISTRY }}
          github_token: ${{ secrets.ORG_GITHUB_TOKEN }}
          file: apps/config-panel/ci/Dockerfile

      - id: replace-template-vars
        name: Replace template variables for helm
        run: envsubst < apps/config-panel/ci/values.tpl.yml > values.yml

      - uses: clincove-eng/github-actions/deploy-image@v2
        with:
          application_type: ${{ vars.CONFIG_SERVICE_APPLICATION_TYPE }}
          application_name: ${{ vars.APPLICATION_NAME }}
          cluster_name: ${{ vars.US_CLUSTER_NAME }}
          location: ${{ vars.US_CLUSTER_LOCATION }}
          country: "NONE"
          notification_script_path: ./.github/notification/slack.sh
