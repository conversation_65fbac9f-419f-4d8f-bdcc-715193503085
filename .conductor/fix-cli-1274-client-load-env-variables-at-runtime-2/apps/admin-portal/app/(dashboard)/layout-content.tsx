"use client";

import type { PropsWithChildren } from "react";
import { twMerge } from "tailwind-merge";

import { useSidebarContext } from "@/contexts/sidebar-context";

export function LayoutContent({ children }: PropsWithChildren) {
  const sidebar = useSidebarContext();

  return (
    <div
      id="main-content"
      className={twMerge(
        "relative h-full w-full overflow-x-hidden bg-gray-100 dark:bg-gray-900",
        sidebar.desktop.isCollapsed ? "lg:ml-16" : "lg:ml-[270px]",
      )}
    >
      <div className="flex h-[calc(100vh-64px)] flex-col gap-4  overflow-y-auto px-4 py-6">
        {children}
      </div>
    </div>
  );
}
