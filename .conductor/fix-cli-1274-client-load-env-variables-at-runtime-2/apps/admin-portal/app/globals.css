@tailwind base;
@tailwind components;
@tailwind utilities;

/* Style for scrollbar for both light and dark mode */
.select-options::-webkit-scrollbar {
  width: 8px;
}

.select-options::-webkit-scrollbar-thumb {
  background-color: #bdbaba;
  border-radius: 20px;
}

.select-options::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

/* chart styles */
.apexcharts-tooltip {
  @apply rounded-lg border-0 bg-white text-gray-500 shadow-lg dark:bg-gray-700 dark:text-gray-400 !important;
}

.apexcharts-tooltip .apexcharts-tooltip-title {
  @apply border-b border-gray-200 bg-gray-100 px-4 py-2 dark:border-gray-500 dark:bg-gray-600 !important;
}

.apexcharts-xaxistooltip {
  @apply rounded-lg border-0 bg-white text-gray-500 shadow-lg dark:bg-gray-700 dark:text-gray-300 !important;
}

.apexcharts-tooltip .apexcharts-tooltip-text-y-value {
  @apply dark:text-white;
}

.apexcharts-xaxistooltip-text {
  @apply text-sm font-medium !important;
}

.apexcharts-xaxistooltip:before,
.apexcharts-xaxistooltip:after {
  @apply border-0 !important;
}

/* SVG map styles */
.svgMap-map-wrapper {
  @apply bg-white !important;
}

.svgMap-map-image {
  @apply dark:bg-gray-800;
}

.svgMap-map-controls-wrapper {
  @apply bottom-0 left-0 shadow-none dark:bg-gray-800 !important;
}

.svgMap-map-controls-zoom {
  @apply dark:bg-gray-800 !important;
}

.svgMap-map-wrapper .svgMap-control-button {
  @apply rounded-lg border border-solid border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-600 !important;
}

.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:after,
.svgMap-map-wrapper .svgMap-control-button.svgMap-zoom-button:before {
  @apply dark:bg-gray-600 dark:hover:bg-gray-500;
}

.svgMap-map-wrapper .svgMap-control-button:first-child {
  @apply mr-2 !important;
}

.svgMap-tooltip {
  @apply rounded-lg border-0 bg-white text-left shadow-lg dark:bg-gray-700 !important;
}

.svgMap-tooltip
  .svgMap-tooltip-content-container
  .svgMap-tooltip-flag-container {
  @apply mr-2 inline-block border-0 p-0 text-left !important;
}

.svgMap-tooltip
  .svgMap-tooltip-content-container
  .svgMap-tooltip-flag-container
  .svgMap-tooltip-flag {
  @apply inline-block h-4 border-0 p-0 !important;
}

.svgMap-tooltip .svgMap-tooltip-title {
  @apply inline-block pt-2 text-sm font-semibold text-gray-900 dark:text-white !important;
}

.svgMap-tooltip .svgMap-tooltip-content {
  @apply mt-0 !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td {
  @apply text-left text-sm font-normal text-gray-500 dark:text-gray-400 !important;
}

.svgMap-tooltip .svgMap-tooltip-content table td span {
  @apply text-left text-sm font-semibold text-gray-900 dark:text-white !important;
}

.svgMap-tooltip .svgMap-tooltip-pointer {
  @apply hidden !important;
}

.svgMap-map-wrapper .svgMap-country {
  @apply dark:stroke-gray-800 !important;
}

.svgMap-country[fill="#4B5563"] {
  @apply fill-[#4B5563] !important;
}

/* kanban styles */

.drag-card {
  @apply opacity-100 !important;
  @apply rotate-6;
}

.ghost-card {
  @apply bg-gray-100/40 dark:bg-gray-600/40 !important;
}

.timescape-root {
  display: flex;
  align-items: center;
  gap: 2px;
  /* width: fit-content; */
  border: 1px solid #bbb;
  padding: 5px;
  user-select: none;
  border-radius: 10px;
  transition: 100ms;
}

.timescape-root:focus-within {
  outline: 1px solid #1a56db;
  border-color: #1a56db;
}

.timescape-input {
  font-variant-numeric: tabular-nums;
  height: fit-content;
  font-size: 18px;
  color: #333;
  border: none;
  outline: none;
  cursor: default;
  user-select: none;
  max-width: 50px;
}

.timescape-input::selection {
  background: none;
}

.timescape-input:focus {
  background-color: #1a56db;
  color: #fff;
  border-radius: 6px;
  padding: 2px;
}

.separator {
  font-size: 80%;
  color: #8c8c8c;
  margin: 0;
}

.scrollbar::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}
.scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 5px;
}
.scrollbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 5px;
}
.scrollbar::-webkit-scrollbar-button:decrement,
.scrollbar::-webkit-scrollbar-button:increment {
  width: 0px;
}

.monaco-editor {
  position: absolute !important;
}
