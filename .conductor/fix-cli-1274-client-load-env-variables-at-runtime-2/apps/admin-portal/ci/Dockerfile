FROM node:20-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN corepack enable
RUN corepack prepare pnpm@10 --activate

WORKDIR /app

#######################################################################
#######################################################################
#######################################################################
FROM base AS prod-deps
ARG GITHUB_TOKEN
RUN pnpm config set //npm.pkg.github.com/:_authToken=$GITHUB_TOKEN

COPY pnpm-lock.yaml package.json ./
RUN --mount=type=cache,id=pnpm-prod,target=/pnpm/store,sharing=locked \
    --mount=type=cache,id=pnpm-metadata,target=/root/.local/share/pnpm,sharing=locked \
    pnpm install --prod --frozen-lockfile --ignore-scripts

#######################################################################
#######################################################################
#######################################################################
FROM base AS build-deps
ARG GITHUB_TOKEN
RUN pnpm config set //npm.pkg.github.com/:_authToken=$GITHUB_TOKEN

COPY pnpm-lock.yaml package.json ./
RUN --mount=type=cache,id=pnpm-build,target=/pnpm/store,sharing=locked \
    --mount=type=cache,id=pnpm-metadata,target=/root/.local/share/pnpm,sharing=locked \
    pnpm install --frozen-lockfile --ignore-scripts

#######################################################################
#######################################################################
#######################################################################
# TODO: Move linting to PR, don't do it in the docker build
FROM build-deps AS lint
COPY .eslintrc.json nx.json tsconfig.base.json ./
COPY apps/admin-portal/ ./apps/admin-portal/
COPY packages/shared-ui/ ./packages/shared-ui/

RUN pnpm exec nx run admin-portal:lint

#######################################################################
#######################################################################
#######################################################################
FROM build-deps AS build
ENV NX_DAEMON=false \
    NODE_ENV=production

ARG NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY
ENV NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=$NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY

COPY . .
# COPY nx.json tsconfig.base.json ./
# COPY packages/shared-ui/ ./packages/shared-ui/
# COPY apps/admin-portal/ ./apps/admin-portal/

# Building the shared-ui shouldn't be necessary, should be built via NX project graph
RUN pnpm nx run shared-ui:build
RUN pnpm nx run admin-portal:build

# Production image
#######################################################################
#######################################################################
#######################################################################
FROM node:20-alpine AS release

RUN addgroup --system --gid 1001 clincove && \
    adduser --system --uid 1001 clincove

WORKDIR /app

# Copy the standalone Next.js application
COPY --from=prod-deps --chown=clincove:clincove /app/node_modules /app/node_modules
COPY --from=build --chown=clincove:clincove /app/package.json /app/package.json
COPY --from=build --chown=clincove:clincove /app/apps/admin-portal/.next/standalone ./
COPY --from=build --chown=clincove:clincove /app/apps/admin-portal/.next/static ./apps/admin-portal/.next/static
COPY --from=build --chown=clincove:clincove /app/apps/admin-portal/public ./apps/admin-portal/public

USER clincove

EXPOSE 3000

ENV PORT=3000 \
    NODE_ENV=production \
    HOSTNAME="0.0.0.0"

CMD ["node", "apps/admin-portal/server.js"]
