"use client";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { EntitlementsTab } from "./tabs/entitlements";
import { OrganizationsTab } from "./tabs/organizations";

const BILLINGS_TABS = [
  {
    key: "organizations",
    title: "Organizations",
    content: <OrganizationsTab />,
  },
  {
    key: "entitlements",
    title: "Entitlements",
    content: <EntitlementsTab />,
  },
];

const BREADCRUMB_ITEMS = [{ label: "Billings" }];

export const BillingsContent = () => {
  return (
    <>
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Billings</PageHeader>
      <TabsWrapper tabs={BILLINGS_TABS} />
    </>
  );
};
