"use client";

import { Card } from "flowbite-react";

import { SearchField } from "@/components/shared";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { entitlementsColumns } from "./columns";
import { useEntitlements } from "./hooks/use-entitlements-queries";

export const EntitlementsTab = () => {
  const { data, isPending, isPlaceholderData } = useEntitlements();

  return (
    <Card className="[&>div]:p-0">
      <div className="flex items-center justify-between p-4">
        <h2 className="text-lg font-semibold dark:text-white">Entitlements</h2>
        <SearchField className="max-w-60" placeholder="Search..." />
      </div>
      {isPending ? (
        <TableLoading columns={entitlementsColumns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={entitlementsColumns} data={data?.results ?? []} />
          {data?.metadata && <TableDataPagination metadata={data.metadata} />}
        </LoadingWrapper>
      )}
    </Card>
  );
};
