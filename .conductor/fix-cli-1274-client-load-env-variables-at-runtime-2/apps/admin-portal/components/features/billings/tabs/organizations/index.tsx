"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { useGroups } from "@/components/features/settings/groups/hooks/use-groups";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { Group } from "@/lib/apis/groups/types";

import { generateOrganizationsColumns } from "./columns";
import { OrganizationEntitlementsModal } from "./organization-entitlements-modal";
import { OrganizationsFilters } from "./organizations-filters";

export const OrganizationsTab = () => {
  const { data, isPending, isPlaceholderData } = useGroups();
  const [selectedOrganization, setSelectedOrganization] =
    useState<Group | null>(null);

  const handleViewEntitlements = (organization: Group) => {
    setSelectedOrganization(organization);
  };

  const handleCloseModal = () => {
    setSelectedOrganization(null);
  };

  const columns = useMemo(
    () =>
      generateOrganizationsColumns({
        onView: handleViewEntitlements,
      }),
    [],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="flex items-center justify-between gap-3 p-4">
          <h2 className="text-lg font-semibold dark:text-white">
            Organizations
          </h2>
          <OrganizationsFilters />
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </Card>

      {selectedOrganization && (
        <OrganizationEntitlementsModal
          isOpen={!!selectedOrganization}
          onClose={handleCloseModal}
          organization={selectedOrganization}
        />
      )}
    </>
  );
};
