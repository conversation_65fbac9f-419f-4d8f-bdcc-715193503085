import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import type { AuditLogEntry } from "@/lib/apis/audit-log/types";
import { capitalize, snakeCaseToCapitalized } from "@/utils/string";

export const auditLogColumns: ColumnDef<AuditLogEntry>[] = [
  {
    accessorKey: "profile.name",
    header: "Profile Name",
    cell: ({ row }) => {
      return row.original.profile ? (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/users/${row.original.profile.userId}`}
        >
          {row.original.profile.name}
        </Link>
      ) : (
        "-"
      );
    },
  },
  {
    accessorKey: "resourceType",
    header: "Resource Type",
    cell: ({ row }) => {
      return capitalize(row.original.resourceType);
    },
  },
  {
    accessorKey: "action",
    header: "Action",
    cell: ({ row }) => {
      return snakeCaseToCapitalized(row.original.action);
    },
  },
  {
    accessorKey: "summary",
    header: "Summary",
  },
];
