import { useState } from "react";
import { HiDocumentText, HiExclamation } from "react-icons/hi";

import { Button } from "@/components/ui/button";

import { ActivityFeed } from "./activity-feed";
import { CriticalEvents } from "./critical-events";
import { useAuditLogFilters } from "./hooks/use-audit-log-filters";

const ACTIVITY_TABLES = {
  "activity-feed": <ActivityFeed />,
  "critical-events": <CriticalEvents />,
};

type TableType = keyof typeof ACTIVITY_TABLES;

export const AuditLogTab = () => {
  const [table, setTable] = useState<TableType>("activity-feed");
  const { clearAllFilters } = useAuditLogFilters();

  const handleTableSwitch = (newTable: TableType) => {
    setTable(newTable);
    clearAllFilters();
  };

  return (
    <div className="space-y-6">
      <div className=" flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
        <h2 className="text-lg font-semibold text-gray-900 md:text-xl dark:text-white">
          Audit & Activity Log
        </h2>

        <div className="flex gap-2">
          <Button
            variant={table === "activity-feed" ? "primary" : "outline"}
            size="sm"
            onClick={() => handleTableSwitch("activity-feed")}
            className="flex-1 focus:ring-0 sm:flex-none"
          >
            <HiDocumentText className="h-4 w-4" />
            Activity Feed
          </Button>
          <Button
            variant={table === "critical-events" ? "primary" : "outline"}
            size="sm"
            onClick={() => handleTableSwitch("critical-events")}
            className="flex-1 focus:ring-0 sm:flex-none"
          >
            <HiExclamation className="h-4 w-4" />
            Critical Events
          </Button>
        </div>
      </div>

      {ACTIVITY_TABLES[table]}
    </div>
  );
};
