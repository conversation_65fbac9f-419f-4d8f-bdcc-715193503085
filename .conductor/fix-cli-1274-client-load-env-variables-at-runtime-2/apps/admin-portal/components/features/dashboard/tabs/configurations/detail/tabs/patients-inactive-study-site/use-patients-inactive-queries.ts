import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const usePatientsInactiveKeys = {
  all: () => ["data-integrity", "patients-inactive"] as const,
  list: (params?: MetadataParams) =>
    [...usePatientsInactiveKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const usePatientsInactive = () => {
  const { page } = usePagination();
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: usePatientsInactiveKeys.list(params),
    queryFn: () => api.dataIntegrity.getPatientsInactive(params),
    placeholderData: (prev) => prev,
  });
};
