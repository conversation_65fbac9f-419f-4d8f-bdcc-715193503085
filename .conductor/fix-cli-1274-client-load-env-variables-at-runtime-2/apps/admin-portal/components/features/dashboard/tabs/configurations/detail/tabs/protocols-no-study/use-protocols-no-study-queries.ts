import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useProtocolsNoStudyKeys = {
  all: () => ["data-integrity", "protocols-no-study"] as const,
  list: (params?: MetadataParams) =>
    [...useProtocolsNoStudyKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const useProtocolsNoStudy = () => {
  const { page } = usePagination();
  const params = {
    page,
    take: TAKE_50,
  };
  return useQuery({
    queryKey: useProtocolsNoStudyKeys.list(params),
    queryFn: () => api.dataIntegrity.getProtocolsNoStudy(params),
    placeholderData: (prev) => prev,
  });
};
