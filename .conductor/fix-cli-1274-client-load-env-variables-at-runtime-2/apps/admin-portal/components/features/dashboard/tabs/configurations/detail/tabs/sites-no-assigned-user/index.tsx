"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { sitesNoAssignedUserColumns } from "./columns";
import { useSitesNoAssignedUser } from "./use-sites-no-assigned-user-queries";

export const SitesNoAssignedUserTab = () => {
  const {
    data: sitesData,
    isPending: isLoadingSites,
    isPlaceholderData: isSitesPlaceholderData,
  } = useSitesNoAssignedUser();

  return (
    <div>
      <Card className="[&>div]:p-0">
        <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
          Sites without Assigned User
        </h4>

        {isLoadingSites ? (
          <TableLoading columns={sitesNoAssignedUserColumns} />
        ) : (
          <LoadingWrapper isLoading={isSitesPlaceholderData}>
            <Table
              columns={sitesNoAssignedUserColumns}
              data={sitesData?.results || []}
            />

            {sitesData?.metadata && (
              <TableDataPagination metadata={sitesData.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
