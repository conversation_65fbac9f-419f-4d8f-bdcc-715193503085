"use client";

import { Card } from "flowbite-react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { studiesNoProtocolColumns } from "./columns";
import { useStudiesNoProtocol } from "./use-studies-no-protocol-queries";

export const StudiesNoProtocolTab = () => {
  const {
    data: studiesData,
    isPending: isLoadingStudies,
    isPlaceholderData: isStudiesPlaceholderData,
  } = useStudiesNoProtocol();

  return (
    <div>
      <Card className="[&>div]:p-0">
        <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
          Studies without Protocol Assignment
        </h4>

        {isLoadingStudies ? (
          <TableLoading columns={studiesNoProtocolColumns} />
        ) : (
          <LoadingWrapper isLoading={isStudiesPlaceholderData}>
            <Table
              columns={studiesNoProtocolColumns}
              data={studiesData?.results || []}
            />

            {studiesData?.metadata && (
              <TableDataPagination metadata={studiesData.metadata} />
            )}
          </LoadingWrapper>
        )}
      </Card>
    </div>
  );
};
