import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

const useStudiesNoProtocolKeys = {
  all: () => ["data-integrity", "studies-no-protocol"] as const,
  list: (params?: MetadataParams) =>
    [...useStudiesNoProtocolKeys.all(), params] as const,
};

export const TAKE_50 = 50;

export const useStudiesNoProtocol = () => {
  const { page, take } = usePagination();
  const params = {
    page,
    take,
  };
  return useQuery({
    queryKey: useStudiesNoProtocolKeys.list(params),
    queryFn: () => api.dataIntegrity.getStudiesNoProtocol(params),
    placeholderData: (prev) => prev,
  });
};
