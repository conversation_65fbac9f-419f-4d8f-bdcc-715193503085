import { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

import { TaskInactive } from "@/lib/apis/data-integrity";

export const tasksInactiveColumns: ColumnDef<TaskInactive>[] = [
  {
    accessorKey: "id",
    header: "Task ID",
    cell: ({ row }) =>
      row.original.siteId && row.original.studyId ? (
        <Link
          className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
          href={`/sites/${row.original.siteId}/studies/${row.original.studyId}?tab=tasks`}
        >
          {row.original.id}
        </Link>
      ) : (
        row.original.id
      ),
  },
  {
    accessorKey: "name",
    header: "Task Name",
  },
];
