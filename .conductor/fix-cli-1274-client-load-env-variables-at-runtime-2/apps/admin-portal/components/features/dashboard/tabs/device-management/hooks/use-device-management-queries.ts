import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { TAKE_ALL } from "@/lib/constants";

const useDeviceManagementKeys = {
  all: () => ["device-management"] as const,
  fleetOverview: () =>
    [...useDeviceManagementKeys.all(), "fleet-overview"] as const,
  fleetOverviewList: (params?: { page?: number; take?: number }) =>
    [...useDeviceManagementKeys.all(), "fleet-overview-list", params] as const,
  appVersionsBar: () =>
    [...useDeviceManagementKeys.all(), "app-versions-bar"] as const,
  scannerHealthDiagnostics: () =>
    [...useDeviceManagementKeys.all(), "scanner-health-diagnostics"] as const,
  scannerHealthDiagnosticsList: (params?: { page?: number; take?: number }) =>
    [
      ...useDeviceManagementKeys.all(),
      "scanner-health-diagnostics-list",
      params,
    ] as const,

  scannerDiagnostics: (id: string) =>
    [...useDeviceManagementKeys.all(), "scanner-diagnostics", id] as const,
};

const TAKE_50 = 50;

export const useFleetOverview = () => {
  const params = {
    take: TAKE_ALL,
  };
  return useQuery({
    queryKey: useDeviceManagementKeys.fleetOverviewList(params),
    queryFn: () => api.scanners.getFleetOverview(params),
    placeholderData: (prev) => prev,
  });
};

export const useAppVersionsBar = () => {
  return useQuery({
    queryKey: useDeviceManagementKeys.appVersionsBar(),
    queryFn: () => api.scanners.getAppVersionsBar(),
  });
};

export const useScannerHealthDiagnostics = (page: number) => {
  const params = {
    page,
    take: TAKE_50,
  };

  return useQuery({
    queryKey: useDeviceManagementKeys.scannerHealthDiagnosticsList(params),
    queryFn: () => api.scanners.list(params),
    placeholderData: (prev) => prev,
  });
};

export const useScannerDiagnostics = (id: string) => {
  return useQuery({
    queryKey: useDeviceManagementKeys.scannerDiagnostics(id),
    queryFn: () => api.scanners.getScannerDiagnostics(id),
  });
};
