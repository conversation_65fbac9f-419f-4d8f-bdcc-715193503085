"use client";

import { Card, useThemeMode } from "flowbite-react";
import { FiSmartphone } from "react-icons/fi";
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Skeleton } from "@/components/ui/skeleton";

import { useAppVersionsBar } from "./hooks/use-device-management-queries";

export const ScannerAppVersions = () => {
  const { mode } = useThemeMode();
  const { data, isPending } = useAppVersionsBar();

  const totalScanners = data?.reduce((sum, version) => sum + version.count, 0);

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Scanner Application Versions
      </h3>
      <Card className="[&>div]:p-6">
        <div className="mb-4 flex items-center justify-end">
          {isPending ? (
            <Skeleton className="h-5 w-40" />
          ) : (
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Total: {totalScanners} scanners
            </div>
          )}
        </div>

        {isPending ? (
          <Skeleton className="h-80 w-full" />
        ) : !data?.length ? (
          <div className="grid h-80 place-content-center gap-2 text-center text-gray-500 dark:text-gray-400">
            <FiSmartphone className="mx-auto h-12 w-12 text-gray-300 dark:text-gray-600" />
            <p className="text-sm">No version data available</p>
          </div>
        ) : (
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="versionNumber"
                  tick={{
                    fontSize: 14,
                  }}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                />
                <YAxis
                  tick={{
                    fontSize: 12,
                  }}
                  stroke={mode === "dark" ? "#fff" : "#000"}
                  allowDecimals={false}
                />
                <Tooltip
                  cursor={false}
                  formatter={(value, name) => [
                    `${value} ${+value === 1 ? "scanner" : "scanners"}`,
                    "Count",
                  ]}
                  labelFormatter={(label) => `Version: ${label}`}
                />
                <Bar dataKey="count" maxBarSize={60} fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        )}
      </Card>
    </div>
  );
};
