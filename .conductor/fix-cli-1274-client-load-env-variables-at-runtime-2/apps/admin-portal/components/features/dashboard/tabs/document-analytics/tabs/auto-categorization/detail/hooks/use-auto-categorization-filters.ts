import { endOfDay, startOfDay } from "date-fns";
import { parseAsString, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSort } from "@/hooks/use-sort";

export const useAutoCategorizationFilters = () => {
  const [siteId, setSiteId] = useQueryState("siteId", parseAsString);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [status, setStatus] = useQueryState("status", parseAsString);
  const [method, setMethod] = useQueryState("method", parseAsString);
  const [fromDate, setFromDate] = useQueryState("fromDate", parseAsString);
  const [toDate, setToDate] = useQueryState("toDate", parseAsString);

  const { page, take, goToPage } = usePagination();
  const { orderBy, orderDirection } = useSort();

  const params = {
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    page,
    take,
    filter: {
      siteId,
      studyId,
      status,
      method,
      fromDate: fromDate ? startOfDay(new Date(fromDate)) : undefined,
      toDate: toDate ? endOfDay(new Date(toDate)) : undefined,
    },
  };

  return {
    siteId,
    studyId,
    status,
    method,
    fromDate,
    toDate,
    page,
    take,
    setSiteId,
    setStudyId,
    setStatus,
    setMethod,
    setFromDate,
    setToDate,
    goToPage,
    params,
  };
};
