"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { AgingDocumentItem } from "@/lib/apis/document-analytics";
import { formatDate } from "@/lib/utils";
import { capitalize } from "@/utils/string";

import { useAgingDocuments } from "../hooks/use-document-analytics-queries";

const AGING_DOCUMENT_TYPES = {
  pendingReviewOver7Days: "Pending Review (Over 7 Days)",
  inReviewOver14Days: "In Review (Over 14 Days)",
  expiringSoon: "Expiring Soon",
} as const;

const columns: ColumnDef<AgingDocumentItem>[] = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "sourceType",
    header: "Source Type",
    cell: ({ row }) => capitalize(row.original.type),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => capitalize(row.original.status),
  },
  {
    accessorKey: "type",
    header: "Type",
    cell: ({ row }) => AGING_DOCUMENT_TYPES[row.original.type] ?? "-",
  },
  {
    accessorKey: "createdDate",
    header: "Created Date",
    cell: ({ row }) => formatDate(row.original.createdDate, "LLL dd, yyyy"),
  },
];

export const AgingDocumentsTable = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const { data, isPending, isPlaceholderData } = useAgingDocuments(currentPage);

  return (
    <Card className="[&>div]:p-0">
      <h3 className="p-4 text-lg font-semibold text-gray-900 dark:text-white">
        Aging Documents
      </h3>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isPlaceholderData}>
          <Table columns={columns} data={data?.results ?? []} />
          {data?.metadata && (
            <TableDataPagination
              metadata={data?.metadata}
              page={currentPage}
              setPage={setCurrentPage}
            />
          )}
        </LoadingWrapper>
      )}
    </Card>
  );
};
