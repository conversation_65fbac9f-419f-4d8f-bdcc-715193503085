"use client";

import { ListFilter } from "lucide-react";
import { useRef, useState } from "react";
import { z } from "zod";

import { useInfiniteSponsors } from "@/components/features/sponsors/hooks/use-sponsors";
import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { DateRangePicker, Form, FormRef } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteStudiesBySponsor } from "@/hooks/queries/use-infinite-studies";
import { cn } from "@/lib/utils";

import { useHighLevelSummaryFilters } from "../hooks/use-document-analytics-filters";

const schema = z.object({
  sponsorId: z.string().optional(),
  studyId: z.string().optional(),
  dateRange: z
    .object({
      from: z.date().optional().nullable(),
      to: z.date().optional().nullable(),
    })
    .optional()
    .nullable(),
});

export const HighLevelSummaryFilter = () => {
  const [open, setOpen] = useState(false);
  const {
    sponsorId,
    studyId,
    fromDate,
    toDate,
    setSponsorId,
    setStudyId,
    setFromDate,
    setToDate,
  } = useHighLevelSummaryFilters();

  const clearFilters = () => {
    setSponsorId(null);
    setStudyId(null);
    setFromDate(null);
    setToDate(null);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (sponsorId) count++;
    if (studyId) count++;
    if (fromDate || toDate) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button
          variant="outline"
          className={cn(
            "relative  transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 dark:hover:border-blue-600 dark:hover:bg-blue-900/20",
            activeFilterCount > 0 &&
              "border-blue-500 bg-blue-50 text-blue-700 dark:border-blue-400 dark:bg-blue-900/30 dark:text-blue-300",
          )}
        >
          <ListFilter className="size-4" />
          {activeFilterCount > 0 && (
            <span className="absolute right-1 top-1 grid h-5 w-5 place-items-center rounded-full bg-blue-500 text-xs font-bold text-white dark:bg-blue-400 dark:text-blue-900">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-10 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent
          setOpen={setOpen}
          sponsorId={sponsorId}
          studyId={studyId}
          fromDate={fromDate}
          toDate={toDate}
          setSponsorId={setSponsorId}
          setStudyId={setStudyId}
          setFromDate={setFromDate}
          setToDate={setToDate}
          clearFilters={clearFilters}
        />
      </DropdownContent>
    </Dropdown>
  );
};

type FilterContentProps = {
  setOpen: (open: boolean) => void;
  sponsorId: string | null;
  studyId: string | null;
  fromDate: string | null;
  toDate: string | null;
  setSponsorId: (value: string | null) => void;
  setStudyId: (value: string | null) => void;
  setFromDate: (value: string | null) => void;
  setToDate: (value: string | null) => void;
  clearFilters: () => void;
};

const FilterContent = ({
  setOpen,
  sponsorId,
  studyId,
  fromDate,
  toDate,
  setSponsorId,
  setStudyId,
  setFromDate,
  setToDate,
  clearFilters,
}: FilterContentProps) => {
  const formRef = useRef<FormRef<typeof schema>>(null);
  const [selectedSponsorId, setSelectedSponsorId] = useState(sponsorId || "");

  function onSubmit(data: z.infer<typeof schema>) {
    setSponsorId(data.sponsorId || null);
    setStudyId(data.studyId || null);
    setFromDate(
      data.dateRange?.from ? data.dateRange.from.toISOString() : null,
    );
    setToDate(data.dateRange?.to ? data.dateRange.to.toISOString() : null);
    setOpen(false);
  }

  function onClear() {
    clearFilters();
    setSelectedSponsorId("");
    formRef.current?.formHandler?.reset({
      dateRange: null,
      sponsorId: "",
      studyId: "",
    });
  }

  const dateRange =
    fromDate || toDate
      ? {
          from: fromDate ? new Date(fromDate) : undefined,
          to: toDate ? new Date(toDate) : undefined,
        }
      : undefined;

  const defaultValues = {
    sponsorId: sponsorId ?? "",
    studyId: studyId ?? "",
    dateRange,
  };

  const hasFilters = !!sponsorId || !!studyId || !!fromDate || !!toDate;

  return (
    <Form
      schema={schema}
      mode="onChange"
      onSubmit={onSubmit}
      className="w-96"
      ref={formRef}
      defaultValues={defaultValues}
    >
      <div className="flex flex-col divide-y">
        <div className="px-[15px] pb-5 pt-2.5">
          <Label
            htmlFor="sponsorId"
            className="mb-2 block text-base font-normal leading-6 text-gray-700"
          >
            Sponsor
          </Label>
          <LazySelect
            name="sponsorId"
            useInfiniteQuery={useInfiniteSponsors}
            getOptionLabel={(sponsor) => sponsor.name}
            getOptionValue={(sponsor) => sponsor.id}
            placeholder="Select Sponsor"
            searchPlaceholder="Search sponsors..."
            onSelect={(sponsorId) => setSelectedSponsorId(sponsorId)}
          />
        </div>

        <div className="px-[15px] pb-5 pt-2.5">
          <Label
            htmlFor="studyId"
            className="mb-2 block text-base font-normal leading-6 text-gray-700"
          >
            Study
          </Label>
          <LazySelect
            name="studyId"
            useInfiniteQuery={useInfiniteStudiesBySponsor}
            params={[selectedSponsorId]}
            getOptionLabel={(study) => study.name}
            getOptionValue={(study) => study.id}
            placeholder="Select Study"
            searchPlaceholder="Search studies..."
            dependentFieldNames={["sponsorId"]}
          />
        </div>

        <div className="px-[15px] pb-5 pt-2.5">
          <Label
            htmlFor="dateRange"
            className="mb-2 block text-base font-normal leading-6 text-gray-700"
          >
            Date Range
          </Label>
          <DateRangePicker
            name="dateRange"
            placeholder="Select Date Range"
            placement="bottom-end"
            maxDate={new Date()}
          />
        </div>
      </div>

      <div className="flex justify-end gap-5 px-[15px] pb-5 pt-2.5">
        <Button
          variant="outline"
          className={cn(
            "w-full justify-center",
            !hasFilters && "pointer-events-none invisible",
          )}
          onClick={() => {
            onClear();
          }}
          type="button"
        >
          Clear Filters
        </Button>
        <Button
          variant="primary"
          type="submit"
          className="w-full justify-center"
        >
          Apply Filters
        </Button>
      </div>
    </Form>
  );
};
