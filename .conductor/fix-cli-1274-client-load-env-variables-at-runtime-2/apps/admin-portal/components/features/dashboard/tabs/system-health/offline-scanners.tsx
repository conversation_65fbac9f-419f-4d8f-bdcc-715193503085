"use client";

import { Card } from "flowbite-react";
import { useState } from "react";
import { FiMonitor } from "react-icons/fi";

import { UncontrolledSelect } from "@/components/ui/form/select/uncontrolled-select";
import { Skeleton } from "@/components/ui/skeleton";

import { useOfflineScanners } from "./hooks/use-system-health-queries";

const THRESHOLD_OPTIONS = [
  { value: "1", label: "1 hour" },
  { value: "6", label: "6 hours" },
  { value: "12", label: "12 hours" },
  { value: "24", label: "24 hours" },
  { value: "48", label: "48 hours" },
  { value: "72", label: "72 hours" },
];

type Props = {
  onNavigateToDevices: () => void;
};

export const OfflineScanners = ({ onNavigateToDevices }: Props) => {
  const [thresholdValue, setThresholdValue] = useState("24");
  const threshold = Number(thresholdValue) || 24;
  const { data, isPending } = useOfflineScanners(threshold);

  if (isPending) {
    return <OfflineScannersSkeleton />;
  }

  return (
    <>
      <Card>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div
              className={`flex h-12 w-12 items-center justify-center rounded-full ${data?.total === 0 ? "bg-green-100 dark:bg-green-800" : "bg-yellow-100 dark:bg-yellow-800"}`}
            >
              <FiMonitor
                className={`h-6 w-6 ${data?.total === 0 ? "text-green-600 dark:text-green-400" : "text-yellow-600 dark:text-yellow-400"}`}
              />
            </div>

            <div>
              <button
                className="hover:text-primary-500 dark:hover:text-primary-500 text-lg font-semibold text-gray-900 hover:underline dark:text-white"
                onClick={() => {
                  onNavigateToDevices();
                }}
              >
                Offline Scanners
              </button>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {data?.total === 0
                  ? "All scanners online"
                  : `${data?.total} scanner${data?.total === 1 ? "" : "s"} offline in last ${threshold}h`}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div
              className={`text-3xl font-bold ${data?.total === 0 ? "text-green-600 dark:text-green-400" : "text-yellow-600 dark:text-yellow-400"}`}
            >
              {data?.total ?? 0}
            </div>

            <UncontrolledSelect
              value={thresholdValue}
              onChange={(value) => setThresholdValue(value || "")}
              options={THRESHOLD_OPTIONS}
              placeholder="Select threshold"
              className="border-none bg-transparent p-0 text-xs text-gray-600 dark:text-gray-400"
            />
          </div>
        </div>
      </Card>
    </>
  );
};

export const OfflineScannersSkeleton = () => {
  return (
    <Card>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32" />
            <Skeleton className="mt-1 h-4 w-40" />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-12" />
          <Skeleton className="h-6 w-20" />
        </div>
      </div>
    </Card>
  );
};
