"use client";

import { Card } from "flowbite-react";
import { useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";

import { userColumns, usersNeverLoginColum } from "./columns";
import {
  useProfilesWithoutRole,
  useUsersNeverLogin,
  useUsersWithoutProfile,
} from "./hooks/use-user-management-queries";

export const UsersWithoutKeyInfo = () => {
  const [usersWithoutProfilePage, setUsersWithoutProfilePage] = useState(1);
  const [profilesWithoutRolePage, setProfilesWithoutRolePage] = useState(1);
  const [usersNeverLoginPage, setUsersNeverLoginPage] = useState(1);

  const {
    data: usersWithoutProfileData,
    isPending: isLoadingUsersWithoutProfile,
    isPlaceholderData: isUsersWithoutProfilePlaceholderData,
  } = useUsersWithoutProfile(usersWithoutProfilePage);

  const {
    data: profilesWithoutRoleData,
    isPending: isLoadingProfilesWithoutRole,
    isPlaceholderData: isProfilesWithoutRolePlaceholderData,
  } = useProfilesWithoutRole(profilesWithoutRolePage);

  const {
    data: usersNeverLoginData,
    isPending: isLoadingUsersNeverLogin,
    isPlaceholderData: isUsersNeverLoginPlaceholderData,
  } = useUsersNeverLogin(usersNeverLoginPage);

  return (
    <div>
      <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
        Users Without Key Information
      </h3>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="[&>div]:p-0">
          <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
            Users without a Profile
          </h4>

          {isLoadingUsersWithoutProfile ? (
            <TableLoading columns={userColumns} />
          ) : (
            <LoadingWrapper isLoading={isUsersWithoutProfilePlaceholderData}>
              <Table
                columns={userColumns}
                data={usersWithoutProfileData?.results || []}
              />

              {usersWithoutProfileData?.metadata && (
                <TableDataPagination
                  metadata={usersWithoutProfileData.metadata}
                  page={usersWithoutProfilePage}
                  setPage={setUsersWithoutProfilePage}
                />
              )}
            </LoadingWrapper>
          )}
        </Card>

        <Card className="[&>div]:p-0">
          <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
            Profiles without a Role
          </h4>

          {isLoadingProfilesWithoutRole ? (
            <TableLoading columns={userColumns} />
          ) : (
            <LoadingWrapper isLoading={isProfilesWithoutRolePlaceholderData}>
              <Table
                columns={userColumns}
                data={profilesWithoutRoleData?.results || []}
              />

              {profilesWithoutRoleData?.metadata && (
                <TableDataPagination
                  metadata={profilesWithoutRoleData.metadata}
                  page={profilesWithoutRolePage}
                  setPage={setProfilesWithoutRolePage}
                />
              )}
            </LoadingWrapper>
          )}
        </Card>
      </div>

      <div className="mt-6">
        <Card className="[&>div]:p-0">
          <h4 className="p-4 text-base font-medium text-gray-900 dark:text-white">
            Users who Never Logged In
          </h4>

          {isLoadingUsersNeverLogin ? (
            <TableLoading columns={usersNeverLoginColum} />
          ) : (
            <LoadingWrapper isLoading={isUsersNeverLoginPlaceholderData}>
              <Table
                columns={usersNeverLoginColum}
                data={usersNeverLoginData?.results || []}
              />

              {usersNeverLoginData?.metadata && (
                <TableDataPagination
                  metadata={usersNeverLoginData.metadata}
                  page={usersNeverLoginPage}
                  setPage={setUsersNeverLoginPage}
                />
              )}
            </LoadingWrapper>
          )}
        </Card>
      </div>
    </div>
  );
};
