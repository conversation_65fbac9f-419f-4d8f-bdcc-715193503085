import { File } from "@/lib/apis/isf-templates";

type Props = {
  document: File;
};

export const FileOverlay = ({ document }: Props) => {
  return (
    <div className="flex w-fit max-w-[360px] cursor-grabbing items-center gap-2 rounded-lg border border-purple-500 bg-white px-4 sm:max-w-fit md:gap-4">
      <div className="flex min-w-0 flex-1 items-center gap-4 rounded-lg py-2 md:gap-10">
        <span className="min-w-0 flex-1 select-none truncate whitespace-nowrap text-sm font-medium leading-5">
          {document.title}
        </span>
        {/* <span className="hidden whitespace-nowrap md:block">
          {document.category?.artifactNumber || (
            <span className="text-gray-400">N/A</span>
          )}
        </span>
        <DocumentStatusBadge
          className="min-w-0"
          status={status as DocumentStatus}
        /> */}
      </div>
    </div>
  );
};
