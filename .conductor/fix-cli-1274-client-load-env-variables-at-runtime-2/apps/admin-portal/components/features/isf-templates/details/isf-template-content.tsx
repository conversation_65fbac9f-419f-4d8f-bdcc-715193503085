"use client";
import { useParams } from "next/navigation";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { IsfTemplate } from "@/lib/apis/isf-templates";
import { formatDate } from "@/lib/utils";

import { useISFTemplate } from "../hooks/use-isf-template-queries";
import { TemplateModal } from "../template-modal";
import { ISFTemplateDetail } from "./isf-template-detail";

export const ISFTemplateContent = () => {
  const { id } = useParams();
  const [selectedTemplate, setSelectedTemplate] = useState<IsfTemplate | null>(
    null,
  );
  const { data, isPending } = useISFTemplate(id as string);

  const breadcrumbItems = [
    { label: "ISF Templates", href: "/isf-templates" },
    { label: data?.name || "ISF Template Detail", loading: isPending },
  ];

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <PageHeader showBackButton href="/isf-templates">
          {isPending ? (
            <Skeleton className="h-7 w-40" />
          ) : (
            data?.name || "ISF Template Detail"
          )}
        </PageHeader>
        <OverviewCard
          title="Overview"
          rightContent={
            <Button
              onClick={() => {
                setSelectedTemplate(data ? data : null);
              }}
              disabled={isPending}
              variant="primary"
              className="px-2 sm:px-4"
            >
              <CiEdit />
              Edit Template
            </Button>
          }
        >
          {isPending ? (
            <OverViewSkeleton />
          ) : (
            <div className="grid grid-cols-2 gap-x-2 gap-y-4 sm:grid-cols-3">
              <OverviewItem label="Name" value={data?.name} />
              <OverviewItem label="Status">
                <ActiveStatusBadge isActive={data?.isActive ?? false} />
              </OverviewItem>
              <OverviewItem
                label="Created At"
                value={
                  data?.createdDate
                    ? formatDate(data?.createdDate, "LLL dd, yyyy")
                    : "N/A"
                }
              />
            </div>
          )}
        </OverviewCard>
      </div>
      <ISFTemplateDetail />

      {selectedTemplate && (
        <TemplateModal
          isOpen={!!selectedTemplate}
          onClose={() => setSelectedTemplate(null)}
          template={selectedTemplate}
        />
      )}
    </>
  );
};

const OverViewSkeleton = () => {
  return (
    <div className="grid grid-cols-2 gap-x-2 gap-y-4 sm:grid-cols-3">
      <div className=" space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-28" />
      </div>

      <div className=" space-y-1">
        <Skeleton className="h-6 w-14" />
        <Skeleton className="h-5 w-14" />
      </div>
      <div className=" space-y-1">
        <Skeleton className="h-6 w-20" />
        <Skeleton className="h-5 w-24" />
      </div>
    </div>
  );
};
