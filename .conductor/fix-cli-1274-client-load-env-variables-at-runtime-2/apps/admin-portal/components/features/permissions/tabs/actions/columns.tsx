import { type ColumnDef } from "@tanstack/react-table";
import { Tooltip } from "flowbite-react";
import { MdAdminPanelSettings, MdPerson } from "react-icons/md";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { Permission } from "@/lib/apis/roles";
import { capitalize } from "@/utils/string";

export const generatePermissionActionColumns = ({
  onView,
}: {
  onView: (data: Permission) => void;
}): ColumnDef<Permission>[] => [
  {
    accessorKey: "permissionSubject",
    header: "Permission Subject",
    cell: ({ row }) => {
      const data = row.original;
      return capitalize(data.permissionSubject.name);
    },
  },
  {
    accessorKey: "action",
    header: "Action",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      const data = row.original;
      return <ActiveStatusBadge isActive={data.isActive} />;
    },
  },
  {
    accessorKey: "isAdmin",
    header: "Admin",
    cell: ({ row }) => {
      const data = row.original;
      return data.isAdmin ? (
        <Tooltip content="Admin">
          <MdAdminPanelSettings size={24} className="text-blue-600" />
        </Tooltip>
      ) : (
        <Tooltip content="User">
          <MdPerson size={24} className="text-gray-500" />
        </Tooltip>
      );
    },
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return <TableViewButton type="button" onClick={() => onView(data)} />;
    },
  },
];
