import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { UpdatePermissionPayload } from "@/lib/apis/roles";

import { permissionActionKeys } from "./use-permission-actions-queries";

export const useUpdatePermissionAction = () => {
  return useMutation({
    mutationFn: (payload: UpdatePermissionPayload) =>
      api.roles.updatePermission(payload),
    onError: (err) => toast.error(err?.message || "Fail to update permission"),
    onSettled: (_, err) =>
      !err && toast.success("Update permission successfully"),
    meta: {
      awaits: permissionActionKeys.allList(),
    },
  });
};
