import { type ColumnDef } from "@tanstack/react-table";

import { TableViewButton } from "@/components/shared/table-action-buttons";
import { RoleSubject } from "@/lib/apis/roles";
import { capitalize } from "@/utils/string";

export const generatePermissionSubjectColumns = ({
  onView,
}: {
  onView: (data: RoleSubject) => void;
}): ColumnDef<RoleSubject>[] => [
  {
    header: "Name",
    cell: ({ row }) => capitalize(row.original.name),
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const data = row.original;
      return <TableViewButton type="button" onClick={() => onView(data)} />;
    },
  },
];
