import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { UpdatePermissionSubjectPayload } from "@/lib/apis/roles";

import { permissionSubjectKeys } from "./use-permission-subject-queries";

export const useUpdatePermissionSubjects = () => {
  return useMutation({
    mutationFn: (payload: UpdatePermissionSubjectPayload) =>
      api.roles.updateSubjects(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update permission subject"),
    onSettled: (_, err) =>
      !err && toast.success("Update permission subject successfully"),
    meta: {
      awaits: permissionSubjectKeys.allList(),
    },
  });
};
