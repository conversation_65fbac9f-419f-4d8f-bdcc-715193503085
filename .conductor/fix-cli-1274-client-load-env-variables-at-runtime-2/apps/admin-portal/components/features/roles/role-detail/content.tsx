"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { ActiveStatusBadge } from "@/components/ui/badges";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { RoleModal } from "../default/role-modal";
import { useRole } from "../hooks/use-roles-queries";
import { AdminRoleTab } from "./tabs/admin-roles";
import { RoleTab } from "./tabs/roles";

const ROLE_TABS = [
  {
    key: "roles",
    title: "Roles",
    content: <RoleTab />,
  },
  {
    key: "admin-roles",
    title: "Admin Roles",
    content: <AdminRoleTab />,
  },
];

export const RoleDetailContent = () => {
  const roleId = useParams().id as string;
  const [isOpen, setIsOpen] = useState(false);

  const { data: role, isPending: isRolePending } = useRole(roleId);

  const breadcrumbItems = [
    { label: "Roles", href: "/roles" },
    { label: role?.name ?? "Role Detail", loading: isRolePending },
  ];

  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex items-center justify-between gap-x-2">
          <PageHeader showBackButton href="/roles?active=true">
            {isRolePending ? (
              <Skeleton className="h-7 w-32" />
            ) : (
              role?.name || "Role Detail"
            )}
          </PageHeader>
          <Button
            className="flex-shrink-0"
            variant="primary"
            disabled={isRolePending}
            onClick={() => setIsOpen(true)}
          >
            <CiEdit />
            Edit role
          </Button>
        </div>

        {isRolePending ? (
          <UserContentSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="space-y-2">
              <div className="grid grid-cols-2 ">
                <OverviewItem
                  label="Type"
                  className="capitalize"
                  value={role?.type || "N/A"}
                />
                <OverviewItem label="Status">
                  <ActiveStatusBadge
                    isActive={
                      typeof role?.isActive === "boolean"
                        ? role?.isActive
                        : false
                    }
                  />
                </OverviewItem>
              </div>
              <OverviewItem
                label="Description"
                value={role?.description || "N/A"}
              />
            </div>
          </OverviewCard>
        )}
        <TabsWrapper tabs={ROLE_TABS} />
      </div>
      <RoleModal
        selectedRole={role}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};

const UserContentSkeleton = () => {
  return (
    <div className="flex flex-col shadow-md">
      <div className="rounded-lg  bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-7 w-24" />
        <div className="space-y-2">
          <div className="grid grid-cols-2 ">
            <div className="flex flex-col gap-2">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-5 w-16" />
            </div>
            <div className="flex flex-col gap-2">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-5 w-24" />
            </div>
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-6 w-28" />
            <Skeleton className="h-5 w-80" />
          </div>
        </div>
      </div>
    </div>
  );
};
