import { ColumnDef } from "@tanstack/react-table";
import { Checkbox, Tooltip } from "flowbite-react";

import { FormattedRoleData, Permission } from "@/lib/apis/roles";
import { PERMISSION } from "@/lib/constants";

const HEADERS = ["List", "Create", "Read", "Update", "Menu", "Delete"] as const;

const getTooltipContentForNormalAction = (permission?: Permission) => {
  if (permission?.isAdmin) return "Admin Only";
  if (!permission?.isActive) return "Disabled";
  return permission?.description || "No description";
};

export const generateNormalPermissionColumns = (
  onClick: (params: {
    isAdd: boolean;
    id: string;
    newPermission: Permission;
  }) => void,
): ColumnDef<FormattedRoleData>[] => {
  const actionColumns = HEADERS.map(
    (header) =>
      ({
        header,
        cell: ({ row }) => {
          const data = row.original;

          const validPermission = data.allPermissions.find((per) =>
            per.action.startsWith(
              PERMISSION[header.toLocaleLowerCase() as keyof typeof PERMISSION],
            ),
          );

          const hasPermission = data.selectedPermission.some(
            (per) => per.id === validPermission?.id,
          );

          return (
            <Tooltip
              content={getTooltipContentForNormalAction(validPermission)}
            >
              <Checkbox
                disabled={
                  !validPermission ||
                  !validPermission.isActive ||
                  validPermission.isAdmin
                }
                checked={hasPermission}
                onChange={(e) => {
                  if (!validPermission) return;
                  onClick({
                    id: validPermission?.id as string,
                    isAdd: e.target.checked,
                    newPermission: validPermission,
                  });
                }}
                className="size-6  cursor-pointer disabled:pointer-events-none disabled:border-red-500 disabled:opacity-50 dark:disabled:border-red-500"
              />
            </Tooltip>
          );
        },
      }) as ColumnDef<FormattedRoleData>,
  );

  return [
    {
      header: "Permission Subject",
      accessorKey: "name",
      cell: ({ row }) => {
        const permissionSubject = row.original;
        return (
          <Tooltip content={permissionSubject.description || "No description"}>
            <span className="capitalize">{permissionSubject.name}</span>
          </Tooltip>
        );
      },
    },
    ...actionColumns,
  ];
};

const getTooltipContentForAdminAction = (permission?: Permission) => {
  if (!permission?.isActive) return "Disabled";
  return permission?.description || "No description";
};

export const generateAdminPermissionColumns = (
  onClick: (params: {
    isAdd: boolean;
    id: string;
    newPermission: Permission;
  }) => void,
): ColumnDef<FormattedRoleData>[] => {
  const actionColumns = HEADERS.map(
    (header) =>
      ({
        header,
        cell: ({ row }) => {
          const data = row.original;

          const validPermission = data.allPermissions.find((per) =>
            per.action.startsWith(
              PERMISSION[header.toLocaleLowerCase() as keyof typeof PERMISSION],
            ),
          );

          const hasPermission = data.selectedPermission.some(
            (per) => per.id === validPermission?.id,
          );

          return (
            <Tooltip content={getTooltipContentForAdminAction(validPermission)}>
              <Checkbox
                disabled={!validPermission || !validPermission.isActive}
                checked={hasPermission}
                onChange={(e) => {
                  if (!validPermission) return;
                  onClick({
                    id: validPermission?.id as string,
                    isAdd: e.target.checked,
                    newPermission: validPermission,
                  });
                }}
                className="size-6 cursor-pointer disabled:pointer-events-none disabled:border-red-500 disabled:opacity-50 dark:disabled:border-red-500"
              />
            </Tooltip>
          );
        },
      }) as ColumnDef<FormattedRoleData>,
  );

  return [
    {
      header: "Permission Subject",
      accessorKey: "name",
      cell: ({ row }) => {
        const permissionSubject = row.original;
        return (
          <Tooltip content={permissionSubject.description || "No description"}>
            <span className="capitalize">{permissionSubject.name}</span>
          </Tooltip>
        );
      },
    },
    ...actionColumns,
  ];
};
