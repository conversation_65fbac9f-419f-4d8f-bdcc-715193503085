import { type ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import type { ScannerApplication } from "@/lib/apis/scanner-applications";

export const getColumns = (
  onEdit: (application: ScannerApplication) => void,
  onDownload: (application: ScannerApplication) => void,
): ColumnDef<ScannerApplication>[] => [
  {
    accessorKey: "versionNumber",
    header: "Version Number",
    cell: ({ row }) => (
      <button
        onClick={() => onEdit(row.original)}
        className="text-primary-500 hover:underline"
      >
        {row.getValue("versionNumber")}
      </button>
    ),
  },
  {
    accessorKey: "apkLink",
    header: "APK Link",
    cell: ({ row }) => (
      <button
        onClick={() => onDownload(row.original)}
        className="text-primary-500 hover:underline"
      >
        Link
      </button>
    ),
  },
  {
    accessorKey: "releaseNotes",
    header: "Release Notes",
    cell: ({ row }) => (
      <div className="text-gray-600">{row.getValue("releaseNotes")}</div>
    ),
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const application = row.original;

      return (
        <div
          className="text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
          onClick={() => onEdit(application)}
        >
          <span className="whitespace-nowrap">Edit</span>
          <MdOutlineEdit />
        </div>
      );
    },
  },
];
