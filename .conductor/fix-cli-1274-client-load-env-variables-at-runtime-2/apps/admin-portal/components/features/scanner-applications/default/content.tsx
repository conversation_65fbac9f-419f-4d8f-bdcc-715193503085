"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table } from "@/components/ui/table";
import type { ScannerApplication } from "@/lib/apis/scanner-applications";

import { ModalScannerApplication } from "../modal-scanner-applications";
import { getColumns } from "./columns";
import { useScannerApplications } from "./hooks/use-scanner-applications";
import { useDownloadApk } from "./hooks/use-upload-url";

const BREADCRUMB_ITEMS = [{ label: "Scanner Applications" }];

const ScannerApplicationPageContent = () => {
  const { data, isLoading } = useScannerApplications();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedApplication, setSelectedApplication] =
    useState<ScannerApplication | null>(null);
  const { mutateAsync: downloadApk, isPending: isDownloading } =
    useDownloadApk();
  const handleViewApplication = (application: ScannerApplication) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  const handleDownloadApplication = async (application: ScannerApplication) => {
    const res = await downloadApk(application.id);
    window.open(res.url, "_blank");
  };

  const columns = useMemo(() => {
    return getColumns(handleViewApplication, handleDownloadApplication);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Prepare CSV export data
  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Version Number", key: "versionNumber" },
      { label: "APK Link", key: "apkLink" },
      { label: "Release Notes", key: "releaseNotes" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((application) => [
        application.id,
        application.versionNumber,
        application.apkLink,
        application.releaseNotes,
      ]),
    ];
  }, [data]);

  const handleAddApplication = () => {
    setSelectedApplication(null);
    setIsModalOpen(true);
  };

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Scanner Applications</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Scanner Application"
            filename="scanner-applications.csv"
            onButtonClick={handleAddApplication}
          />

          <LoadingWrapper isLoading={isDownloading}>
            <Table
              data={
                isLoading
                  ? Array(10).fill({} as ScannerApplication)
                  : data?.results || []
              }
              columns={columns}
              headCellStyle="text-gray-500 p-4"
              isLoading={isLoading}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        </Card>
      </div>

      {isModalOpen && (
        <ModalScannerApplication
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          application={selectedApplication}
        />
      )}
    </>
  );
};

export default ScannerApplicationPageContent;
