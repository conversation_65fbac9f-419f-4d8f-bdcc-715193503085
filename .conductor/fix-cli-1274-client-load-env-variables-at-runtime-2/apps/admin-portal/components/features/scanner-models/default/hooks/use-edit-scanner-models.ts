import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateScannerModelPayload } from "@/lib/apis/scanner-models";

import { USE_SCANNER_MODEL_QUERY_KEY } from "./use-scanner-model";
import { USE_SCANNER_MODELS_QUERY_KEY } from "./use-scanner-models";

export const useEditScannerModel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UpdateScannerModelPayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.scannerModels.update(id, payload);
    },
    onSuccess: (_, { id }) => {
      toast.success("Scanner Model edited successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_SCANNER_MODEL_QUERY_KEY, id],
      });
      queryClient.invalidateQueries({
        queryKey: [USE_SCANNER_MODELS_QUERY_KEY],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
