import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const USE_SCANNER_MODELS_QUERY_KEY = "scannerModels";

export const useScannerModels = (params?: MetadataParams) => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  return useQuery({
    queryKey: [USE_SCANNER_MODELS_QUERY_KEY, page, take, search],
    queryFn: () =>
      api.scannerModels.list({
        page,
        take,
        ...params,
        filter: { modelName: search },
      }),
    placeholderData: (prevData) => prevData,
  });
};
