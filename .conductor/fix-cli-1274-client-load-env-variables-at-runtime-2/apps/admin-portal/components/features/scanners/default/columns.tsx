import { type ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { IoMdEye } from "react-icons/io";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { <PERSON>anner } from "@/lib/apis/scanners";

export const columns: ColumnDef<Scanner>[] = [
  {
    accessorKey: "displayName",
    header: "Display Name",
    cell: ({ row }) => (
      <Link
        href={`/scanners/${row.original.id}`}
        className="text-primary-500 font-bold hover:underline dark:font-medium"
      >
        {row.getValue("displayName")}
      </Link>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="text-gray-600">{row.getValue("description")}</div>
    ),
  },
  {
    accessorKey: "currentVersion.versionNumber",
    header: "Current Version",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      if (row.original.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const scanner = row.original;

      return (
        <div className="flex gap-4 text-xs text-blue-500">
          <Link
            href={`/scanners/${scanner.id}`}
            className="text-destructive flex items-center gap-1"
          >
            <span>View</span>
            <IoMdEye />
          </Link>
        </div>
      );
    },
  },
];
