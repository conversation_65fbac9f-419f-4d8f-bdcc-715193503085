import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateScannerPayload } from "@/lib/apis/scanners";

import { USE_SCANNER_QUERY_KEY } from "./use-scanner";

export const useEditScanner = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (payload: UpdateScannerPayload & { id: string }) => {
      const { id, ...data } = payload;
      return api.scanners.update(id, data);
    },
    onSuccess: (_, { id }) => {
      toast.success("Site updated successfully");
      queryClient.invalidateQueries({ queryKey: [USE_SCANNER_QUERY_KEY, id] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
