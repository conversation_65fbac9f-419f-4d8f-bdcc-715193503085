import type { ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import type { ScannerModel } from "@/lib/apis/scanner-models";

export const generateScannerModelColumns = (
  onEditModel: (model: ScannerModel) => void,
): ColumnDef<ScannerModel>[] => {
  return [
    {
      header: "Model Name",
      accessorKey: "modelName",
    },
    {
      header: "Company",
      accessorKey: "companyName",
    },
    {
      header: "Description",
      accessorKey: "description",
    },
    {
      header: "Actions",
      accessorKey: "id",
      cell: ({ row }) => {
        return (
          <div
            className="leading-4.5 text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
            onClick={() => onEditModel(row.original)}
          >
            <span className="whitespace-nowrap">Edit</span>
            <MdOutlineEdit />
          </div>
        );
      },
    },
  ];
};
