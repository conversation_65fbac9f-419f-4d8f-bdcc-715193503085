import { Card } from "flowbite-react";
import { useState } from "react";

import { useScannerApplication } from "@/components/features/scanner-applications/default/hooks/use-scanner-application";
import { ModalScannerApplication } from "@/components/features/scanner-applications/modal-scanner-applications";
import { Table, TableLoading } from "@/components/ui/table";
import type { ScannerApplication } from "@/lib/apis/scanner-applications/types";

import { generateScannerApplicationColumns } from "../columns/scanner-applications";

type ScannerApplicationsTabProps = {
  currentVersionId: string | undefined;
  targetVersionId: string | undefined;
};

export const ScannerApplicationsTab = ({
  currentVersionId,
  targetVersionId,
}: ScannerApplicationsTabProps) => {
  const { data: currentVersion, isLoading: isLoadingCurrent } =
    useScannerApplication(currentVersionId);
  const { data: targetVersion, isLoading: isLoadingTarget } =
    useScannerApplication(targetVersionId);

  const [selectedApplication, setSelectedApplication] =
    useState<ScannerApplication | null>(null);

  const columns = generateScannerApplicationColumns(
    (application: ScannerApplication) => {
      setSelectedApplication(application);
    },
  );

  const handleModalClose = () => {
    setSelectedApplication(null);
  };

  return (
    <>
      <Card className="mb-6 [&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Current Version</div>
        </div>
        {isLoadingCurrent ? (
          <TableLoading columns={columns} />
        ) : (
          <Table
            columns={columns}
            data={currentVersion ? [currentVersion] : []}
          />
        )}
      </Card>

      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Target Version</div>
        </div>
        {isLoadingTarget ? (
          <TableLoading columns={columns} />
        ) : (
          <Table
            columns={columns}
            data={targetVersion ? [targetVersion] : []}
          />
        )}
      </Card>

      {selectedApplication && (
        <ModalScannerApplication
          isOpen={!!selectedApplication}
          onClose={handleModalClose} // Updated to use new handler
          application={selectedApplication}
        />
      )}
    </>
  );
};
