import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { AddPromptPayload, UpdatePromptPayload } from "@/lib/apis/app-prompts";

import { promptTemplateKeys } from "./use-protocol-prompt-query";

export const useAddProtocolPrompt = () => {
  return useMutation({
    mutationFn: (payload: AddPromptPayload) =>
      api.promptTemplate.create(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to create prompt template"),
    onSettled: (_, err) =>
      !err && toast.success("Create prompt template successfully"),
    meta: {
      awaits: promptTemplateKeys.allLists(),
    },
  });
};

export const useUpdateProtocolPrompt = () => {
  return useMutation({
    mutationFn: (payload: UpdatePromptPayload) =>
      api.promptTemplate.update(payload),
    onError: (err) =>
      toast.error(err?.message || "Fail to update prompt template"),
    onSettled: (_, err) =>
      !err && toast.success("Update prompt template successfully"),
    meta: {
      awaits: promptTemplateKeys.allLists(),
    },
  });
};

export const useActivatePrompt = () => {
  return useMutation({
    mutationFn: (id: string) => api.promptTemplate.activate(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to activate prompt template"),
    onSettled: (_, err) =>
      !err && toast.success("Activate prompt template successfully"),
    meta: {
      awaits: promptTemplateKeys.allLists(),
    },
  });
};

export const useDeletePrompt = () => {
  return useMutation({
    mutationFn: (id: string) => api.promptTemplate.delete(id),
    onError: (err) =>
      toast.error(err?.message || "Fail to delete prompt template"),
    onSettled: (_, err) =>
      !err && toast.success("Delete prompt template successfully"),
    meta: {
      awaits: promptTemplateKeys.allLists(),
    },
  });
};
