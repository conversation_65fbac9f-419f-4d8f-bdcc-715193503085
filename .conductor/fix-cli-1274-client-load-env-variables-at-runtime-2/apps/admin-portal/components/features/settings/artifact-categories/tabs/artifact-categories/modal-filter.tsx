import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox, Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteCategoryVersion } from "@/hooks/queries/use-infinite-category-versions";

import { useFilterArtifactCategories } from "./hooks/use-filter-artifact-categories";

const filterSchema = z.object({
  tmfZoneName: z.string().optional(),
  tmfSectionName: z.string().optional(),
  tmfRecordGroupName: z.string().optional(),
  isfZoneName: z.string().optional(),
  isfSectionName: z.string().optional(),
  isfRecordGroupName: z.string().optional(),
  recordType: z.string().optional(),
  alternativeNames: z.string().optional(),
  isTMF: z.boolean().default(false),
  isISF: z.boolean().default(false),
  version: z.string().optional(),
  isActive: z.boolean().default(false),
  versionLatest: z.boolean().default(false),
  requiresSignature: z.boolean().default(false),
  expires: z.boolean().default(false),
  inspectableRecord: z.boolean().default(false),
  includesPHI: z.boolean().default(false),
});

type FilterFormValues = z.infer<typeof filterSchema>;

type ModalFilterProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalFilter = ({ isOpen, onClose }: ModalFilterProps) => {
  const {
    tmfZoneName,
    setTmfZoneName,
    tmfSectionName,
    setTmfSectionName,
    tmfRecordGroupName,
    setTmfRecordGroupName,
    isfZoneName,
    setIsfZoneName,
    isfSectionName,
    setIsfSectionName,
    isfRecordGroupName,
    setIsfRecordGroupName,
    recordType,
    setRecordType,
    alternativeNames,
    setAlternativeNames,
    isTMF,
    setIsTMF,
    isISF,
    setIsISF,
    version,
    setVersion,
    isActive,
    setIsActive,
    versionLatest,
    setVersionLatest,
    requiresSignature,
    setRequiresSignature,
    expires,
    setExpires,
    inspectableRecord,
    setInspectableRecord,
    includesPHI,
    setIncludesPHI,
    setPage,
  } = useFilterArtifactCategories();

  const hasFilters =
    !!tmfZoneName ||
    !!tmfSectionName ||
    !!tmfRecordGroupName ||
    !!isfZoneName ||
    !!isfSectionName ||
    !!isfRecordGroupName ||
    !!recordType ||
    !!alternativeNames ||
    !!isTMF ||
    !!isISF ||
    !!version ||
    !!isActive ||
    !!versionLatest ||
    !!requiresSignature ||
    !!expires ||
    !!inspectableRecord ||
    !!includesPHI;

  const handleSubmit = (data: FilterFormValues) => {
    setTmfZoneName(data.tmfZoneName || null);
    setTmfSectionName(data.tmfSectionName || null);
    setTmfRecordGroupName(data.tmfRecordGroupName || null);
    setIsfZoneName(data.isfZoneName || null);
    setIsfSectionName(data.isfSectionName || null);
    setIsfRecordGroupName(data.isfRecordGroupName || null);
    setRecordType(data.recordType || null);
    setAlternativeNames(data.alternativeNames || null);
    setIsTMF(data.isTMF || null);
    setIsISF(data.isISF || null);
    setVersion(data.version || null);
    setIsActive(data.isActive || null);
    setVersionLatest(data.versionLatest || null);
    setRequiresSignature(data.requiresSignature || null);
    setExpires(data.expires || null);
    setInspectableRecord(data.inspectableRecord || null);
    setIncludesPHI(data.includesPHI || null);
    setPage(1);
    onClose();
  };

  const handleClearFilters = () => {
    setTmfZoneName(null);
    setTmfSectionName(null);
    setTmfRecordGroupName(null);
    setIsfZoneName(null);
    setIsfSectionName(null);
    setIsfRecordGroupName(null);
    setRecordType(null);
    setAlternativeNames(null);
    setIsTMF(null);
    setIsISF(null);
    setVersion(null);
    setIsActive(null);
    setVersionLatest(null);
    setRequiresSignature(null);
    setExpires(null);
    setInspectableRecord(null);
    setIncludesPHI(null);
    setPage(1);
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Filter Artifact Categories</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={filterSchema}
          onSubmit={handleSubmit}
          defaultValues={{
            tmfZoneName: tmfZoneName || "",
            tmfSectionName: tmfSectionName || "",
            tmfRecordGroupName: tmfRecordGroupName || "",
            isfZoneName: isfZoneName || "",
            isfSectionName: isfSectionName || "",
            isfRecordGroupName: isfRecordGroupName || "",
            recordType: recordType || "",
            alternativeNames: alternativeNames || "",
            isTMF: isTMF || false,
            isISF: isISF || false,
            version: version || "",
            versionLatest: versionLatest || false,
            isActive: isActive || false,
            requiresSignature: requiresSignature || false,
            expires: expires || false,
            inspectableRecord: inspectableRecord || false,
            includesPHI: includesPHI || false,
          }}
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="flex flex-col gap-2">
              <Label htmlFor="tmfZoneName">TMF Zone Name</Label>
              <InputField
                id="tmfZoneName"
                name="tmfZoneName"
                placeholder="Filter by TMF Zone..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="tmfSectionName">TMF Section Name</Label>
              <InputField
                id="tmfSectionName"
                name="tmfSectionName"
                placeholder="Filter by TMF Section..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="tmfRecordGroupName">TMF Record Group Name</Label>
              <InputField
                id="tmfRecordGroupName"
                name="tmfRecordGroupName"
                placeholder="Filter by TMF Record Group..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="isfZoneName">ISF Zone Name</Label>
              <InputField
                id="isfZoneName"
                name="isfZoneName"
                placeholder="Filter by ISF Zone..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="isfSectionName">ISF Section Name</Label>
              <InputField
                id="isfSectionName"
                name="isfSectionName"
                placeholder="Filter by ISF Section..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="isfRecordGroupName">ISF Record Group Name</Label>
              <InputField
                id="isfRecordGroupName"
                name="isfRecordGroupName"
                placeholder="Filter by ISF Record Group..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="recordType">Record Type</Label>
              <InputField
                id="recordType"
                name="recordType"
                placeholder="Filter by Record Type..."
              />
            </div>

            <div className="flex flex-col gap-2">
              <Label htmlFor="alternativeNames">Alternative Names</Label>
              <InputField
                id="alternativeNames"
                name="alternativeNames"
                placeholder="Filter by Alternative Names..."
              />
            </div>

            <div className="flex flex-col gap-2 sm:col-span-2">
              <Label htmlFor="version">Version</Label>
              <LazySelect
                id="version"
                name="version"
                placeholder="Select version..."
                searchPlaceholder="Search versions..."
                useInfiniteQuery={useInfiniteCategoryVersion}
                getOptionLabel={(option) => option.version.toString()}
                getOptionValue={(option) => option.version.toString()}
              />
            </div>

            <div className="flex flex-wrap items-center gap-x-8 gap-y-2 sm:col-span-2">
              <div className="flex items-center gap-2">
                <Checkbox id="isTMF" name="isTMF" />
                <Label htmlFor="isTMF">TMF</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="isISF" name="isISF" />
                <Label htmlFor="isISF">ISF</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="isActive" name="isActive" />
                <Label htmlFor="isActive">Active</Label>
              </div>

              <div className="flex items-center gap-2 ">
                <Checkbox id="versionLatest" name="versionLatest" />
                <Label htmlFor="versionLatest">Latest Version</Label>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-x-8 gap-y-2 sm:col-span-2">
              <div className="flex items-center gap-2">
                <Checkbox id="requiresSignature" name="requiresSignature" />
                <Label htmlFor="requiresSignature">Requires Signature</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="expires" name="expires" />
                <Label htmlFor="expires">Expires</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="inspectableRecord" name="inspectableRecord" />
                <Label htmlFor="inspectableRecord">Inspectable</Label>
              </div>

              <div className="flex items-center gap-2">
                <Checkbox id="includesPHI" name="includesPHI" />
                <Label htmlFor="includesPHI">PHI</Label>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-8"></div>

          <div className="flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            {hasFilters && (
              <Button
                onClick={handleClearFilters}
                type="button"
                variant="outline"
              >
                Clear Filters
              </Button>
            )}
            <Button type="submit" variant="primary">
              Apply Filters
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
