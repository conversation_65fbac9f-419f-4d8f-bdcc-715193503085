import { <PERSON>pic<PERSON> } from "@clincove/shared-ui";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import { Form, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import {
  useAddCategoryVersion,
  useUpdateCategoryVersion,
} from "./hooks/use-category-version-mutations";

const schema = z.object({
  version: z.coerce.number({
    required_error: "Version is required",
    invalid_type_error: "Version is required",
  }),
  effectiveDate: z
    .string({
      required_error: "Effective date is required",
      invalid_type_error: "Effective date is required",
    })
    .min(1, "Effective date is required"),
  notes: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVersion: CategoryVersion | null;
};

export const CategoryVersionModal = function ({
  isOpen,
  onClose,
  selectedVersion,
}: Props) {
  const { mutateAsync: addCategoryVersion, isPending: isAdding } =
    useAddCategoryVersion();
  const { mutateAsync: updateCategoryVersion, isPending: isUpdating } =
    useUpdateCategoryVersion();

  const isEditing = !!selectedVersion;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateCategoryVersion({
          ...data,
          id: selectedVersion.id,
        })
      : await addCategoryVersion(data);

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Category Version Details`}
    >
      <Form
        defaultValues={{
          version: selectedVersion?.version || "",
          notes: selectedVersion?.notes || "",
          effectiveDate: selectedVersion?.effectiveDate || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="version">Version</Label>
            <InputNumber
              min={0}
              id="version"
              name="version"
              placeholder="Enter version..."
              readOnly
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="effectiveDate">Effective Date</Label>
            <Datepicker id="effectiveDate" name="effectiveDate" readOnly />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="notes">Description</Label>
            <Textarea
              id="notes"
              name="notes"
              placeholder="Enter description..."
              readOnly
            />
          </div>
        </div>
      </Form>
    </WrapperModal>
  );
};
