import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { MonacoEditor } from "@/components/ui/form/monaco-editor";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { CategoryVersion } from "@/lib/apis/artifact-categories";

import {
  useAddCategorySummary,
  useUpdateCategorySummary,
} from "./hooks/use-category-version-mutations";

const schema = z.object({
  content: z
    .string({
      required_error: "Content is required",
      invalid_type_error: "Content is required",
    })
    .min(1, "Content is required"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVersion: CategoryVersion;
};

export const SummaryModal = function ({
  isOpen,
  onClose,
  selectedVersion,
}: Props) {
  const { mutateAsync: addCategorySummary, isPending: isAdding } =
    useAddCategorySummary();
  const { mutateAsync: updateCategorySummary, isPending: isUpdating } =
    useUpdateCategorySummary();

  const isEditing = !!selectedVersion.summary;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateCategorySummary({
          ...data,
          id: selectedVersion.summary?.id as string,
        })
      : await addCategorySummary({
          ...data,
          artifactCategoryVersionId: selectedVersion?.id,
        });

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      size="4xl"
      title={`${isEditing ? "Edit" : "Add"} Summary Content`}
    >
      <Form
        defaultValues={{
          content: selectedVersion?.summary?.content || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="space-y-2">
          <Label htmlFor="content">Summary Content</Label>
          <MonacoEditor height={500} language="html" name="content" />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            isLoading={isAdding || isUpdating}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
