import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const isfRefModelKeys = {
  all: () => ["isf-models"] as const,

  allLists: () => [...isfRefModelKeys.all(), "lists"] as const,
  list: (params: MetadataParams) =>
    [...isfRefModelKeys.allLists(), params] as const,
};

export const useISFRefModels = () => {
  const { search } = useSearch();
  const { take, page } = usePagination();

  const params = {
    page,
    take,
    filter: {
      isfRefModel: search,
    },
  };

  return useQuery({
    queryKey: isfRefModelKeys.list(params),
    queryFn: () => api.isfRefModelApi.list(params),
    placeholderData: (prev) => prev,
  });
};
