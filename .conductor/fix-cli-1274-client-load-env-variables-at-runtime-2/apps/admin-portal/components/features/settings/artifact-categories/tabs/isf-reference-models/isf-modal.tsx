import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { ISFRefModel } from "@/lib/apis/isf-ref-models";

import {
  useAddISFRefModel,
  useUpdateISFRefModel,
} from "./hooks/use-isf-ref-model-mutations";

const schema = z.object({
  isfRefModel: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  description: z.string().optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedISFModel: ISFRefModel | null;
};

export const ISFModal = function ({
  isOpen,
  onClose,
  selectedISFModel,
}: Props) {
  const { mutateAsync: addISFRefModel, isPending: isAdding } =
    useAddISFRefModel();
  const { mutateAsync: updateISFRefModel, isPending: isUpdating } =
    useUpdateISFRefModel();

  const isEditing = !!selectedISFModel;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateISFRefModel({
          ...data,
          id: selectedISFModel.id,
        })
      : await addISFRefModel(data);

    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`ISF Reference Model Details`}
    >
      <Form
        defaultValues={{
          isfRefModel: selectedISFModel?.isfRefModel || "",
          description: selectedISFModel?.description || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid grid-cols-1 gap-6">
          <div className="flex flex-col gap-2">
            <Label htmlFor="isfRefModel">Label</Label>
            <InputField
              id="isfRefModel"
              name="isfRefModel"
              placeholder="Enter label..."
              readOnly
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
              readOnly
            />
          </div>
        </div>
      </Form>
    </WrapperModal>
  );
};
