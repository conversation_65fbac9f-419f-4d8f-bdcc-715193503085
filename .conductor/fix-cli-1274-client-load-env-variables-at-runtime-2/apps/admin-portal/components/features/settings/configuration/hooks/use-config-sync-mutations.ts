import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";

import { configSyncKeys } from "./use-config-sync-queries";

export const useRunConfigSync = () => {
  return useMutation({
    mutationFn: async () => {
      return api.configSync.run();
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Configuration sync started successfully");
      }
    },
    onError: (error) =>
      toast.error(error.message || "Failed to start configuration sync"),
    meta: {
      awaits: configSyncKeys.all(),
    },
  });
};
