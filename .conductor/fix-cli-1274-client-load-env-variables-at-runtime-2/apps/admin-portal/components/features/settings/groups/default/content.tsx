"use client";

import { Card } from "flowbite-react";
import { type FC, useMemo } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { Group } from "@/lib/apis/groups/types";

import { useGroups } from "../hooks/use-groups";
import { columns } from "./columns";

const BREADCRUMB_ITEMS = [{ label: "Groups" }];

const GroupPageContent: FC = function () {
  const { data, isLoading } = useGroups();

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "Name", key: "name" },
      { label: "Type", key: "type" },
      { label: "City", key: "city" },
      { label: "State/Province", key: "stateProvince" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((group) =>
        headers.map((header) => group[header.key as keyof Group]),
      ),
    ];
  }, [data]);

  return (
    <div className="space-y-4">
      <Breadcrumb items={BREADCRUMB_ITEMS} />
      <PageHeader>Groups</PageHeader>
      <Card className="[&>div]:p-0">
        <HeaderActions data={exportCSVData} filename="groups.csv" />
        {isLoading && <TableLoading columns={columns} />}
        {data && (
          <>
            <Table data={data.results} columns={columns} />
            {data.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
    </div>
  );
};

export default GroupPageContent;
