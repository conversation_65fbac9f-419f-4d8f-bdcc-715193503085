import { useMutation, useQueryClient } from "@tanstack/react-query";

import api from "@/lib/apis";
import type { RemoveUserFromGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUP_PROFILES_QUERY_KEY } from "./use-group-profiles";

export function useRemoveProfileFromGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: RemoveUserFromGroupPayload) =>
      api.groups.removeProfileFromGroup(payload),
    onSuccess: (_, { groupId }) => {
      queryClient.invalidateQueries({
        queryKey: [USE_GROUP_PROFILES_QUERY_KEY, groupId],
      });
    },
  });
}
