import type { z } from "zod";

import { Form } from "@/components/ui/form";
import { Modal } from "@/components/ui/modal";
import type { Group } from "@/lib/apis/groups/types";

import { addGroupSchema, GroupForm } from "../default/modal-add-group";
import { useUpdateGroup } from "../hooks/use-update-group";

const editGroupSchema = addGroupSchema;

type ModalEditGroupProps = {
  isOpen: boolean;
  onClose: () => void;
  group?: Group;
};

export const ModalEditGroup = function ({
  isOpen,
  onClose,
  group,
}: ModalEditGroupProps) {
  const { mutateAsync: updateGroup, isPending } = useUpdateGroup();

  async function onSubmit(data: z.infer<typeof editGroupSchema>) {
    await updateGroup({ id: group?.id as string, ...data });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Group</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editGroupSchema}
          onSubmit={onSubmit}
          defaultValues={{
            ...group,
            address: {
              ...group?.address,
              countryId: group?.address?.country.id,
              stateProvinceId: group?.address?.stateProvince.id,
            },
          }}
          formProps={{ shouldFocusError: false }}
        >
          <GroupForm onClose={onClose} isSubmitting={isPending} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};
