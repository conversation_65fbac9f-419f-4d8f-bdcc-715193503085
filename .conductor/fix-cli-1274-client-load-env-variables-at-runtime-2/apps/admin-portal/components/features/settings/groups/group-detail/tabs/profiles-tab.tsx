"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { But<PERSON> } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";
import { useDisclosure } from "@/hooks/use-disclosure";

import { useGroupProfiles } from "../hooks/use-group-profiles";
import { generateColumns } from "./columns/profiles-columns";
import { useUpdateProfileStatus } from "./hooks/use-update-profile-status";
import { ModalAddProfile } from "./modal-add-profile";

type ProfilesTabProps = {
  groupId: string;
  isAdminGroup: boolean;
};

export const ProfilesTab = ({ groupId, isAdminGroup }: ProfilesTabProps) => {
  const { data: profiles, isLoading } = useGroupProfiles(groupId);
  const { mutateAsync: updateStatus, isPending: isUpdateStatus } =
    useUpdateProfileStatus();
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<any>(null);

  const {
    close: onCloseWarningModal,
    isOpen: isOpenWarningModal,
    open: onOpenWarningModal,
  } = useDisclosure();

  const columns = useMemo(
    () =>
      generateColumns((profile: any) => {
        if (profile.profile.isOnlyActiveProfile && profile.profile.isActive) {
          setSelectedProfile(profile);
          onOpenWarningModal();
        } else {
          updateStatus({
            userId: profile.profile.userId,
            profileId: profile.profile.id,
            isActive: !profile.profile.isActive,
          });
        }
      }),
    [updateStatus, onOpenWarningModal],
  );

  const handleConfirmDisable = () => {
    if (selectedProfile) {
      updateStatus({
        userId: selectedProfile.profile.userId,
        profileId: selectedProfile.profile.id,
        isActive: false,
      });
      setSelectedProfile(null);
      onCloseWarningModal();
    }
  };

  const handleCloseWarningModal = () => {
    setSelectedProfile(null);
    onCloseWarningModal();
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Profiles</div>
          {!isAdminGroup && groupId && (
            <Button variant="primary" onClick={() => setIsOpen(true)}>
              <IoMdAdd />
              Add Profile
            </Button>
          )}
        </div>

        {isLoading ? (
          <TableLoading columns={columns} />
        ) : (
          <TableData columns={columns} data={profiles?.results ?? []} />
        )}
      </Card>
      <ModalAddProfile
        groupId={groupId}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
      <ConfirmModal
        isOpen={isOpenWarningModal}
        onClose={handleCloseWarningModal}
        onConfirm={handleConfirmDisable}
        isLoading={isUpdateStatus}
        title="Disable Profile"
        confirmLabel="Disable"
      >
        <span className="dark:text-white">
          If you disable this profile, the associated user account will also be
          disabled.
        </span>
      </ConfirmModal>
    </>
  );
};
