"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";

import { useGroupStudies } from "../hooks/use-group-studies";
import { useRemoveStudyFromGroup } from "../hooks/use-remove-study-from-group";
import { generateColumns } from "./columns/studies-columns";
import { ModalAddStudy } from "./modal-add-study";

export const StudiesTab = ({ groupId }: { groupId: string }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: studies, isLoading } = useGroupStudies(groupId);
  const { mutateAsync: removeStudy, isPending: isRemoving } =
    useRemoveStudyFromGroup();
  const columns = useMemo(
    () =>
      generateColumns((studyId: string, siteId: string) => {
        removeStudy({
          groupId,
          studyId,
          siteId,
        });
      }),
    [groupId, removeStudy],
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Studies</div>
        <Button variant="primary" onClick={() => setIsOpen(true)}>
          <IoMdAdd />
          Add Study
        </Button>
      </div>

      {isLoading ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isRemoving}>
          <TableData
            columns={columns}
            data={studies?.results ?? []}
            isLoading={isLoading}
          />
          {studies?.metadata && (
            <TableDataPagination metadata={studies.metadata} />
          )}
        </LoadingWrapper>
      )}

      <ModalAddStudy
        groupId={groupId}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </Card>
  );
};
