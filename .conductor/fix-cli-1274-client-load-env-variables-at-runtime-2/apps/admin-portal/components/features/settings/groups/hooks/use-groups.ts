import { useQuery } from "@tanstack/react-query";
import { parseAsString } from "nuqs";
import { useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";

export const USE_GROUPS_QUERY_KEY = "use-groups";

export const useGroups = () => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  const [type] = useQueryState("type", parseAsString);
  const [groupInstitutionType] = useQueryState(
    "groupInstitutionType",
    parseAsString,
  );
  const [stateProvince] = useQueryState("stateProvince", parseAsString);
  const [city] = useQueryState("city", parseAsString);
  const [name] = useQueryState("name", parseAsString);

  const params = {
    page,
    take,
    filter: {
      name: name || search,
      type: type || undefined,
      groupInstitutionType: groupInstitutionType || undefined,
      stateProvinceId: stateProvince || undefined,
      city: city || undefined,
    },
  };

  return useQuery({
    queryKey: [USE_GROUPS_QUERY_KEY, params],
    queryFn: () => api.groups.list(params),
    placeholderData: (previousData) => previousData,
  });
};
