import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateGroupPayload } from "@/lib/apis/groups/types";

import { USE_GROUP_QUERY_KEY } from "./use-group";

export const useUpdateGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateGroupPayload & { id: string }) => {
      const { id, ...data } = payload;
      return api.groups.update(id, data);
    },
    onSuccess: (_, variables) => {
      toast.success("Group updated successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_GROUP_QUERY_KEY, variables.id],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
