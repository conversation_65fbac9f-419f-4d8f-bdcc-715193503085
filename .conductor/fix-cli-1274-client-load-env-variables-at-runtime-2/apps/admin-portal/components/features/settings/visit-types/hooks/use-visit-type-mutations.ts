import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type {
  CreateVisitTypePayload,
  UpdateVisitTypePayload,
} from "@/lib/apis/visit-types";

import { visitTypeKeys } from "./use-visit-types";

export const useAddVisitType = () => {
  return useMutation({
    mutationFn: async (data: CreateVisitTypePayload) => {
      return api.visitTypes.create(data);
    },
    onSettled: (_, err) =>
      !err && toast.success("Visit type added successfully"),
    onError: (error) =>
      toast.error(error.message || "Failed to add visit type"),
    meta: {
      awaits: visitTypeKeys.allList(),
    },
  });
};

export const useEditVisitType = () => {
  return useMutation({
    mutationFn: async (data: UpdateVisitTypePayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.visitTypes.update(id, payload);
    },
    onSettled: (_, err) =>
      !err && toast.success("Visit type updated successfully"),
    onError: (error) =>
      toast.error(error.message || "Failed to update visit type"),
    meta: {
      awaits: visitTypeKeys.allList(),
    },
  });
};
