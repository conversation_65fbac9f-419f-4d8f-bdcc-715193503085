"use client";

import { ListFilter } from "lucide-react";
import { parseAsBoolean, parseAsString, useQueryState } from "nuqs";
import { useRef, useState } from "react";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { Form, FormRef } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Switch } from "@/components/ui/form/toggle-switch";
import { LazySelect } from "@/components/ui/lazy-select";
import { useInfiniteStudies } from "@/hooks/queries/use-infinite-studies";
import { cn } from "@/lib/utils";

const schema = z.object({
  studyId: z.string().optional(),
  isActive: z.boolean().optional(),
});

export const VisitTypeFilters = () => {
  const [open, setOpen] = useState(false);
  const [studyId] = useQueryState("studyId", parseAsString);
  const [isActive] = useQueryState("isActive", parseAsBoolean);

  const getActiveFilterCount = () => {
    let count = 0;
    if (studyId) count++;
    if (isActive) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dropdown open={open} onOpenChange={setOpen} placement="bottom-end">
      <DropdownTrigger>
        <Button
          variant="outline"
          className={cn(
            "relative !py-1 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 sm:!py-2 dark:hover:border-blue-600 dark:hover:bg-blue-900/20",
            activeFilterCount > 0 &&
              "border-blue-500 bg-blue-50 text-blue-700 dark:border-blue-400 dark:bg-blue-900/30 dark:text-blue-300",
          )}
        >
          <ListFilter className="size-6" />
          {activeFilterCount > 0 && (
            <span className="absolute right-1 top-1 grid h-5 w-5 place-items-center rounded-full bg-blue-500 text-xs font-bold text-white dark:bg-blue-400 dark:text-blue-900">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </DropdownTrigger>
      <DropdownContent className="z-10 rounded-lg bg-white drop-shadow-2xl dark:bg-gray-700">
        <FilterContent setOpen={setOpen} />
      </DropdownContent>
    </Dropdown>
  );
};

type FilterContentProps = {
  setOpen: (open: boolean) => void;
};

const FilterContent = ({ setOpen }: FilterContentProps) => {
  const formRef = useRef<FormRef<typeof schema>>(null);
  const [studyId, setStudyId] = useQueryState("studyId", parseAsString);
  const [isActive, setIsActive] = useQueryState("isActive", parseAsBoolean);

  function onSubmit(data: z.infer<typeof schema>) {
    setStudyId(data.studyId || null);
    setIsActive(data.isActive || null);
    setOpen(false);
  }

  function onClear() {
    setStudyId(null);
    setIsActive(null);
    formRef.current?.formHandler?.reset({
      studyId: "",
      isActive: false,
    });
  }

  const defaultValues = {
    studyId: studyId ?? "",
    isActive: isActive ?? false,
  };

  const hasFilters = !!studyId || !!isActive;

  return (
    <Form
      schema={schema}
      mode="onChange"
      onSubmit={onSubmit}
      className="w-80 sm:w-96"
      ref={formRef}
      defaultValues={defaultValues}
    >
      <div className="flex flex-col divide-y">
        <div className="px-[15px] pb-5 pt-2.5">
          <Label
            htmlFor="studyId"
            className="mb-2 block text-base font-normal leading-6 text-gray-700"
          >
            Study
          </Label>
          <LazySelect
            name="studyId"
            useInfiniteQuery={useInfiniteStudies}
            getOptionLabel={(study) => study.name}
            getOptionValue={(study) => study.id}
            placeholder="Select Study"
            searchPlaceholder="Search studies..."
          />
        </div>
        <div className="flex gap-2 px-[15px] pb-5 pt-2.5">
          <Label
            htmlFor="isActive"
            className="mb-2 block text-base font-normal leading-6 text-gray-700"
          >
            Just Show Active
          </Label>
          <Switch name="isActive" />
        </div>
      </div>

      <div className="flex justify-end gap-5 px-[15px] pb-5 pt-2.5">
        <Button
          variant="outline"
          className={cn(
            "w-full justify-center",
            !hasFilters && "pointer-events-none invisible",
          )}
          onClick={() => {
            onClear();
          }}
          type="button"
        >
          Clear Filters
        </Button>
        <Button
          variant="primary"
          type="submit"
          className="w-full justify-center"
        >
          Apply Filters
        </Button>
      </div>
    </Form>
  );
};
