import { z } from "zod";

import {
  AddressFormFields,
  addressSchema,
} from "@/components/shared/address-form-fields";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { useAddSite } from "../hooks/use-add-site";

export const addSiteSchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  email: z
    .string({ required_error: "Email is required" })
    .email("Invalid email format")
    .optional(),
  phone: z.string().optional(),
  address: addressSchema,
});

type ModalAddSiteProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddSite = ({ isOpen, onClose }: ModalAddSiteProps) => {
  const { mutateAsync: addSite, isPending: isAdding } = useAddSite();

  async function onSubmit(data: z.infer<typeof addSiteSchema>) {
    await addSite(data);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Site</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addSiteSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            name: "",
            email: "",
            phone: "",
            address: {
              addressLine: "",
              city: "",
              stateProvinceId: "",
              zipPostalCode: "",
              countryId: "",
            },
          }}
        >
          <SiteForm onClose={onClose} isSubmitting={isAdding} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type SiteFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

export const SiteForm = ({ onClose, isSubmitting }: SiteFormProps) => {
  return (
    <>
      <div className="grid gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="space-y-1">
          <Label htmlFor="name">Name</Label>
          <InputField id="name" name="name" placeholder="Enter name..." />
        </div>

        <div className="space-y-1">
          <Label htmlFor="email">Email</Label>
          <InputField id="email" name="email" placeholder="Enter email..." />
        </div>

        <div className="space-y-1">
          <Label htmlFor="phone">Phone</Label>
          <InputField id="phone" name="phone" placeholder="Enter phone..." />
        </div>

        <AddressFormFields />
      </div>
      <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
