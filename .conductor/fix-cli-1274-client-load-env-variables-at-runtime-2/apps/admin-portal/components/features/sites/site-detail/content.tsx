"use client";

import { useParams } from "next/navigation";
import { useState } from "react";
import { RiEditLine } from "react-icons/ri";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";
import { formatDate } from "@/lib/utils";

import { useSite } from "../hooks/use-site";
import { ModalEditSite } from "./modal-edit-site";
import { ContactsTab, DocExchangeTab, StudiesTab, UsersTab } from "./tabs";

const SITE_TABS = [
  {
    key: "studies",
    content: <StudiesTab />,
  },
  {
    key: "users",
    content: <UsersTab />,
  },
  {
    key: "doc-exchange",
    title: "Doc Exchange",
    content: <DocExchangeTab />,
  },
  {
    key: "contacts",
    content: <ContactsTab />,
  },
];

export const SiteDetailPageContent = () => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const params = useParams();
  const id = params.id as string;
  const { data: site, isLoading: isLoadingSite } = useSite(id);

  const { address } = site ?? {};

  const breadcrumbItems = [
    { label: "Sites", href: "/sites" },
    { label: site?.name ?? "Site Detail", loading: isLoadingSite },
  ];
  return (
    <>
      <div className="flex flex-col gap-4 ">
        <Breadcrumb items={breadcrumbItems} />
        <div className="flex justify-between gap-2">
          <PageHeader showBackButton href="/sites">
            {site?.name}
          </PageHeader>

          <Button
            className="flex-shrink-0"
            variant="primary"
            onClick={() => setIsEditModalOpen(true)}
          >
            <RiEditLine />
            Edit Site
          </Button>
        </div>

        {isLoadingSite ? (
          <OverviewSkeleton />
        ) : (
          <OverviewCard title="Overview">
            <div className="grid grid-cols-2 gap-4 xl:grid-cols-4">
              <OverviewItem label="Name" value={site?.name ?? ""} />
              <OverviewItem
                label="Address"
                value={
                  address
                    ? `${address.addressLine}, ${address.city}, ${address.stateProvince.name}, ${address.zipPostalCode}`
                    : "N/A"
                }
              />
              <OverviewItem label="Active Status">
                <PillBadge variant={site?.isActive ? "success" : "default"}>
                  {site?.isActive ? "Active" : "Inactive"}
                </PillBadge>
              </OverviewItem>
              <OverviewItem
                label="Created Date"
                value={
                  site?.createdDate
                    ? formatDate(site?.createdDate, "MMM dd, yyyy")
                    : ""
                }
              />
            </div>
          </OverviewCard>
        )}
        <TabsWrapper tabs={SITE_TABS} mountOnActive />
      </div>

      <ModalEditSite
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        site={site}
      />
    </>
  );
};

const OverviewSkeleton = () => (
  <OverviewCard title="Overview">
    <div className="grid grid-cols-2 gap-4 xl:grid-cols-4">
      <div className="space-y-1">
        <Skeleton className="h-6 w-11 " />
        <Skeleton className="h-6 w-48 " />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-16 " />
        <Skeleton className="h-6 w-52 " />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-28 " />
        <Skeleton className="h-6 w-20 " />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-6 w-28 " />
        <Skeleton className="h-6 w-24 " />
      </div>
    </div>
  </OverviewCard>
);
