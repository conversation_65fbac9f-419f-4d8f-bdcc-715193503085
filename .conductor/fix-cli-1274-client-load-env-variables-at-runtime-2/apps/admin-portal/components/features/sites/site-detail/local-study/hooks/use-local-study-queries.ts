import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { LocalStudyParams } from "@/lib/apis/sites";

export const localStudyKeys = {
  all: () => ["local-study"] as const,

  detail: (params: LocalStudyParams) =>
    [...localStudyKeys.all(), params] as const,
};

export const useLocalStudy = ({ siteId, studyId }: LocalStudyParams) => {
  return useQuery({
    queryKey: localStudyKeys.detail({
      siteId,
      studyId,
    }),

    queryFn: () =>
      api.sites.getStudy({
        siteId,
        studyId,
      }),
  });
};
