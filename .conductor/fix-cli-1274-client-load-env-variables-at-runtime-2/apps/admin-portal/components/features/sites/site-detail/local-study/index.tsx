"use client";

import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { Skeleton } from "@/components/ui/skeleton";
import { TabsWrapper } from "@/components/ui/tabs-wrapper";

import { useLocalStudy } from "./hooks/use-local-study-queries";
import { useLocalStudyParams } from "./hooks/useLocalStudyParams";
import { IsfTab } from "./tabs/isf";
import { OverviewTab } from "./tabs/overview";
import { PatientsTab } from "./tabs/patients";
import { TasksTab } from "./tabs/tasks";

const LOCAL_STUDY_TABS = [
  {
    key: "overview",
    content: <OverviewTab />,
  },
  {
    key: "isf",
    title: "ISF",
    content: <IsfTab />,
  },
  {
    key: "patients",
    content: <PatientsTab />,
  },
  {
    key: "tasks",
    content: <TasksTab />,
  },
];

export const LocalStudyContent = () => {
  const { siteId, studyId } = useLocalStudyParams();

  const { data, isPending } = useLocalStudy({
    siteId,
    studyId,
  });

  const breadcrumbItems = [
    { label: "Sites", href: "/sites" },
    {
      label: data?.site.name ?? "Site Detail",
      href: data?.siteId ? `/sites/${data.siteId}` : "/sites",
      loading: isPending,
    },
    { label: data?.study.name ?? "Local Study", loading: isPending },
  ];

  return (
    <div className="space-y-4 ">
      <Breadcrumb items={breadcrumbItems} />
      {isPending ? (
        <PageHeader showBackButton href={`/sites/${siteId}`}>
          <Skeleton className="h-7 w-60" />
        </PageHeader>
      ) : (
        <PageHeader showBackButton href={`/sites/${siteId}`}>
          {data ? `${data.site.name} - ${data.study.name}` : "Local study"}
        </PageHeader>
      )}

      <TabsWrapper mountOnActive tabs={LOCAL_STUDY_TABS} />
    </div>
  );
};
