import { functionalUpdate } from "@tanstack/react-table";
import React, { useMemo, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Document } from "@/lib/apis/essential-document-files/types";

import { useFilterDocuments } from "../hooks/use-filter-documents";
import { useDeleteDocument } from "../hooks/use-isf-mutations";
import { useDocuments } from "../hooks/use-isf-queries";
import AuditModal from "./audit-modal";
import { generateDocumentColumns } from "./columns";

type Props = {
  onOpenEditDocument: (file: Document) => void;
  draggingFile: Document | null;
  isMovingDocument: boolean;
};

export const DocumentsTable = ({
  onOpenEditDocument,
  draggingFile,
  isMovingDocument,
}: Props) => {
  const { orderBy, orderDirection, changeSort, showAllDetails } =
    useFilterDocuments();
  const {
    isOpen: isAuditOpen,
    open: openAudit,
    close: closeAudit,
  } = useDisclosure();

  // const {
  //   isOpen: isPreviewOpen,
  //   open: openPreview,
  //   close: closePreview,
  // } = useDisclosure();
  const [selectedDocumentId, setSelectedDocumentId] = useState("");
  const { data, isPending, isPlaceholderData } = useDocuments();
  const { mutateAsync: deleteDocument, isPending: isDeleting } =
    useDeleteDocument();

  const columns = useMemo(
    () =>
      generateDocumentColumns({
        // onPreview: (document) => {
        //   openPreview();
        //   setSelectedDocumentId(document.id);
        // },
        onEdit: onOpenEditDocument,
        onViewAuditLogs: (document) => {
          openAudit();
          setSelectedDocumentId(document.id);
        },
        onDelete: (document) => {
          deleteDocument(document.id);
        },
        isShowDetailColumns: showAllDetails,
      }),
    [onOpenEditDocument, showAllDetails, deleteDocument, openAudit],
  );

  // const handleClosePreview = () => {
  //   closePreview();
  //   setSelectedDocumentId("");
  // };

  const handleCloseAudit = () => {
    closeAudit();
    setSelectedDocumentId("");
  };
  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];

  return (
    <>
      <div className="overflow-hidden rounded-lg border border-gray-300 dark:border-gray-500">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper
            isLoading={isMovingDocument || isPlaceholderData || isDeleting}
          >
            <Table
              columns={columns}
              data={data?.results ?? []}
              draggingRowId={draggingFile?.id || ""}
              enableSorting
              sorting={sorting}
              onSortingChange={(updater) => {
                const newSorting = functionalUpdate(updater, sorting);
                const sort = newSorting[0];
                if (sort)
                  return changeSort(sort.id, sort.desc ? "desc" : "asc");
                changeSort();
              }}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>
      <AuditModal
        documentId={selectedDocumentId}
        isOpen={isAuditOpen}
        onClose={handleCloseAudit}
      />
      {/* {isPreviewOpen && (
        <PreviewDocumentModal
          documentId={selectedDocumentId}
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
        />
      )} */}
    </>
  );
};
