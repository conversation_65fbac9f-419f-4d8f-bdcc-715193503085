import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import {
  DocumentStatus,
  DocumentStatusBadge,
} from "@/components/ui/badges/ebinder-document-badge";
import { Document } from "@/lib/apis/essential-document-files/types";

type Props = {
  document: Document;
};

export const FileOverlay = ({ document }: Props) => {
  const status = document?.isfStatus?.name || document.tmfStatus?.name;

  return (
    <div className="flex w-fit max-w-[360px] cursor-grabbing items-center gap-2 rounded-lg border border-purple-500 bg-white px-4 sm:max-w-fit md:gap-4">
      {
        DOCUMENT_ICONS[
          document?.currentVersion?.fileRecord.extension as DocumentType
        ]
      }
      <div className="flex min-w-0 flex-1 items-center gap-4 rounded-lg py-2 md:gap-10">
        <span className="min-w-0 flex-1 select-none truncate whitespace-nowrap text-sm font-medium leading-5">
          {document.title}
        </span>
        <span className="hidden whitespace-nowrap md:block">
          {document.category?.artifactNumber || (
            <span className="text-gray-400">N/A</span>
          )}
        </span>
        <DocumentStatusBadge
          className="min-w-0"
          status={status as DocumentStatus}
        />
      </div>
    </div>
  );
};
