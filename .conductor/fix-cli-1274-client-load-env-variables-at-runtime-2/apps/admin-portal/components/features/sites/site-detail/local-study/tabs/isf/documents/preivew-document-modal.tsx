import { Spinner, theme } from "flowbite-react";
import { useState } from "react";
import { BiError } from "react-icons/bi";
import { IoMdClose } from "react-icons/io";
import { IoEyeOutline } from "react-icons/io5";
import { LuMaximize2, LuMinimize2 } from "react-icons/lu";

import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import { DocumentViewer } from "@/components/shared/document-viewers/document-viewer";
import { DocumentTypeBadge } from "@/components/ui/badges/document-type-badge";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { useDisclosure } from "@/hooks/use-disclosure";
import { cn } from "@/lib/utils";

import { useDocument, useDownloadDocument } from "../hooks/use-isf-queries";
import { PreviewDocumentSkeleton } from "./preview-document-skeleton";
import { AuditTrail, DocumentDetails, DocumentHistory } from "./preview-tabs";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
};

const PREVIEW_DOCUMENT_TABS = [
  "document-details",
  "audit-trail",
  "document-history",
] as const;

type PreviewDocumentTab = (typeof PREVIEW_DOCUMENT_TABS)[number];

const PreviewDocumentModal = ({ isOpen, onClose, documentId }: Props) => {
  const { close, isOpen: isOpenDocument, open } = useDisclosure();
  const [isModalFullscreen, setIsModalFullscreen] = useState(false);
  const { data: document, isPending: isPendingDocument } =
    useDocument(documentId);

  const [currentTab, setCurrentTab] = useState<PreviewDocumentTab>(
    PREVIEW_DOCUMENT_TABS[0],
  );
  const {
    data: previewDocument,
    isPending,
    error,
  } = useDownloadDocument(document?.currentVersion?.id);

  const handleRenderPreviewDocument = () => {
    if (isPending)
      return (
        <div className="grid h-full place-content-center dark:bg-inherit">
          <Spinner size="xl" color="blue" className="fill-primary-500" />
        </div>
      );

    if (error)
      return (
        <div className="flex h-full items-center justify-center gap-2 dark:bg-inherit">
          <BiError className="size-8 text-red-500" />
          <span className="text-red-500">
            {(error as Error)?.message || "Fail to load document"}
          </span>
        </div>
      );

    return (
      <DocumentViewer
        type={document?.currentVersion.fileRecord.extension as DocumentType}
        url={previewDocument.url}
      />
    );
  };

  const tabs = {
    "document-details": <DocumentDetails documentId={documentId} />,
    "audit-trail": <AuditTrail documentId={documentId} />,
    "document-history": <DocumentHistory documentId={documentId} />,
  };
  if (isPendingDocument) {
    return <PreviewDocumentSkeleton />;
  }

  return (
    <>
      <Modal
        theme={{
          content: {
            inner: cn(
              theme.modal.content.inner,
              isModalFullscreen && "fixed inset-0 max-h-full w-full",
            ),
          },
          body: {
            base: cn(theme.modal.body.base, isModalFullscreen && "!max-h-full"),
          },
        }}
        size="7xl"
        show={isOpen}
        onClose={onClose}
      >
        <Modal.Header className="border-b-0 !py-0 [&>button]:hidden [&>h3]:w-full">
          <div className="flex flex-col justify-between gap-y-4 pt-3 lg:flex-row">
            <div className="order-2 flex items-center gap-5 lg:order-1">
              <div className="flex flex-1 items-center justify-between">
                <div className="flex items-center gap-3 sm:gap-5">
                  {document?.currentVersion?.fileRecord.extension && (
                    <DocumentTypeBadge
                      className="text-base"
                      type={document.currentVersion.fileRecord.extension}
                    />
                  )}
                  <p className="flex gap-2 sm:gap-3">
                    {document?.currentVersion?.fileRecord.extension &&
                      DOCUMENT_ICONS[
                        document.currentVersion.fileRecord.extension
                      ]}
                    <span className="text-sm font-bold">
                      {document?.title || "Document Preview"}
                    </span>
                  </p>
                </div>
                <Button onClick={open} className="lg:hidden" variant="primary">
                  <IoEyeOutline className="size-5" />
                </Button>
              </div>
            </div>

            <div className="order-1 flex flex-col justify-between sm:flex-row lg:order-2">
              <div className="order-2 flex truncate sm:order-1 sm:gap-2 ">
                {PREVIEW_DOCUMENT_TABS.map((tab) => (
                  <button
                    key={tab}
                    className={cn(
                      "min-w-0  cursor-pointer truncate border-b-4 border-transparent px-2 py-2.5 text-sm font-medium capitalize leading-5 sm:px-5",
                      currentTab === tab &&
                        "text-primary-500 border-primary-500 font-semibold",
                    )}
                    onClick={() => setCurrentTab(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>
              <div className="order-1 flex items-center justify-end gap-3 sm:order-2">
                <Button
                  onClick={() => setIsModalFullscreen(!isModalFullscreen)}
                  variant="primary"
                  className="w-fit rounded-full !px-0"
                >
                  {isModalFullscreen ? (
                    <LuMinimize2 className="size-5" />
                  ) : (
                    <LuMaximize2 className="size-5" />
                  )}
                </Button>
                <button onClick={onClose}>
                  <IoMdClose className="size-6 text-gray-500" />
                </button>
              </div>
            </div>
          </div>
        </Modal.Header>
        <Modal.Body className="grid max-h-[70vh] min-h-[70vh] grid-rows-1 gap-x-2 overflow-hidden border-none p-4 lg:grid-cols-2">
          <div className="hidden overflow-y-auto lg:block">
            {document?.processingStatus === "completed" ? (
              handleRenderPreviewDocument()
            ) : (
              <div className="grid h-full w-full place-content-center text-3xl font-semibold text-gray-400">
                No Document Uploaded
              </div>
            )}
          </div>
          <div className="has-[.previewing]:border-primary-500 dark:has-[.previewing]:border-primary-500 overflow-y-auto rounded-lg border dark:border-gray-400">
            {tabs[currentTab]}
          </div>
        </Modal.Body>
      </Modal>

      <Modal
        theme={{
          content: {
            inner: cn(
              theme.modal.content.inner,
              "fixed inset-0 max-h-full w-full",
            ),
          },
          body: {
            base: cn(theme.modal.body.base, "!max-h-full"),
          },
        }}
        show={isOpenDocument}
        onClose={close}
      >
        <Modal.Header>
          <div className="flex items-center gap-3 sm:gap-5">
            {document?.currentVersion?.fileRecord.extension && (
              <DocumentTypeBadge
                className="text-base"
                type={document.currentVersion.fileRecord.extension}
              />
            )}
            <p className="flex gap-2 sm:gap-3">
              {document?.currentVersion?.fileRecord.extension &&
                DOCUMENT_ICONS[document.currentVersion.fileRecord.extension]}
              <span className="text-sm font-bold">
                {document?.title || "Document Preview"}
              </span>
            </p>
          </div>
        </Modal.Header>
        <Modal.Body className="grid grid-rows-1 overflow-hidden p-4">
          <div className="overflow-y-auto">
            {document?.processingStatus === "completed" ? (
              handleRenderPreviewDocument()
            ) : (
              <div className="grid h-full w-full place-content-center text-3xl font-semibold text-gray-400">
                No Document Uploaded
              </div>
            )}
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default PreviewDocumentModal;
