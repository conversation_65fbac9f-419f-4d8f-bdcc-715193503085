import { X } from "lucide-react";
import React, { useState } from "react";
import { LuUpload } from "react-icons/lu";

import { DocumentViewer } from "@/components/shared/document-viewers/document-viewer";
import { LoadingDocument } from "@/components/shared/document-viewers/loading-document";
import { Button } from "@/components/ui/button";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { EBinderDocumentVersion } from "@/lib/apis/essential-document-versions/types";
import { formatDate } from "@/lib/utils";

import {
  useDocumentVersions,
  useDownloadDocument,
} from "../../hooks/use-isf-queries";
import { generateVersionColumns } from "../columns";
import { AddVersionModal } from "./add-version-modal";

type Props = {
  documentId: string;
};

export const DocumentHistory = ({ documentId }: Props) => {
  const { close, isOpen, open } = useDisclosure();
  const [selectedVersion, setSelectedVersion] =
    useState<EBinderDocumentVersion | null>(null);
  const { data: versions, isPending } = useDocumentVersions(documentId);

  const { data, isPending: isPendingUrl } = useDownloadDocument(
    selectedVersion?.id,
  );
  const columns = generateVersionColumns({
    onView: (version) => setSelectedVersion(version),
  });

  if (selectedVersion)
    return (
      <div className="previewing flex h-full flex-col">
        <div className="flex items-center justify-between border-b border-blue-500 px-4 py-3 sm:px-6 dark:text-white">
          <p>
            <span className="px-2.5">
              {selectedVersion?.versionNumber?.join(".")}
            </span>
            {selectedVersion?.createdDate && (
              <span className="px-2.5">
                {formatDate(selectedVersion?.createdDate)}
              </span>
            )}
          </p>
          <X
            className="size-4 cursor-pointer"
            onClick={() => setSelectedVersion(undefined)}
          />
        </div>
        <div className="flex-1 overflow-y-auto">
          {isPendingUrl ? (
            <LoadingDocument />
          ) : data?.url ? (
            <DocumentViewer
              type={selectedVersion.fileRecord.extension}
              url={data?.url}
            />
          ) : null}
        </div>
      </div>
    );

  return (
    <>
      <div className="space-y-2 p-4">
        <div className="flex justify-end">
          <Button onClick={open} variant="primary">
            <LuUpload className="size-5" />
            Add New Version
          </Button>
        </div>

        <div className="overflow-hidden rounded-lg border">
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <Table columns={columns} data={versions ?? []} />
          )}
        </div>
      </div>
      {isOpen && (
        <AddVersionModal
          documentId={documentId}
          onClose={close}
          isOpen={isOpen}
        />
      )}
    </>
  );
};
