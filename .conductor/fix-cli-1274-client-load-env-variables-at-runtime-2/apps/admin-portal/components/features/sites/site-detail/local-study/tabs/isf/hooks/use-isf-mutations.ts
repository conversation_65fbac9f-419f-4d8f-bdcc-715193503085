import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  EBinderCreatePlaceHolderPayload,
  EBinderMoveDocumentPayload,
  EBinderMultipleUploadPayload,
  EBinderUpdatePlaceholderPayload,
} from "@/lib/apis/essential-document-files/types";
import { EbinderUploadPlaceholderPayload } from "@/lib/apis/essential-document-versions/types";
import {
  CreateFolderPayload,
  MoveFolderPayload,
  UpdateFolderPayload,
} from "@/lib/apis/isf-folders";
import {
  CreateTemplatePayload,
  ImportTemplatePayload,
} from "@/lib/apis/isf-templates";
import { ScaffoldFoldersPayload } from "@/lib/apis/sites";
import { SiteStudyParams } from "@/lib/apis/types";

import { localStudyKeys } from "../../../hooks/use-local-study-queries";
import { useFilterDocuments } from "./use-filter-documents";
import { isfKeys } from "./use-isf-queries";

export const useAddFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateFolderPayload) =>
      api.isfFolders.createFolder(payload),
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: isfKeys.folderList({
          siteId: payload.siteId,
          studyId: payload.studyId,
        }),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Folder created successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to create folder");
    },
  });
};

export const useUpdateFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateFolderPayload & { folderId: string }) =>
      api.isfFolders.updateFolder(payload.folderId, {
        name: payload.name,
        parentId: payload.parentId,
        studyId: payload.studyId,
        siteId: payload.siteId,
      }),
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: isfKeys.folderList({
          siteId: payload.siteId,
          studyId: payload.studyId,
        }),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Folder updated successfully");
    },
    onError: () => {
      toast.error("Failed to update folder");
    },
  });
};

export const useDeleteFolder = ({ siteId, studyId }: SiteStudyParams) => {
  const queryClient = useQueryClient();
  const { folderId, setFolderId } = useFilterDocuments();
  return useMutation({
    mutationFn: (id: string) => api.isfFolders.deleteFolder(id),

    onSuccess: (_, payload) => {
      if (folderId === payload) {
        setFolderId(null);
      }

      return queryClient.invalidateQueries({
        queryKey: isfKeys.folderList({
          siteId,
          studyId,
        }),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Folder deleted successfully");
    },
    onError: (err) => toast.error(err?.message || "Something went wrong!"),
  });
};

export const useMoveFolder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: MoveFolderPayload) => {
      return api.isfFolders.moveFolder(payload);
    },
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: isfKeys.folderList({
          siteId: payload.siteId,
          studyId: payload.studyId,
        }),
      });
    },
    onSettled: (_, err, payload) => {
      if (err) return;
      toast.success(
        payload.isRollback ? "Folder move undone" : "Folder moved successfully",
      );
    },
    onError: (err, payload) => {
      toast.error(
        err?.message || payload.isRollback
          ? "Failed to undo action"
          : "Failed to move folder",
      );
    },
  });
};

export const useCreatePlaceholder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: EBinderCreatePlaceHolderPayload) =>
      api.essentialDocumentFiles.createPlaceHolder(payload),

    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: isfKeys.allFolderLists(),
      });
      return queryClient.invalidateQueries({
        queryKey: isfKeys.allDocumentLists(),
      });
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Placeholder created successfully");
    },

    onError: () => {
      toast.error("Failed to create placeholder");
    },
  });
};

export const useUpdateDocument = (id?: string) => {
  return useMutation({
    mutationFn: (
      payload: EBinderUpdatePlaceholderPayload & {
        hideSuccessNotification?: boolean;
      },
    ) => api.essentialDocumentFiles.updateDocument(payload),
    onSettled: (_, err, payload) => {
      if (!err && !payload.hideSuccessNotification)
        toast.success("Update document successfully!");
    },
    onError: (err) => {
      toast.error(err?.message || "Fail to update document!");
    },
    meta: {
      awaits: [isfKeys.allDocumentLists(), isfKeys.documentDetail(id)],
      // invalidates: id ? [isfKeys.documentStatuses(id)] : [],
    },
  });
};

export const useDeleteDocument = () => {
  return useMutation({
    mutationFn: (id: string) => api.essentialDocumentFiles.deleteDocument(id),
    onSettled: (_, err) => {
      if (!err) toast.success("Update document successfully!");
    },
    onError: (err) => {
      toast.error(err?.message || "Fail to update document!");
    },
    meta: {
      awaits: [isfKeys.allDocumentLists(), isfKeys.allFolderLists()],
    },
  });
};

export const useMoveDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: EBinderMoveDocumentPayload) => {
      return api.essentialDocumentFiles.moveDocument(payload);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: isfKeys.allFolderLists(),
      });
      return queryClient.invalidateQueries({
        queryKey: isfKeys.allDocumentLists(),
      });
    },
    onSettled: (_, err, payload) => {
      if (err) return;
      toast.success(
        payload.isRollback
          ? "Document move undone"
          : "Document moved successfully",
      );
    },
    onError: (err, payload) => {
      toast.error(
        err?.message || payload.isRollback
          ? "Failed to move document"
          : "Failed to undo folder",
      );
    },
  });
};

export const useCreateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateTemplatePayload) => {
      return api.isfTemplates.createTemplate(payload);
    },
    onSuccess: () => {
      return queryClient.invalidateQueries({
        queryKey: isfKeys.allTemplateLists(),
      });
    },
    onSettled: (_, err) => {
      if (err) return;
      toast.success("Create template successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to create template");
    },
  });
};

export const useImportTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: ImportTemplatePayload) => {
      return api.isfTemplates.importTemplate(payload);
    },
    onSuccess: () => {
      return queryClient.invalidateQueries({
        queryKey: isfKeys.all(),
      });
    },
    onSettled: (_, err) => {
      if (err) return;
      toast.success("Import template successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to import template");
    },
  });
};

export const useUploadPlaceholder = () => {
  return useMutation({
    mutationFn: async (payload: EbinderUploadPlaceholderPayload) =>
      api.essentialDocumentVersions.createVersion(payload),
  });
};

export const useUploadMultiple = () => {
  return useMutation({
    mutationFn: async (payload: EBinderMultipleUploadPayload) =>
      api.essentialDocumentFiles.uploadMultiple(payload),
  });
};

export const useScaffoldFolders = () => {
  return useMutation({
    mutationFn: (payload: ScaffoldFoldersPayload) =>
      api.sites.scaffoldFolders(payload),
    onSettled: (_, err) => {
      if (!err) toast.success("Folders scaffolded successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to scaffold folders");
    },
    meta: {
      awaits: [isfKeys.all(), localStudyKeys.all()],
    },
  });
};

export const useCreateSystemFolder = () => {
  return useMutation({
    mutationFn: (payload: ScaffoldFoldersPayload) =>
      api.sites.createSystemFolder(payload),
    onSettled: (_, err) => {
      if (!err) toast.success("System folder created successfully");
    },
    onError: (err) => {
      toast.error(err?.message || "Failed to create system folder");
    },
    meta: {
      awaits: [isfKeys.allFolderLists(), isfKeys.allDocumentLists()],
    },
  });
};
