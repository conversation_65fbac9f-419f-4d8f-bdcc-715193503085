import { useParams } from "next/navigation";
import { parseAsString, useQueryState } from "nuqs";
import { useState } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useDisclosure } from "@/hooks/use-disclosure";

import {
  useCreateTemplate,
  useImportTemplate,
} from "./hooks/use-isf-mutations";
import { useInfiniteTemplates } from "./hooks/use-isf-queries";

const exportSchema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  // selectedCohort: CohortItem | null;
};

export const ExportModal = function ({ isOpen, onClose }: Props) {
  const siteId = useParams()?.id as string;
  const [studyId] = useQueryState("studyId", parseAsString.withDefault(""));
  const { mutateAsync, isPending } = useCreateTemplate();
  const onSubmit = async (data: z.infer<typeof exportSchema>) => {
    await mutateAsync({
      name: data.name,
      siteId,
      studyId,
    });
    onClose();
  };

  return (
    <WrapperModal isOpen={isOpen} onClose={onClose} title="Create Template">
      <Form mode="onChange" schema={exportSchema} onSubmit={onSubmit}>
        <div className="flex flex-col gap-2">
          <Label htmlFor="name">Name</Label>
          <InputField
            id="name"
            name="name"
            placeholder="Enter cohort name..."
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            disabled={isPending}
            isLoading={isPending}
          >
            Create
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};

const importSchema = z.object({
  templateId: z
    .string({
      required_error: "Template is required",
      invalid_type_error: "Template is required",
    })
    .min(1, "Template is required"),
});

export const ImportModal = function ({ isOpen, onClose }: Props) {
  const params = useParams();
  const siteId = params?.id as string;
  const studyId = params?.studyId as string;
  const { mutateAsync, isPending } = useImportTemplate();

  const [templateId, setTemplateId] = useState("");
  const { isOpen: isConfirmOpen, close, open } = useDisclosure();
  const onSubmit = async (data: z.infer<typeof importSchema>) => {
    open();
    setTemplateId(data.templateId);
  };

  const handleClose = () => {
    onClose();
    setTemplateId("");
  };

  const onConfirm = async () => {
    await mutateAsync({
      siteId,
      studyId,
      templateId,
    });
    handleClose();
    close();
  };

  return (
    <>
      <WrapperModal
        isOpen={isOpen}
        onClose={handleClose}
        title="Import Template"
      >
        <Form mode="onChange" schema={importSchema} onSubmit={onSubmit}>
          <div className="flex flex-col gap-2">
            <Label htmlFor="templateId">Template</Label>
            <LazySelect
              id="templateId"
              name="templateId"
              placeholder="Select a template..."
              searchPlaceholder="Search template..."
              useInfiniteQuery={useInfiniteTemplates}
              getOptionLabel={(option) => option.name}
              getOptionValue={(option) => option.id}
              params={[]}
            />
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={handleClose} />
            <Button type="submit" variant="primary">
              Import
            </Button>
          </div>
        </Form>
      </WrapperModal>
      <WrapperModal
        isOpen={isConfirmOpen}
        onClose={close}
        title="Import Template Confirmation"
      >
        <div className="flex flex-col items-center justify-center gap-2.5">
          <span className="text-xl font-medium leading-[150%] dark:text-white">
            Are you sure you want to import the ISF template?
          </span>
        </div>
        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={close} />
          <Button
            onClick={onConfirm}
            isLoading={isPending}
            disabled={isPending}
            type="button"
            variant="primary"
          >
            Confirm
          </Button>
        </div>
      </WrapperModal>
    </>
  );
};
