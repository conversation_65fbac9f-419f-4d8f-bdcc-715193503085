import { skipToken, useQuery } from "@tanstack/react-query";
import { endOfDay, startOfDay } from "date-fns";
import { parseAsBoolean, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

type PatientVisitParams = {
  patientId: string;
  visitId: string;
};

export const patientVisitKeys = {
  all: () => ["patient-visits"] as const,
  allList: (patientId: string) => [
    ...patientVisitKeys.all(),
    "list",
    patientId,
  ],
  list: ({
    params,
    patientId,
  }: {
    params?: MetadataParams;
    patientId: string;
  }) => [...patientVisitKeys.allList(patientId), params],

  visitDetail: (params: PatientVisitParams) => [
    ...patientVisitKeys.all(),
    "detail",
    params,
  ],

  allPatientVisitDocumentList: (patientVisitId: string) =>
    [
      ...patientVisitKeys.all(),
      "visit-documents-list",
      patientVisitId,
    ] as const,
  patientVisitDocumentList: ({
    params,
    patientVisitId,
  }: {
    patientVisitId: string;
    params: MetadataParams;
  }) => [
    ...patientVisitKeys.allPatientVisitDocumentList(patientVisitId),
    params,
  ],

  downloadedVisitDocument: (id: string) => [
    ...patientVisitKeys.all(),
    "downloaded-visit-document",
    id,
  ],
};

export const usePatientVisits = ({
  patientId,
  ignoreFilters,
}: {
  patientId: string;
  ignoreFilters?: boolean;
}) => {
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const params = ignoreFilters
    ? {}
    : {
        page,
        take,
        orderBy: orderBy || undefined,
        orderDirection: orderDirection || undefined,
      };
  return useQuery({
    queryKey: patientVisitKeys.list({
      params,
      patientId,
    }),
    queryFn: () => api.patients.patientVisits(patientId, params),
    placeholderData: (prev) => prev,
  });
};

export const usePatientVisit = ({
  patientId,
  visitId,
}: {
  patientId: string;
  visitId?: string;
}) => {
  return useQuery({
    queryKey: visitId
      ? patientVisitKeys.visitDetail({
          patientId,
          visitId,
        })
      : [],
    queryFn: visitId
      ? () => api.patients.patientVisit(patientId, visitId)
      : skipToken,
  });
};

export const usePatientVisitDocuments = (patientVisitId?: string) => {
  const { search } = useSearch();
  const [showArchived] = useQueryState(
    "showArchived",
    parseAsBoolean.withDefault(false),
  );

  const [documentType] = useQueryState("extension");
  const [documentStatus] = useQueryState("status");
  const [createdFrom] = useQueryState("fromCreatedDate");
  const [createdTo] = useQueryState("toCreatedDate");
  const params = {
    filter: {
      title: search,
      showArchived,
      extension: documentType,
      status: documentStatus,
      fromCreatedDate: createdFrom
        ? startOfDay(new Date(createdFrom)).toISOString()
        : undefined,
      toCreatedDate: createdTo
        ? endOfDay(new Date(createdTo)).toISOString()
        : undefined,
    },
  };
  return useQuery({
    queryKey: patientVisitId
      ? patientVisitKeys.patientVisitDocumentList({
          patientVisitId,
          params,
        })
      : [],
    queryFn: patientVisitId
      ? () => api.patientVisits.patientVisitDocuments(patientVisitId, params)
      : skipToken,
  });
};

export const useDownloadVisitDocument = (id: string) =>
  useQuery({
    queryKey: patientVisitKeys.downloadedVisitDocument(id),
    queryFn: () => api.visitDocuments.signedUrl(id),
  });
