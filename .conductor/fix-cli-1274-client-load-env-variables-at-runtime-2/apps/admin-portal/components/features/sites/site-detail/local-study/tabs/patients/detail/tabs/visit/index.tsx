import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Table, TableLoading } from "@/components/ui/table";

import { useLocalStudyParams } from "../../../../../hooks/useLocalStudyParams";
import { generateColumnsVisit } from "./columns";
import { usePatientVisits } from "./hook/use-visit-queries";
import { VisitModal } from "./visit-modal";

export const VisitTab = () => {
  const { patientId } = useLocalStudyParams();
  const { data, isPending, isPlaceholderData } = usePatientVisits({
    patientId,
  });
  const [selectedVisitId, setSelectedVisitId] = useState("");
  const columns = useMemo(
    () =>
      generateColumnsVisit({
        onView: (visit) => {
          setSelectedVisitId(visit.id);
        },
      }),
    [],
  );

  const handleCloseVisitModal = () => {
    setSelectedVisitId("");
  };
  return (
    <>
      <Card className="[&>div]:p-0">
        <h2 className="p-4 text-2xl font-semibold dark:text-white">Visit</h2>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table
              columns={columns}
              enableSorting
              data={
                data?.results
                  ? data.results.filter((visit) => visit.isVisit)
                  : []
              }
            />
            {/* {data?.metadata && <TableDataPagination metadata={data.metadata} />} */}
          </LoadingWrapper>
        )}
      </Card>
      <VisitModal
        isOpen={!!selectedVisitId}
        onClose={handleCloseVisitModal}
        visitId={selectedVisitId}
      />
    </>
  );
};
