import { useMutation, useQueryClient } from "@tanstack/react-query";
import { endOfDay, parse, startOfDay } from "date-fns";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { LocalStudyParams } from "@/lib/apis/sites";
import { CreateStudyTaskPayload } from "@/lib/apis/studies";
import {
  TaskListResponse,
  UpdateAssignedUserPayload,
  UpdateTaskPayload,
} from "@/lib/apis/tasks";
import { TAKE_ALL } from "@/lib/constants";

import { MoveTaskPayload } from "../kanban";
import { useTaskFilter } from "./use-task-filter";
import { localStudyTaskKeys } from "./use-tasks-queries";

export const useCreateTask = (localStudyParams: LocalStudyParams) =>
  useMutation({
    mutationFn: (payload: Omit<CreateStudyTaskPayload, "siteId" | "studyId">) =>
      api.studies.createTask({
        ...payload,
        ...localStudyParams,
      }),
    onSettled: (_, err) => !err && toast.success("Create task successfully"),
    onError: (err) => toast.error(err?.message || "Fail to create task"),
    meta: {
      awaits: localStudyTaskKeys.allList(localStudyParams.studyId),
    },
  });

export const useUpdateTask = (params: { taskId: string; studyId: string }) =>
  useMutation({
    mutationFn: (payload: Omit<UpdateTaskPayload, "siteId" | "studyId">) =>
      api.tasks.updateTask({
        ...payload,
        ...params,
      }),
    onSettled: (_, err) => !err && toast.success("Update task successfully"),
    onError: (err) => toast.error(err?.message || "Fail to update task"),
    meta: {
      awaits: [
        localStudyTaskKeys.allList(params.studyId),
        localStudyTaskKeys.detail(params.taskId),
      ],
    },
  });

export const useDeleteTask = (localStudyParams: LocalStudyParams) =>
  useMutation({
    mutationFn: (id: string) => api.tasks.delete(id),
    onSettled: (_, err) => !err && toast.success("Delete task successfully"),
    onError: (err) => toast.error(err?.message || "Fail to delete task"),
    meta: {
      awaits: localStudyTaskKeys.allList(localStudyParams.studyId),
    },
  });

export const useReassignTask = (params: { taskId: string; studyId: string }) =>
  useMutation({
    mutationFn: (payload: Omit<UpdateAssignedUserPayload, "taskId">) =>
      api.tasks.updateAssignedUser({
        ...payload,
        taskId: params.taskId,
      }),
    onSettled: (_, err) => !err && toast.success("Reassign task successfully"),
    onError: (err) => toast.error(err?.message || "Fail to reassign task"),
    meta: {
      awaits: [
        localStudyTaskKeys.allList(params.studyId),
        localStudyTaskKeys.detail(params.taskId),
      ],
    },
  });

const MOVE_TASK_KEY = ["use-move-task"];

export const useMoveTask = (params: { taskId: string; studyId: string }) => {
  const queryClient = useQueryClient();
  const { search, priority, orderBy, orderDirection, from, to } =
    useTaskFilter();

  const fromDueDate = from
    ? startOfDay(parse(from, "M/d/yyyy", new Date())).toISOString()
    : undefined;
  const toDueDate = to
    ? endOfDay(parse(to, "M/d/yyyy", new Date())).toISOString()
    : undefined;

  const generateFilterParams = (status: string) => ({
    take: TAKE_ALL,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      name: search,
      priority: priority || undefined,
      fromDueDate,
      toDueDate,
      status,
    },
  });

  return useMutation({
    mutationFn: (payload: MoveTaskPayload) =>
      api.tasks.updateTask({
        ...payload.task,
        status: payload.newStatus,
        ...params,
      }),
    onMutate: async (payload) => {
      const queryKeyTarget = localStudyTaskKeys.list(
        params.studyId,
        generateFilterParams(payload.newStatus),
      );

      const queryKeyCurrent = localStudyTaskKeys.list(
        params.studyId,
        generateFilterParams(payload.task.status),
      );

      await queryClient.cancelQueries({ queryKey: queryKeyTarget });
      await queryClient.cancelQueries({ queryKey: queryKeyCurrent });

      const targetSnapshot = queryClient.getQueryData(queryKeyTarget);
      const currentSnapshot = queryClient.getQueryData(queryKeyCurrent);

      queryClient.setQueryData(
        queryKeyTarget,
        (old: TaskListResponse | undefined) => {
          if (!old) return undefined;
          return {
            ...old,
            results: [
              ...old.results,
              {
                ...payload.task,
                status: payload.newStatus,
              },
            ],
          };
        },
      );
      queryClient.setQueryData(
        queryKeyCurrent,
        (old: TaskListResponse | undefined) => {
          if (!old) return undefined;
          return {
            ...old,
            results: old.results.filter((task) => task.id !== payload.task.id),
          };
        },
      );
      toast.success("Update task status successfully");
      return () => {
        queryClient.setQueryData(queryKeyTarget, targetSnapshot);
        queryClient.setQueryData(queryKeyCurrent, currentSnapshot);
      };
    },
    onError: (err, _, rollback) => {
      rollback?.();
      toast.error(err?.message || "Fail to update task status");
    },
    onSettled: async () => {
      if (
        queryClient.isMutating({
          mutationKey: MOVE_TASK_KEY,
        }) === 1
      ) {
        queryClient.invalidateQueries({
          queryKey: localStudyTaskKeys.allList(params.studyId),
        });
        queryClient.invalidateQueries({
          queryKey: localStudyTaskKeys.detail(params.taskId),
        });
      }
    },
    mutationKey: MOVE_TASK_KEY,
  });
};
