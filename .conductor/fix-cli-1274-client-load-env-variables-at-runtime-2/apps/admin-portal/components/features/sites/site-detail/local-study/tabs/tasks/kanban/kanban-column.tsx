import { useDroppable } from "@dnd-kit/core";
import { CiWarning } from "react-icons/ci";
import { FaPlus } from "react-icons/fa6";
import { VscMove } from "react-icons/vsc";

import {
  TASK_STATUSES,
  TaskStatus,
} from "@/components/ui/badges/task-status-badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn, createRandomArray } from "@/lib/utils";

import { useLocalStudyParams } from "../../../hooks/useLocalStudyParams";
import { useTaskModals } from "../hooks/use-task-modals-store";
import { useStudyTasksByStatus } from "../hooks/use-tasks-queries";
import { KanbanTask, KanbanTaskSkeleton } from "./kanban-task";

type Props = {
  label: string;
  status: TaskStatus;
};

const BORDER_COLORS = {
  notStarted: "border-teal-400 ring-teal-400",
  inProgress: "border-green-400 ring-green-400",
  completed: "border-purple-400 ring-purple-400",
  cancelled: "border-red-400 ring-red-400",
} as const;

const KANBAN_TOTAL_LABEL_COLORS = {
  notStarted: "bg-teal-400",
  inProgress: "bg-green-400",
  completed: "bg-purple-400",
  cancelled: "bg-red-500",
} as const;

export const KanbanColumn = ({ label, status }: Props) => {
  const { onOpenTaskModal } = useTaskModals();
  const { studyId } = useLocalStudyParams();
  const { data, isPending } = useStudyTasksByStatus({
    status,
    studyId,
  });
  const { setNodeRef, isOver, active, over } = useDroppable({
    id: status,
  });

  const lastStatusIndex = TASK_STATUSES.length - 1;
  const overStatusIndex = TASK_STATUSES.findIndex((s) => s.value === over?.id);
  const activeStatusIndex = TASK_STATUSES.findIndex(
    (s) => s.value === active?.id.toString().split("_")[0],
  );

  const isSameColumn = active?.id.toString().includes(status);
  const validColumn =
    overStatusIndex === lastStatusIndex ||
    overStatusIndex === activeStatusIndex + 1;
  const handleRenderTasks = () => {
    if (isPending)
      return createRandomArray().map((idx) => <KanbanTaskSkeleton key={idx} />);
    if (data?.results.length)
      return data?.results.map((task) => (
        <KanbanTask task={task} key={task.id} />
      ));

    return (
      <span className="text-center text-sm font-medium text-black/50 dark:text-gray-400">
        No tasks
      </span>
    );
  };

  return (
    <div id={status} ref={setNodeRef} className="relative transition-all">
      <div
        className={cn(
          " flex h-fit flex-col gap-4 rounded-lg border p-3 transition-all",
          BORDER_COLORS[status],
        )}
      >
        <div className="flex items-center gap-4 font-semibold">
          <span className="dark:text-white">{label}</span>

          {isPending ? (
            <Skeleton className="size-5 rounded-full" />
          ) : (
            <span
              className={cn(
                "grid size-5 place-content-center rounded-full p-1 text-white",
                KANBAN_TOTAL_LABEL_COLORS[status],
              )}
            >
              {data?.results.length}
            </span>
          )}
        </div>
        <div className="flex flex-1 flex-col gap-4 overflow-y-auto">
          {handleRenderTasks()}
        </div>
        <button
          onClick={() =>
            onOpenTaskModal({
              status,
            })
          }
          className=" flex w-full justify-center rounded border py-1.5 text-gray-500 transition-all hover:bg-gray-100/55
           hover:text-black dark:border-gray-400 dark:text-gray-500 dark:hover:bg-gray-900 dark:hover:text-white"
        >
          <FaPlus className="size-5 " />
        </button>
      </div>
      {isOver && !isSameColumn && (
        <div
          className={cn(
            "absolute inset-0 rounded-lg border-2 bg-gray-100/75 ring-2",
            validColumn ? BORDER_COLORS[status] : "border-red-500 bg-red-100",
          )}
        >
          {validColumn ? (
            <div className="flex h-full w-full flex-col items-center justify-center">
              <div className="text-sm">
                Drop here to move task to this board
              </div>
              <VscMove className="mt-4 text-4xl" />
            </div>
          ) : (
            <div className="flex h-full w-full flex-col items-center justify-center">
              <div className="text-sm">Task cannot be dropped here</div>
              <CiWarning className="mt-4 text-4xl text-red-500" />
            </div>
          )}
        </div>
      )}
    </div>
  );
};
