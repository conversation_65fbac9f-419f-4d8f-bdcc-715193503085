import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { useInfiniteAssignableUsers } from "@/hooks/queries/use-infinite-assignable-users";

import { useLocalStudyParams } from "../../hooks/useLocalStudyParams";
import { useTaskModals } from "./hooks/use-task-modals-store";
import { useReassignTask } from "./hooks/use-task-mutations";
import { useStudyTask } from "./hooks/use-tasks-queries";

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

const schema = z.object({
  assignedToProfileId: z
    .string({
      required_error: "Assignee is required",
      invalid_type_error: "Assignee is required",
    })
    .min(1, "Assignee is required"),
});

type FormValues = z.infer<typeof schema>;

export const ReassignTaskModal = ({ isOpen, onClose }: Props) => {
  const { studyId } = useLocalStudyParams();

  const { isOpenTaskDetailModal, selectedTaskId } = useTaskModals();

  const { data } = useStudyTask({
    enabled: isOpenTaskDetailModal,
    taskId: selectedTaskId,
  });
  const { mutateAsync, isPending } = useReassignTask({
    studyId,
    taskId: data?.id as string,
  });

  const onSubmit = async (data: FormValues) => {
    await mutateAsync(data);
    onClose();
  };
  return (
    <WrapperModal
      size="lg"
      title="Reassign task"
      isOpen={isOpen}
      onClose={onClose}
    >
      <Form
        onSubmit={onSubmit}
        schema={schema}
        className="space-y-4"
        defaultValues={{
          assignedToProfileId: data?.assignedTo?.profile?.id,
        }}
      >
        <div className="space-y-2">
          <Label htmlFor="assignedToProfileId">Assigned To</Label>
          <LazySelect
            placeholder="Select assignee"
            name="assignedToProfileId"
            id="assignedToProfileId"
            useInfiniteQuery={useInfiniteAssignableUsers}
            getOptionLabel={(option) =>
              `${option.user.firstName} ${option.user.lastName}`
            }
            getOptionValue={(user) => user.id}
            params={[studyId]}
          />
        </div>

        <div className="flex flex-col justify-end gap-4 border-none pt-0 sm:col-span-2 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" isLoading={isPending} variant="primary">
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
