import { functionalUpdate } from "@tanstack/react-table";
import React, { useMemo, useState } from "react";

import { DOCUMENT_ICONS, DocumentType } from "@/components/icons/doc-icons";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { DocumentTypeBadge } from "@/components/ui/badges/document-type-badge";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { DocExchangeDocument } from "@/lib/apis/doc-exchange/types";

import {
  useDeleteDocument,
  useUnarchiveDocument,
} from "../hooks/use-doc-exchange-mutations";
import { useDocuments } from "../hooks/use-doc-exchange-queries";
import { useFilterDocuments } from "../hooks/use-filter-documents";
import { UpdateDocumentModal } from "../update-document-modal";
import AuditModal from "./audit-modal";
import { generateDocumentColumns } from "./columns";

type Props = {
  draggingFile: DocExchangeDocument | null;
  isMovingDocument: boolean;
};

export const DocumentsTable = ({ draggingFile, isMovingDocument }: Props) => {
  const { orderBy, orderDirection, changeSort, siteId } = useFilterDocuments();
  const {
    isOpen: isAuditOpen,
    open: openAudit,
    close: closeAudit,
  } = useDisclosure();

  // const {
  //   isOpen: isPreviewOpen,
  //   open: openPreview,
  //   close: closePreview,
  // } = useDisclosure();

  const {
    isOpen: isConfirmOpen,
    open: openConfirm,
    close: closeConfirm,
  } = useDisclosure();

  const {
    isOpen: isUpdateOpen,
    open: openUpdate,
    close: closeUpdate,
  } = useDisclosure();
  const [selectedDocument, setSelectedDocument] =
    useState<DocExchangeDocument | null>(null);
  const [selectedDocumentId, setSelectedDocumentId] = useState("");
  const { data, isPending, isPlaceholderData } = useDocuments();
  const {
    mutateAsync: deleteDocument,
    isPending: isDeleting,
    isSuccess: isDeleted,
    reset,
  } = useDeleteDocument();

  const { mutateAsync: unDeleteDocument, isPending: isUnDeleting } =
    useUnarchiveDocument(siteId);

  const columns = useMemo(
    () =>
      generateDocumentColumns({
        // onPreview: (document) => {
        //   openPreview();
        //   setSelectedDocument(document);
        // },
        onEdit: (document) => {
          setSelectedDocument(document);
          openUpdate();
        },
        onViewAuditLogs: (document) => {
          openAudit();
          setSelectedDocumentId(document.id);
        },
        onDelete: (document) => {
          openConfirm();
          setSelectedDocument(document);
        },
      }),
    [openUpdate, openAudit, openConfirm],
  );

  // const handleClosePreview = () => {
  //   closePreview();
  //   setSelectedDocument(null);
  // };
  const handleCloseUpdateModalModal = () => {
    setSelectedDocument(null);
    closeUpdate();
  };

  const handleCloseAudit = () => {
    closeAudit();
    setSelectedDocumentId("");
  };

  const handleCloseConfirm = () => {
    setSelectedDocument(null);
    closeConfirm();
    reset();
  };

  const handleConfirm = async () => {
    if (!selectedDocument) return;
    await deleteDocument({
      id: selectedDocument.id,
      isHideToast: true,
    });
  };

  const handleUnDelete = async () => {
    if (!selectedDocument) return;
    await unDeleteDocument(selectedDocument.id);
    handleCloseConfirm();
  };
  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];

  return (
    <>
      <div className="flex-1 overflow-hidden rounded-lg border border-gray-300 dark:border-gray-500">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper
            isLoading={isMovingDocument || isPlaceholderData || isDeleting}
          >
            <Table
              columns={columns}
              data={data?.results ?? []}
              draggingRowId={draggingFile?.id || ""}
              enableSorting
              sorting={sorting}
              onSortingChange={(updater) => {
                const newSorting = functionalUpdate(updater, sorting);
                const sort = newSorting[0];
                if (sort)
                  return changeSort(sort.id, sort.desc ? "desc" : "asc");
                changeSort();
              }}
            />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>
      <AuditModal
        documentId={selectedDocumentId}
        isOpen={isAuditOpen}
        onClose={handleCloseAudit}
      />
      {/* {isPreviewOpen && selectedDocument && (
        <DocumentPreviewModal
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
          document={{
            id: selectedDocument?.id,
            title: selectedDocument?.title,
            extension: selectedDocument?.extension,
          }}
          fetchPreviewUrl={(id) => api.docExchange.signUrlDownload(id)}
        />
      )} */}
      {!!isUpdateOpen && (
        <UpdateDocumentModal
          selectedFile={selectedDocument as DocExchangeDocument}
          isOpen={isUpdateOpen}
          onClose={handleCloseUpdateModalModal}
        />
      )}
      <ConfirmModal
        onConfirm={isDeleted ? handleUnDelete : handleConfirm}
        isOpen={isConfirmOpen}
        onClose={handleCloseConfirm}
        isLoading={isDeleting || isUnDeleting}
        confirmLabel={isDeleted ? "Undo" : "Delete"}
        title="Delete Document"
        size="md"
      >
        {isDeleted ? (
          <span className="dark:text-white">Document Deleted</span>
        ) : (
          <div className="space-y-2">
            {selectedDocument && (
              <div className="flex items-center gap-4 rounded-lg border border-red-500 px-4 py-3 dark:text-white">
                <DocumentTypeBadge
                  className="text-base"
                  type={selectedDocument?.fileRecord.extension}
                />
                <p className="flex gap-2 sm:gap-3">
                  {
                    DOCUMENT_ICONS[
                      selectedDocument?.fileRecord.extension as DocumentType
                    ]
                  }
                  <span className="font-medium">{selectedDocument?.title}</span>
                </p>
              </div>
            )}
            <p className="dark:text-white">
              Are you sure you want to delete this document?
            </p>
          </div>
        )}
      </ConfirmModal>
    </>
  );
};
