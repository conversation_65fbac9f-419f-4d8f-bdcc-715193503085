import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { FileDropzone } from "@/components/ui/form/file-drop-zone";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { ALLOWED_FILE_TYPES, MAX_FILE_SIZE } from "@/lib/constants";

import {
  useUpdateFilesStatus,
  useUploadMultiple,
} from "../hooks/use-doc-exchange-mutations";
import { docExchangeKeys } from "../hooks/use-doc-exchange-queries";
import { useFilterDocuments } from "../hooks/use-filter-documents";

const schema = z.object({
  files: z
    .array(
      z
        .instanceof(File)
        .refine(
          (file) => {
            const splittedName = file.name.split(".");
            const extension = splittedName[splittedName.length - 1];
            return ALLOWED_FILE_TYPES.includes(`.${extension}`);
          },
          { message: "Invalid document file type" },
        )
        .refine((file) => file.size <= MAX_FILE_SIZE, {
          message: "File size should not exceed 5MB",
        }),
    )
    .min(1),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  onCreatePlaceHolder: () => void;
  path: string;
};

export const UploadDocumentModal = function ({ isOpen, onClose, path }: Props) {
  const queryClient = useQueryClient();
  const { siteId } = useFilterDocuments();
  const [isManualUploading, setIsManualUploading] = useState(false);

  const { mutateAsync: uploadMultiple, isPending: isUploadingMultiple } =
    useUploadMultiple();
  const { mutateAsync: updateStatus } = useUpdateFilesStatus();
  const onSubmit = async (data: z.infer<typeof schema>) => {
    const formattedFiles = data.files.map((file) => ({
      fileName: path ? `${path.slice(1)}/${file.name}` : file.name,
      contentType: file.type,
      originationType: "web",
      file,
    }));
    try {
      setIsManualUploading(true);
      const uploadResult = await uploadMultiple({
        siteId,
        files: formattedFiles.map(({ file, ...rest }) => ({
          ...rest,
        })),
      });

      if (uploadResult) {
        const entries = Object.entries(uploadResult);
        entries.forEach(async ([name, { url, fileId }]) => {
          const file = formattedFiles.find((file) => file.fileName === name)
            ?.file as File;
          await fetch(url, {
            method: "PUT",
            body: file,
            headers: {
              "Content-Type": file.type,
            },
          });
        });
        await updateStatus({
          files: entries.map(([, { fileId }]) => ({
            fileId,
            status: "completed",
          })),
        });
        queryClient.invalidateQueries({
          queryKey: docExchangeKeys.allDocumentLists(),
        });
        queryClient.invalidateQueries({
          queryKey: docExchangeKeys.folderList(siteId),
        });
        toast.success("New document uploaded successfully");
        onClose();
      } else {
        toast.error("Failed to upload document");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Error uploading document");
      setIsManualUploading(false);
    } finally {
      setIsManualUploading(false);
    }
  };

  return (
    <WrapperModal
      size="3xl"
      isOpen={isOpen}
      onClose={onClose}
      title={`Upload Document`}
    >
      <Form
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
        className="space-y-4"
      >
        <div className="space-y-2">
          <Label htmlFor="files">Document</Label>
          <FileDropzone
            name="files"
            multiple
            acceptTypes={ALLOWED_FILE_TYPES}
          />
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            variant="primary"
            isLoading={isUploadingMultiple || isManualUploading}
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
