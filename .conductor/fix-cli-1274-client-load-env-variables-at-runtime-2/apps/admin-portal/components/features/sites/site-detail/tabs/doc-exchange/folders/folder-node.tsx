import { useDraggable, useDroppable } from "@dnd-kit/core";
import {
  ChevronDown,
  ChevronRight,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon,
  MoreVertical,
} from "lucide-react";
import { parseAsString, useQueryState } from "nuqs";
import React, { MouseEvent } from "react";
import { CiLock } from "react-icons/ci";

import {
  Dropdown,
  DropdownContent,
  DropdownItem,
  DropdownSeparator,
  DropdownTrigger,
} from "@/components/ui/dropdown";
import { useDisclosure } from "@/hooks/use-disclosure";
import {
  DocExchangeDocument,
  DocExchangeFolder,
} from "@/lib/apis/doc-exchange/types";
import { cn } from "@/lib/utils";

export type TreeNodeProps = {
  folder: DocExchangeFolder;
  activePath: string;
  setActivePath: React.Dispatch<React.SetStateAction<string>>;
  onEdit: (folder: DocExchangeFolder) => void;
  activeId?: string | null;
  draggingFolder: DocExchangeFolder | null;
  onDelete: (data: DocExchangeFolder) => void;
  level: number;
  path: string;
  rootPath: string;
  draggingFile: DocExchangeDocument | null;
  indent?: number;
};

export type FolderWithPath = DocExchangeFolder & {
  path: string;
};

const DEFAULT_INDENT = 25;

export const FolderNode = ({
  draggingFolder,
  activePath,
  setActivePath,
  onDelete,
  onEdit,
  folder,
  level,
  path,
  rootPath,
  draggingFile,
  indent = DEFAULT_INDENT,
}: TreeNodeProps) => {
  const [folderIdParam, setFolderIdParam] = useQueryState(
    "folderId",
    parseAsString,
  );

  const isCurrent = folder.id === folderIdParam;

  const {
    isOpen: isOpenDropDown,
    close: onCloseDropDown,
    toggle: onToggleDropDown,
  } = useDisclosure();

  const { isOpen: isOpenFolder, toggle: onToggleFolder } = useDisclosure();

  const {
    attributes,
    listeners,
    setNodeRef: setDragRef,
    isDragging: isDraggingNode,
  } = useDraggable({
    id: folder.id,
    data: { ...folder, path },
  });

  const {
    setNodeRef: setDropRef,
    isOver,
    node,
  } = useDroppable({
    id: folder.id,
    data: { ...folder, path },
  });

  const isValidDropFolder =
    draggingFolder && isOver
      ? folder.id !== draggingFolder.id &&
        draggingFolder.parentDirectoryId !== folder.id &&
        !node.current?.dataset.path?.includes(draggingFolder.name)
      : null;

  const isValidDropFile =
    draggingFile && isOver
      ? folder.id !== draggingFile?.parentDirectoryId
      : null;
  const handleFolderClick = () => {
    setActivePath(path);
    onToggleFolder();
    setFolderIdParam(folder.id);
  };

  return (
    <>
      <div
        ref={(node) => {
          setDragRef(node);
          setDropRef(node);
        }}
        className={cn(
          "group flex w-full cursor-pointer items-center justify-between whitespace-nowrap border border-transparent px-3 py-1.5 transition-colors hover:relative hover:border-purple-500",
          activePath.startsWith(rootPath) && "bg-gray-200 dark:bg-[#1a2b3c]",
          isCurrent ? "text-purple-700" : "text-black dark:text-white",
          isDraggingNode && "opacity-50",
          typeof isValidDropFolder === "boolean"
            ? isValidDropFolder
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
          typeof isValidDropFile === "boolean"
            ? isValidDropFile
              ? "border-dashed border-purple-500 bg-purple-50 dark:bg-[#1a2b3c]"
              : "border-dashed border-red-500 !bg-red-300/45"
            : "",
        )}
        data-path={path}
        style={{
          paddingLeft: level * indent,
        }}
        onClick={handleFolderClick}
        {...attributes}
        {...listeners}
      >
        <div className="flex min-w-0 flex-1 items-center justify-between gap-x-1 pl-4">
          <div className="flex min-w-0 flex-1 items-center gap-2.5">
            <div className="flex items-center">
              {/* DocExchangeFolder toggle icon */}

              <div className="flex w-5 items-center">
                {!!folder.subfolders.length && (
                  <>
                    {isOpenFolder ? (
                      <ChevronDown
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    ) : (
                      <ChevronRight
                        className={cn(
                          "size-4",
                          isCurrent
                            ? "text-purple-700"
                            : "text-black dark:text-white",
                        )}
                      />
                    )}
                  </>
                )}
              </div>

              {/* DocExchangeFolder icon */}
              <div className="flex w-5 items-center">
                {isOpenFolder ? (
                  <FolderOpenIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                ) : (
                  <FolderIcon
                    className={cn(
                      "h-5 w-5",
                      isCurrent
                        ? "text-purple-700"
                        : "text-black dark:text-white",
                    )}
                  />
                )}
              </div>
            </div>

            {/* DocExchangeFolder name */}
            <span className="flex-1 select-none truncate text-sm font-medium leading-5">
              {folder.name}
            </span>

            {/* File count badge */}
            {typeof folder.fileCount === "number" && (
              <span
                className={cn(
                  "font-plus-jakarta ml-2 flex  select-none justify-center rounded-full px-2 py-0.5 text-center text-sm font-medium tracking-[0.28px]",
                  isCurrent
                    ? "bg-purple-500 text-white"
                    : "text-primary-500 bg-gray-300",
                )}
              >
                {folder.fileCount}
              </span>
            )}
          </div>

          {folder.isEditable ? (
            <div
              onClick={(e: MouseEvent) => {
                e.stopPropagation();
              }}
            >
              <Dropdown open={isOpenDropDown} onOpenChange={onToggleDropDown}>
                <DropdownTrigger>
                  <div className="flex items-center rounded p-1 hover:bg-gray-100">
                    <MoreVertical size={20} className="text-gray-500" />
                  </div>
                </DropdownTrigger>
                <DropdownContent>
                  <DropdownItem
                    onClick={() => {
                      onEdit(folder);
                      onCloseDropDown();
                    }}
                  >
                    <span>Edit</span>
                  </DropdownItem>
                  <DropdownSeparator />
                  <DropdownItem
                    onClick={() => {
                      onDelete(folder);
                      onCloseDropDown();
                    }}
                    className="text-red-700"
                  >
                    <span>Delete</span>
                  </DropdownItem>
                </DropdownContent>
              </Dropdown>
            </div>
          ) : (
            <div className="p-1">
              <CiLock size={20} className="text-gray-500" />
            </div>
          )}
        </div>
      </div>

      {isOpenFolder &&
        !!folder.subfolders.length &&
        folder.subfolders.map((sub) => (
          <FolderNode
            key={sub.id}
            folder={sub}
            level={level + 1}
            draggingFolder={draggingFolder}
            draggingFile={draggingFile}
            path={`${path}/${sub.name}`}
            activePath={activePath}
            setActivePath={setActivePath}
            onDelete={onDelete}
            onEdit={onEdit}
            rootPath={rootPath}
            indent={indent}
          />
        ))}
    </>
  );
};
