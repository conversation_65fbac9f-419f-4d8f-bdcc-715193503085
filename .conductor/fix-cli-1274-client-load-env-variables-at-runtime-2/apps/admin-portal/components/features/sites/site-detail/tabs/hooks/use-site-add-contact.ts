import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddSiteContactPayload } from "@/lib/apis/sites";

import { USE_SITE_CONTACTS_QUERY_KEY } from "./use-site-contacts";

export const useSiteAddContact = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: AddSiteContactPayload & { siteId: string }) =>
      api.sites.addContact(data.siteId, data),
    onSuccess: (_, { siteId }) => {
      toast.success("Contact added successfully");
      queryClient.invalidateQueries({
        queryKey: [USE_SITE_CONTACTS_QUERY_KEY, siteId],
      });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
