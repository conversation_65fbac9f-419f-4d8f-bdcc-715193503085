import { ColumnDef } from "@tanstack/react-table";
import <PERSON> from "next/link";
import { GrStatusGood } from "react-icons/gr";
import { IoDocumentOutline } from "react-icons/io5";
import { MdBlock } from "react-icons/md";

import { customPermissionColumn } from "@/components/features/users/user-detail/tabs/profiles/columns";
import {
  TableEditButton,
  TableGenericButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { GroupProfile } from "@/lib/apis/groups/types";
import { capitalize } from "@/utils/string";

export const generateUserColumns = ({
  onToggleStatus,
  onEdit,
  onViewStudies,
}: {
  onToggleStatus: (user: GroupProfile) => void;
  onEdit: (user: GroupProfile) => void;
  onViewStudies: (user: GroupProfile) => void;
}): ColumnDef<GroupProfile>[] => [
  {
    header: "First Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/users/${row.original.profile.userId}`}
      >
        {row.original.profile.user.firstName}
      </Link>
    ),
  },
  {
    header: "Last Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/users/${row.original.profile.userId}`}
      >
        {row.original.profile.user.lastName}
      </Link>
    ),
  },
  {
    header: "Status",
    cell: ({ row }) => (
      <PillBadge
        variant={row.original?.profile.isActive ? "success" : "default"}
      >
        {row.original?.profile.isActive ? "Active" : "Inactive"}
      </PillBadge>
    ),
  },
  {
    header: "Role",
    cell: ({ row }) =>
      row.original.profile.rolesProfiles?.[0]?.role.name
        ? `${row.original.profile.rolesProfiles?.[0]?.role.name} (${capitalize(row.original.profile.rolesProfiles?.[0]?.role.type)})`
        : "N/A",
  },
  {
    header: "Custom Permissions",
    cell: ({ row }) => {
      const value = `${row.original.profile.hasCustomPermissions}`;
      return customPermissionColumn[
        value as keyof typeof customPermissionColumn
      ];
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex items-center gap-2">
          <TableGenericButton
            type="button"
            onClick={() => {
              onToggleStatus(user);
            }}
            className={
              user.profile.isActive
                ? "text-red-500 hover:text-red-600"
                : "text-green-500 hover:text-green-600"
            }
          >
            {user.profile.isActive ? "Disable" : "Enable"}
            {user.profile.isActive ? <MdBlock /> : <GrStatusGood />}
          </TableGenericButton>
          <TableEditButton
            type="button"
            onClick={() => {
              onEdit(user);
            }}
          />
          <TableGenericButton
            type="button"
            onClick={() => {
              onViewStudies(user);
            }}
            className="text-primary-500 hover:text-primary-600 flex items-center gap-x-1"
          >
            Studies <IoDocumentOutline className="size-4" />
          </TableGenericButton>
        </div>
      );
    },
  },
];
