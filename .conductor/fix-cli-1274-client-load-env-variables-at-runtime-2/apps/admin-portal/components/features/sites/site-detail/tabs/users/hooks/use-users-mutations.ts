import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import { AddUserToGroupPayload } from "@/lib/apis/groups/types";
import { ChangeUserProfileStatusPayload } from "@/lib/apis/users/types";

import { userKeys } from "./use-users-queries";

export const useUpdateProfileStatus = () => {
  return useMutation({
    mutationFn: (payload: ChangeUserProfileStatusPayload) => {
      return api.users.changeProfileStatus(payload);
    },
    onError: (err) => {
      toast.error(err?.message || "Fail to update status");
    },
    onSettled: (_, err, payload) => {
      !err &&
        toast.success(
          `${payload.isActive ? "Enable" : "Disable"} user successfully`,
        );
    },
    meta: {
      awaits: userKeys.allLists(),
    },
  });
};

export const useAddUserCustomPermission = () => {
  return useMutation({
    mutationFn: (payload: {
      profileId: string;
      studyId: string;
      userId: string;
    }) => {
      return api.users.addUserCustomPermission(payload);
    },
    onSettled: (_, err) => !err && toast.success("Add study successfully"),
    onError: (err) => {
      toast.error(err.message || "Failed to add study");
    },
    meta: {
      invalidates: userKeys.all(),
    },
  });
};

export const useDeleteUserCustomPermission = () => {
  return useMutation({
    mutationFn: (payload: {
      profileId: string;
      studyId: string;
      userId: string;
    }) => {
      return api.users.deleteUserCustomPermission(payload);
    },
    onSettled: (_, err) => !err && toast.success("Delete study successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to delete study");
    },
    meta: {
      awaits: userKeys.all(),
    },
  });
};

export const useRestoreUserCustomPermission = () => {
  return useMutation({
    mutationFn: (payload: { profileId: string; userId: string }) => {
      return api.users.restoreUserCustomPermission(payload);
    },
    onSettled: (_, err) => !err && toast.success("Restore study successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to restore study");
    },
    meta: {
      awaits: userKeys.all(),
    },
  });
};

export const useUpdateRoleProfile = () => {
  return useMutation({
    mutationFn: (payload: {
      roleId: string;
      profileId: string;
      userId: string;
    }) => {
      return api.users.updateRoleProfile(payload);
    },
    onSettled: (_, err) => !err && toast.success("Update role successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to update role");
    },
    meta: {
      awaits: userKeys.allLists(),
    },
  });
};

export function useAddProfile() {
  return useMutation({
    mutationFn: (payload: AddUserToGroupPayload) =>
      api.groups.addProfileToGroup(payload),
    onSettled: (_, err) => {
      !err && toast.success("Add profile successfully");
    },
    onError: (err) => toast.error(err?.message || "Fail to add profile"),
    meta: {
      awaits: userKeys.allLists(),
    },
  });
}
