import { customPermissionColumn } from "@/components/features/users/user-detail/tabs/profiles/columns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Tabs, TabsItem } from "@/components/ui/tabs";
import { GroupProfile } from "@/lib/apis/groups/types";

import { useRestoreUserCustomPermission } from "../hooks/use-users-mutations";
import { ActivePermissions } from "./active-permissions-tab";
import { DefaultPermissions } from "./default-permissions-tab";

type Props = {
  selectedProfile: GroupProfile;
  setSelectedProfile: React.Dispatch<React.SetStateAction<GroupProfile | null>>;
  onClose: () => void;
  isOpen: boolean;
};

const StudiesModal = ({
  selectedProfile,
  onClose,
  isOpen,
  setSelectedProfile,
}: Props) => {
  const { mutateAsync, isPending: isRestoring } =
    useRestoreUserCustomPermission();

  const handleRestorePermission = () => {
    mutateAsync(
      {
        profileId: selectedProfile.profile.id,
        userId: selectedProfile.profile.userId,
      },
      {
        onSuccess: (response) => {
          setSelectedProfile((prev) =>
            prev
              ? {
                  ...prev,
                  profile: {
                    ...prev.profile,
                    hasCustomPermissions: response.hasCustomStudyPermissions,
                  },
                }
              : null,
          );
        },
      },
    );
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title="Studies"
      className="[&>div]:max-w-3xl"
    >
      <div className="grid gap-2">
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile.group.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Type
          </span>
          <span className="text-sm capitalize text-gray-500 sm:col-span-2">
            {selectedProfile.group.type || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Profile Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile.profile.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">Site</span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile.group.site?.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Custom Permissions
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {
              customPermissionColumn[
                `${selectedProfile.profile.hasCustomPermissions || false}`
              ]
            }
          </span>
        </div>
      </div>
      <div className="mt-2 flex justify-end">
        <Button
          onClick={handleRestorePermission}
          disabled={
            !selectedProfile.profile.hasCustomPermissions || isRestoring
          }
          isLoading={isRestoring}
          variant="primary"
        >
          Restore to default
        </Button>
      </div>
      <Tabs>
        <TabsItem title="Active Permissions">
          <ActivePermissions
            setSelectedProfile={setSelectedProfile}
            selectedProfile={selectedProfile}
          />
        </TabsItem>
        <TabsItem title="Default Permissions">
          <DefaultPermissions groupId={selectedProfile.group.id} />
        </TabsItem>
      </Tabs>
    </WrapperModal>
  );
};

export default StudiesModal;
