"use client";
import type { ColumnDef } from "@tanstack/react-table";
import Image from "next/image";
import Link from "next/link";
import { MdOutlineRemoveRedEye } from "react-icons/md";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import { Skeleton } from "@/components/ui/skeleton";
import type { Sponsor } from "@/lib/apis/sponsors/types";

import { useSponsorLogo } from "../hooks/use-sponsor";

export const columns: ColumnDef<Sponsor>[] = [
  // {
  //   header: "Logo",
  //   accessorKey: "logo",
  //   cell: ({ row }) => <LogoCell sponsorId={row.original.id} />,
  // },
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <Link
        href={`/sponsors/${row.original.id}`}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.getValue("name")}
      </Link>
    ),
  },
  {
    header: "Active",
    accessorKey: "isActive",
    cell: ({ row }) => {
      if (row.original.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    header: "Action",
    accessorKey: "id",
    cell: ({ row }) => {
      return (
        <div className="flex gap-4">
          <Link href={`/sponsors/${row.original.id}`}>
            <div className="leading-4.5 text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium">
              <span className="whitespace-nowrap">View</span>
              <MdOutlineRemoveRedEye />
            </div>
          </Link>
        </div>
      );
    },
    enableSorting: false,
  },
];

export const LogoCell = ({ sponsorId }: { sponsorId: string }) => {
  const { data, isPending } = useSponsorLogo(sponsorId);

  if (isPending) return <Skeleton className="h-[150px] w-[150px]" />;
  if (data?.logoUrl)
    return (
      <Image
        src={data.logoUrl}
        alt="Logo"
        width={150}
        height={150}
        className="max-h-[150px] max-w-[150px]"
      />
    );

  return null;
};
