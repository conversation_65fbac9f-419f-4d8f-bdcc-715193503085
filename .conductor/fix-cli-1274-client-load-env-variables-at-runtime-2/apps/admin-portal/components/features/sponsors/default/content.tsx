"use client";

import { Card } from "flowbite-react";
import { type FC, useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { Sponsor } from "@/lib/apis/sponsors/types";

import { useSponsors } from "../hooks/use-sponsors";
import { columns } from "./columns";
import { ModalAddSponsor } from "./modal-add-sponsor";

const BREADCRUMB_ITEMS = [{ label: "Sponsors" }];

const SponsorPageContent: FC = function () {
  const { data, isLoading } = useSponsors();

  const [showModalAddSponsor, setShowModalAddSponsor] = useState(false);

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "Name", key: "name" },
      { label: "Address", key: "address" },
      { label: "Status", key: "status" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((sponsor) =>
        headers.map((header) => {
          if (header.key === "status") {
            return sponsor.isActive ? "Active" : "Inactive";
          }

          if (header.key === "address") {
            return sponsor.group?.address
              ? `${sponsor.group.address.addressLine}, ${sponsor.group.address.city}, ${sponsor.group.address.stateProvince.name}, ${sponsor.group.address.zipPostalCode}`
              : "";
          }

          return sponsor[header.key as keyof Sponsor];
        }),
      ),
    ];
  }, [data]);

  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Sponsors</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add Sponsor"
            filename="sponsors.csv"
            onButtonClick={() => setShowModalAddSponsor(true)}
          />
          {isLoading ? (
            <TableLoading columns={columns} />
          ) : (
            <>
              <Table data={data?.results ?? []} columns={columns} />
              {data?.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      {showModalAddSponsor && (
        <ModalAddSponsor
          isOpen={showModalAddSponsor}
          onClose={() => setShowModalAddSponsor(false)}
        />
      )}
    </>
  );
};

export default SponsorPageContent;
