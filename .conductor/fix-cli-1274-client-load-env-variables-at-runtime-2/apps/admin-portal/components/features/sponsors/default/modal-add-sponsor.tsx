import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import {
  AddressFormFields,
  addressSchema,
} from "@/components/shared/address-form-fields";
import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { useAddSponsor } from "../hooks/use-add-sponsor";
import { useUploadUrl } from "../hooks/use-upload-url";

export const addSponsorSchema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  address: addressSchema.optional(),
});

type ModalAddSponsorProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddSponsor = ({ isOpen, onClose }: ModalAddSponsorProps) => {
  const { mutateAsync: addSponsor, isPending: isAdding } = useAddSponsor();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { mutateAsync: uploadUrl, isPending: isUploading } = useUploadUrl();
  const [isUploadToCloud, setIsUploadToCloud] = useState(false);

  async function onSubmit(data: z.infer<typeof addSponsorSchema>) {
    const res = await addSponsor(data);
    if (res?.id && selectedFile) {
      setIsUploadToCloud(true);
      try {
        const uploadRes = await uploadUrl({
          id: res.id,
          fileType: selectedFile?.type,
          extension: selectedFile?.type.split("/")[1],
        });

        await fetch(uploadRes.url, {
          method: "PUT",
          body: selectedFile,
          headers: {
            "Content-Type": selectedFile.type,
          },
        });
      } catch (err) {
        console.log(err);
      } finally {
        setIsUploadToCloud(false);
      }
    }
    toast.success("Sponsor added successfully");
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Sponsor</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addSponsorSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <SponsorForm
            onClose={onClose}
            isSubmitting={isAdding || isUploading || isUploadToCloud}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type SponsorFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
  selectedFile: File | null;
  setSelectedFile: (file: File | null) => void;
  defaultFileName?: string;
};

export const SponsorForm = ({
  onClose,
  isSubmitting,
  setSelectedFile,
  defaultFileName,
  selectedFile,
}: SponsorFormProps) => {
  function handleLogoChange(event: React.ChangeEvent<HTMLInputElement>) {
    const file = event.target.files?.[0];

    if (!file) {
      toast.error("Please upload a logo");
      return;
    }

    if (!file.type.startsWith("image/")) {
      toast.error("Please upload an image");
      return;
    }

    setSelectedFile(file);
  }

  return (
    <>
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="col-span-1 sm:col-span-2">
          <label
            className="mb-1 block text-sm font-medium text-gray-900 dark:text-white"
            htmlFor="logoLocation"
          >
            Sponsor Logo
          </label>
          <input
            type="file"
            id="logo"
            accept="image/*"
            onChange={handleLogoChange}
            className="hidden w-full cursor-pointer rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400"
          />
          <label
            htmlFor="logo"
            className="inline-flex w-full cursor-pointer items-center rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 hover:bg-gray-100 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400"
          >
            <span className="flex-shrink-0 rounded-l-lg bg-black px-4 py-2.5 text-white dark:bg-gray-600">
              Choose file
            </span>
            <span className="truncate px-4 py-2.5 text-gray-500">
              {selectedFile?.name || defaultFileName || "No file chosen"}
            </span>
          </label>
        </div>

        <div className="space-y-1">
          <Label htmlFor="name">Name</Label>
          <InputField id="name" name="name" placeholder="Enter name..." />
        </div>

        <AddressFormFields />
      </div>{" "}
      <div className="col-span-2 mt-4 flex flex-col justify-end gap-5 border-none pt-0 sm:mt-6 sm:flex-row ">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Save
        </Button>
      </div>
    </>
  );
};
