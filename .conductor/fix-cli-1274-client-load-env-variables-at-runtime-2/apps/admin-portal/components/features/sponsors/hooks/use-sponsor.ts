import { skipToken, useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_SPONSOR_QUERY_KEY = "sponsor";

export const useSponsor = (id?: string) => {
  return useQuery({
    queryKey: [USE_SPONSOR_QUERY_KEY, id],
    queryFn: () => api.sponsors.get(id!),
  });
};

export const useSponsorLogo = (id?: string) => {
  return useQuery({
    queryKey: [USE_SPONSOR_QUERY_KEY, "logo", id],
    queryFn: id ? () => api.sponsors.getLogoUrl(id) : skipToken,
  });
};
