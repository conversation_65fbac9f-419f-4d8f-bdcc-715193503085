import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { UpdateSponsorPayload } from "@/lib/apis/sponsors/types";

import { USE_SPONSOR_QUERY_KEY } from "./use-sponsor";

export const useUpdateSponsor = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateSponsorPayload }) =>
      api.sponsors.update(id, data),
    onSuccess: async () => {
      toast.dismiss();
      toast.success("Sponsor updated successfully");
      await queryClient.invalidateQueries({
        queryKey: [USE_SPONSOR_QUERY_KEY],
      });
    },
    onError: (error) => {
      toast.dismiss();
      toast.error(
        error.message || "An error occurred while updating the sponsor",
      );
    },
  });
};
