import { useParams } from "next/navigation";
import { useState } from "react";
import type { z } from "zod";

import { Form } from "@/components/ui/form";
import { Modal } from "@/components/ui/modal";
import type { Sponsor } from "@/lib/apis/sponsors/types";

import { addSponsorSchema, SponsorForm } from "../default/modal-add-sponsor";
import { useEditSponsor } from "../hooks/use-edit-sponsor";
import { useUploadUrl } from "../hooks/use-upload-url";
type ModalEditSponsorProps = {
  isOpen: boolean;
  onClose: () => void;
  sponsor?: Sponsor;
};

const editSponsorSchema = addSponsorSchema;

export const ModalEditSponsor = ({
  isOpen,
  onClose,
  sponsor,
}: ModalEditSponsorProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const params = useParams();
  const sponsorId = params.id as string;
  const { mutateAsync: editSponsor, isPending: isEditing } = useEditSponsor();
  const { mutateAsync: uploadUrl, isPending: isUploading } = useUploadUrl();
  const [isUploadToCloud, setIsUploadToCloud] = useState(false);

  const logoUrl = sponsor?.logoUrl;
  let defaultFileName = "";
  if (logoUrl) {
    const url = new URL(logoUrl);
    defaultFileName = url.pathname.split("/").pop()?.split("?")[0] ?? "";
  }

  async function onSubmit(data: z.infer<typeof editSponsorSchema>) {
    if (selectedFile) {
      setIsUploadToCloud(true);
      try {
        const uploadRes = await uploadUrl({
          id: sponsorId,
          fileType: selectedFile?.type,
          extension: selectedFile?.type.split("/")[1],
        });

        await fetch(uploadRes.url, {
          method: "PUT",
          body: selectedFile,
          headers: {
            "Content-Type": selectedFile.type,
          },
        });
      } catch (err) {
        console.log(err);
      } finally {
        setIsUploadToCloud(false);
      }
    }
    await editSponsor({ id: sponsorId, ...data });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Edit Sponsor</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={editSponsorSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            ...sponsor,
            address: {
              ...sponsor?.group?.address,
              stateProvinceId: sponsor?.group?.address?.stateProvince.id,
              countryId: sponsor?.group?.address?.country.id,
            },
          }}
        >
          <SponsorForm
            onClose={onClose}
            selectedFile={selectedFile}
            setSelectedFile={setSelectedFile}
            isSubmitting={isEditing || isUploading || isUploadToCloud}
            defaultFileName={defaultFileName}
          />
        </Form>
      </Modal.Body>
    </Modal>
  );
};
