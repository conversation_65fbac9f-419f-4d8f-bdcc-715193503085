import { faker } from "@faker-js/faker";
import type { ColumnDef } from "@tanstack/react-table";
import Link from "next/link";

export type TSiteDummy = {
  id: string;
  name: string;
};

export const sites: TSiteDummy[] = faker.helpers.multiple(
  () => ({
    id: faker.string.uuid(),
    name: faker.company.name(),
  }),
  { count: 5 },
);

export const columns: ColumnDef<TSiteDummy>[] = [
  {
    header: "Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer font-medium hover:underline"
        href={`/sites/${row.original.id}`}
      >
        {row.original.name}
      </Link>
    ),
  },
];
