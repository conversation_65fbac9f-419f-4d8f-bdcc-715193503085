import { ColumnDef } from "@tanstack/react-table";
import <PERSON> from "next/link";
import { GrStatusGood } from "react-icons/gr";
import { IoDocumentOutline } from "react-icons/io5";
import { MdBlock } from "react-icons/md";

import { customPermissionColumn } from "@/components/features/users/user-detail/tabs/profiles/columns";
import {
  TableEditButton,
  TableGenericButton,
} from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { User } from "@/lib/apis/users/types";

export const generateUserColumns = ({
  onToggleStatus,
  onEdit,
  onViewStudies,
}: {
  onToggleStatus: (user: User) => void;
  onEdit: (user: User) => void;
  onViewStudies: (user: User) => void;
}): ColumnDef<User>[] => [
  {
    header: "First Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/users/${row.original.id}`}
      >
        {row.original.firstName}
      </Link>
    ),
  },
  {
    header: "Last Name",
    cell: ({ row }) => (
      <Link
        className="text-primary-500 cursor-pointer whitespace-nowrap underline-offset-4 hover:underline"
        href={`/users/${row.original.id}`}
      >
        {row.original.lastName}
      </Link>
    ),
  },
  {
    header: "Status",
    cell: ({ row }) => (
      <PillBadge variant={row.original?.isActive ? "success" : "default"}>
        {row.original?.isActive ? "Active" : "Inactive"}
      </PillBadge>
    ),
  },
  {
    header: "Custom Permissions",
    cell: ({ row }) => {
      const value = `${row.original.currentProfile.hasCustomPermissions}`;
      return customPermissionColumn[
        value as keyof typeof customPermissionColumn
      ];
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original;
      return (
        <div className="flex items-center gap-2">
          <TableGenericButton
            type="button"
            onClick={() => {
              onToggleStatus(user);
            }}
            className={
              user.currentProfile.isActive
                ? "text-red-500 hover:text-red-600"
                : "text-green-500 hover:text-green-600"
            }
          >
            {user.currentProfile.isActive ? "Disable" : "Enable"}
            {user.currentProfile.isActive ? <MdBlock /> : <GrStatusGood />}
          </TableGenericButton>
          <TableEditButton
            type="button"
            onClick={() => {
              onEdit(user);
            }}
          />
          <TableGenericButton
            type="button"
            onClick={() => {
              onViewStudies(user);
            }}
            className="text-primary-500 hover:text-primary-600 flex items-center gap-x-1"
          >
            Studies <IoDocumentOutline className="size-4" />
          </TableGenericButton>
        </div>
      );
    },
  },
];
