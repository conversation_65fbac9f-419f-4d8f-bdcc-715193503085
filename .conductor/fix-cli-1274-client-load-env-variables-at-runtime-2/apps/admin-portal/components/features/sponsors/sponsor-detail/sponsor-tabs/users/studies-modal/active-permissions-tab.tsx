"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { generateActivePermissionColumns } from "@/components/features/users/user-detail/tabs/profiles/columns";
import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableLoading } from "@/components/ui/table";
import { TableData } from "@/components/ui/table/table";

import { useDeleteUserCustomPermission } from "../hooks/use-users-mutations";
import { useUserCustomPermission } from "../hooks/use-users-queries";
import { ModalAddStudy } from "./add-study-modal";

export const ActivePermissions = ({
  groupId,
  profileId,
  userId,
}: {
  groupId: string;
  userId: string;
  profileId: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data, isPending } = useUserCustomPermission(profileId);
  const { mutateAsync, isPending: isRemoving } =
    useDeleteUserCustomPermission(userId);

  const columns = useMemo(
    () =>
      generateActivePermissionColumns((studyId: string) => {
        mutateAsync({
          studyId,
          userId,
          profileId: profileId,
        });
      }),
    [mutateAsync, profileId, userId],
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Studies</div>
        <Button variant="primary" onClick={() => setIsOpen(true)}>
          <IoMdAdd />
          Add Study
        </Button>
      </div>

      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <LoadingWrapper isLoading={isRemoving}>
          <TableData columns={columns} data={data?.accessibleStudies ?? []} />
        </LoadingWrapper>
      )}

      <ModalAddStudy
        groupId={groupId}
        isOpen={isOpen}
        profileId={profileId}
        userId={userId}
        onClose={() => setIsOpen(false)}
      />
    </Card>
  );
};
