import { type ColumnDef } from "@tanstack/react-table";
import Link from "next/link";
import { IoMdEye } from "react-icons/io";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { Study } from "@/lib/apis/studies";
import { STUDY_PHASES } from "@/lib/constants";

export const columns: ColumnDef<Study>[] = [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <Link
        href={`/studies/${row.original.id}`}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.getValue("name")}
      </Link>
    ),
  },
  {
    accessorKey: "sponsor",
    header: "Sponsor",
    cell: ({ row }) => {
      const sponsor = row.original.sponsor;
      return sponsor?.name ? (
        <Link
          href={`/sponsors/${sponsor.id}`}
          className="text-primary-500 hover:underline"
        >
          {sponsor.name}
        </Link>
      ) : (
        "-"
      );
    },
  },
  {
    accessorKey: "code",
    header: "Code",
    cell: ({ row }) => (
      <div className="font-medium">{row.original.studyCode}</div>
    ),
  },
  {
    accessorKey: "code",
    header: "Phase Is Active",
    cell: ({ row }) => (
      <div className="font-medium">
        {row.original.phase
          ? STUDY_PHASES[row.original.phase as keyof typeof STUDY_PHASES]
          : "-"}
      </div>
    ),
  },
  {
    header: "Is Active",
    accessorKey: "isActive",
    cell: ({ row }) => {
      if (row.original.isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const study = row.original;

      return (
        <div className="flex gap-4 text-xs text-blue-500">
          <Link
            href={`/studies/${study.id}`}
            className="text-destructive flex items-center gap-1"
          >
            <span>View</span>
            <IoMdEye />
          </Link>
        </div>
      );
    },
  },
];
