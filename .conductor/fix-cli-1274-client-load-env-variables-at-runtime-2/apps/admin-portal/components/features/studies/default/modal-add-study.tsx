import { <PERSON>pic<PERSON> } from "@clincove/shared-ui";
import { z } from "zod";

import { Button, CloseButton } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { Skeleton } from "@/components/ui/skeleton";
import { useInfiniteCategoryVersion } from "@/hooks/queries/use-infinite-category-versions";
import type { CreateStudyPayload } from "@/lib/apis/studies";
import {
  BLINDING_TYPES,
  INTERVENTION_TYPES,
  STUDY_PHASES,
  STUDY_TYPES,
} from "@/lib/constants";

import { useInfiniteSponsors } from "../../sponsors/hooks/use-sponsors";
import { useCreateStudy } from "../hooks/use-studies-mutations";

export const schema = z.object({
  sponsorId: z
    .string({ required_error: "Sponsor is required" })
    .min(1, "Sponsor is required"),
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  studyCode: z
    .string({ required_error: "Study Code is required" })
    .min(1, "Study Code is required"),
  phase: z.string().optional().nullable(),
  startDate: z.string().optional().nullable(),
  endDate: z.string().optional().nullable(),
  description: z.string().optional().nullable(),

  numberOfQCSteps: z.coerce
    .number({
      invalid_type_error: "Step is required",
      required_error: "Step is required",
    })
    .min(1, "Value must be in the range [1-4]")
    .max(4, "Value must be in the range [1-4]"),
  artifactCategoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),

  nct: z.string().optional().nullable(),
  primaryEndpoint: z.string().optional().nullable(),
  studyType: z.string().optional().nullable(),
  interventionType: z.string().optional().nullable(),
  blinding: z.string().optional().nullable(),
  randomization: z.boolean().optional().nullable(),
});

type ModalAddStudyProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddStudy = function ({
  isOpen,
  onClose,
}: ModalAddStudyProps) {
  const { mutateAsync: createStudy, isPending: isCreatingStudy } =
    useCreateStudy();
  // const { isLoading: isLoadingSponsors } = useSponsors({
  //   take: 10,
  //   page: 1,
  //   filter: {},
  // });

  async function onSubmit(data: z.infer<typeof schema>) {
    await createStudy(data as CreateStudyPayload);
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-4xl">
      <Modal.Header>Add Study</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          defaultValues={{
            numberOfQCSteps: 1,
            artifactCategoryVersionId: "",
            sponsorId: "",
            name: "",
            studyCode: "",
            phase: null,
            startDate: null,
            endDate: null,
            description: "",
            nct: "",
            primaryEndpoint: "",
            studyType: null,
            interventionType: null,
            blinding: null,
            randomization: false,
          }}
          formProps={{ shouldFocusError: false }}
        >
          <StudyForm />
          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5 ">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              disabled={isCreatingStudy}
              isLoading={isCreatingStudy}
            >
              Add Study
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export const StudyForm = ({ isEditing }: { isEditing?: boolean }) => {
  if (isEditing) {
    return (
      <div className="grid gap-3 sm:grid-cols-2 sm:gap-6">
        <div className="space-y-1">
          <Label htmlFor="sponsorId">Sponsor</Label>
          <LazySelect
            name="sponsorId"
            id="sponsorId"
            placeholder="Select sponsor"
            searchPlaceholder="Search sponsors..."
            useInfiniteQuery={useInfiniteSponsors}
            getOptionLabel={(sponsor) => sponsor.name}
            getOptionValue={(sponsor) => sponsor.id}
            params={[]}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="name">Name</Label>
          <InputField id="name" name="name" placeholder="Enter name..." />
        </div>

        <div className="space-y-1">
          <Label htmlFor="studyCode">Study Code</Label>
          <InputField
            id="studyCode"
            name="studyCode"
            placeholder="Enter study code..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="phase">Phase</Label>
          <Select
            id="phase"
            name="phase"
            placeholder="Select a phase"
            options={Object.keys(STUDY_PHASES).map((phase) => ({
              label: STUDY_PHASES[phase as keyof typeof STUDY_PHASES],
              value: phase,
            }))}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="startDate">Start Date</Label>
          <Datepicker
            id="startDate"
            name="startDate"
            placeholder="Select start date..."
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="endDate">End Date</Label>
          <Datepicker
            id="endDate"
            name="endDate"
            placeholder="Select end date..."
          />
        </div>
        <div className="space-y-1">
          <Label htmlFor="numberOfQCSteps">Number of QC Steps</Label>
          <InputNumber
            min={1}
            max={4}
            isShowButtons
            id="numberOfQCSteps"
            name="numberOfQCSteps"
            isAllowDecimalNumber={false}
            placeholder="Enter steps..."
          />
        </div>
        <div className="space-y-1 ">
          <Label htmlFor="artifactCategoryVersionId">Category Version</Label>
          <LazySelect
            id="artifactCategoryVersionId"
            name="artifactCategoryVersionId"
            placeholder="Select a version..."
            useInfiniteQuery={useInfiniteCategoryVersion}
            getOptionLabel={(option) => option.version.toString()}
            getOptionValue={(option) => option.id}
          />
        </div>

        <div className="space-y-1">
          <Label htmlFor="nct">NCT</Label>
          <InputField id="nct" name="nct" placeholder="Enter NCT..." />
        </div>

        <div className="space-y-1">
          <Label htmlFor="studyType">Study Type</Label>
          <Select
            id="studyType"
            name="studyType"
            placeholder="Select study type"
            options={Object.keys(STUDY_TYPES).map((type) => ({
              label: STUDY_TYPES[type as keyof typeof STUDY_TYPES],
              value: type,
            }))}
          />
        </div>

        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="blinding">Blinding</Label>
          <Select
            id="blinding"
            name="blinding"
            placeholder="Select blinding"
            options={Object.keys(BLINDING_TYPES).map((type) => ({
              label: BLINDING_TYPES[type as keyof typeof BLINDING_TYPES],
              value: type,
            }))}
          />
        </div>

        <div className="space-y-1 sm:col-span-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            name="description"
            placeholder="Enter description..."
          />
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-3 sm:grid-cols-2 sm:gap-6">
      <div className="space-y-1">
        <Label htmlFor="sponsorId">Sponsor</Label>
        <LazySelect
          name="sponsorId"
          id="sponsorId"
          placeholder="Select sponsor"
          searchPlaceholder="Search sponsors..."
          useInfiniteQuery={useInfiniteSponsors}
          getOptionLabel={(sponsor) => sponsor.name}
          getOptionValue={(sponsor) => sponsor.id}
          params={[]}
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="name">Name</Label>
        <InputField id="name" name="name" placeholder="Enter name..." />
      </div>

      <div className="space-y-1">
        <Label htmlFor="studyCode">Study Code</Label>
        <InputField
          id="studyCode"
          name="studyCode"
          placeholder="Enter study code..."
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="phase">Phase</Label>
        <Select
          id="phase"
          name="phase"
          placeholder="Select phase"
          options={Object.keys(STUDY_PHASES).map((phase) => ({
            label: STUDY_PHASES[phase as keyof typeof STUDY_PHASES],
            value: phase,
          }))}
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="startDate">Start Date</Label>
        <Datepicker
          id="startDate"
          name="startDate"
          placeholder="Select start date..."
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="endDate">End Date</Label>
        <Datepicker
          id="endDate"
          name="endDate"
          placeholder="Select end date..."
        />
      </div>
      <div className="space-y-1 ">
        <Label htmlFor="numberOfQCSteps">Number of QC Steps</Label>
        <InputNumber
          min={1}
          max={4}
          isShowButtons
          isAllowDecimalNumber={false}
          id="numberOfQCSteps"
          name="numberOfQCSteps"
          placeholder="Enter steps..."
        />
      </div>
      <div className="space-y-1 ">
        <Label htmlFor="artifactCategoryVersionId">Category Version</Label>
        <LazySelect
          id="artifactCategoryVersionId"
          name="artifactCategoryVersionId"
          placeholder="Select a version..."
          useInfiniteQuery={useInfiniteCategoryVersion}
          getOptionLabel={(option) => option.version.toString()}
          getOptionValue={(option) => option.id}
        />
      </div>

      <div className="space-y-1 sm:col-span-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          placeholder="Enter description..."
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="nct">NCT</Label>
        <InputField id="nct" name="nct" placeholder="Enter NCT..." />
      </div>

      <div className="space-y-1">
        <Label htmlFor="primaryEndpoint">Primary Endpoint</Label>
        <InputField
          id="primaryEndpoint"
          name="primaryEndpoint"
          placeholder="Enter primary endpoint..."
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="studyType">Study Type</Label>
        <Select
          id="studyType"
          name="studyType"
          placeholder="Select study type"
          options={Object.keys(STUDY_TYPES).map((type) => ({
            label: STUDY_TYPES[type as keyof typeof STUDY_TYPES],
            value: type,
          }))}
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="interventionType">Intervention Type</Label>
        <Select
          id="interventionType"
          name="interventionType"
          placeholder="Select intervention type"
          options={Object.keys(INTERVENTION_TYPES).map((type) => ({
            label: INTERVENTION_TYPES[type as keyof typeof INTERVENTION_TYPES],
            value: type,
          }))}
        />
      </div>

      <div className="space-y-1">
        <Label htmlFor="blinding">Blinding</Label>
        <Select
          id="blinding"
          name="blinding"
          placeholder="Select blinding"
          options={Object.keys(BLINDING_TYPES).map((type) => ({
            label: BLINDING_TYPES[type as keyof typeof BLINDING_TYPES],
            value: type,
          }))}
        />
      </div>

      <div className="col-span-1 flex items-center gap-2">
        <Checkbox id="randomization" name="randomization" />
        <Label htmlFor="randomization">Randomization</Label>
      </div>
    </div>
  );
};

export const StudyFormSkeleton = () => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
      {/* Sponsor Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-20" /> {/* Label */}
        <Skeleton className="h-10 w-full" /> {/* Select */}
      </div>

      {/* Name Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Study Code Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Phase Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Start Date Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* End Date Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-20" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Description Field - spans 2 columns */}
      <div className="space-y-1 sm:col-span-2">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-24 w-full" />
      </div>

      {/* NCT Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-12" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Primary Endpoint Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Study Type Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Intervention Type Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Blinding Field */}
      <div className="space-y-1">
        <Skeleton className="h-5 w-20" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Randomization Field */}
      <div className="col-span-1 flex items-center gap-2">
        <Skeleton className="h-5 w-5" /> {/* Checkbox */}
        <Skeleton className="h-5 w-28" /> {/* Label */}
      </div>
    </div>
  );
};
