import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { useOptimisticMutation } from "@/hooks/use-optimistic-mutation";
import api from "@/lib/apis";
import type {
  Activity,
  ActivityListResponse,
  CreateActivityPayload,
  ReorderActivityPayload,
  UpdateActivityPayload,
} from "@/lib/apis/activities";
import { TAKE_ALL } from "@/lib/constants";

import { USE_ACTIVITY_PROCEDURES_QUERY_KEY } from "../../protocols/tabs/schedule-of-activities-tab/protocol-encounter-detail/hooks/use-activity-procedure";
import { USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY } from "../../protocols/tabs/schedule-of-activities-tab/protocol-encounter-detail/hooks/use-encounter-activities";
import { studyActivityKeys } from "./use-activities-queries";

export const useImportActivities = (studyId: string) => {
  return useMutation({
    mutationFn: async (data: { formData: FormData; id: string }) => {
      return api.activities.import(data);
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Import activities successfully");
    },
    onError: (error) => {
      toast.error(error?.message || "Fail to import activities");
    },
    meta: {
      awaits: studyActivityKeys.allList(studyId),
    },
  });
};

export const useAddActivityMutation = (studyId: string) => {
  return useMutation({
    mutationFn: async (data: CreateActivityPayload) => {
      return api.activities.create(data);
    },
    onSettled: (_, err) => {
      if (!err) toast.success("Activity created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: studyActivityKeys.allList(studyId),
    },
  });
};

export const useEditActivity = (studyId: string) => {
  return useMutation({
    mutationFn: async (data: UpdateActivityPayload & { id: string }) => {
      const { id, ...payload } = data;
      return api.activities.update(id, payload);
    },
    onSettled: (_, err) => {
      !err && toast.success("Activity updated successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
    meta: {
      awaits: studyActivityKeys.allList(studyId),
      invalidates: [USE_PROTOCOL_ENCOUNTER_ACTIVITIES_QUERY_KEY],
    },
  });
};

export const useRemoveProcedureFromActivity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      activityId,
      procedureId,
    }: {
      activityId: string;
      procedureId: string;
    }) => api.activities.removeProcedureFromActivity(activityId, procedureId),
    onSuccess: (_, payload) => {
      return queryClient.invalidateQueries({
        queryKey: [USE_ACTIVITY_PROCEDURES_QUERY_KEY, payload.activityId],
      });
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Procedure removed successfully");
      }
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
};

export const useAddProcedureToActivity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      activityId,
      procedureIds,
    }: {
      activityId: string;
      procedureIds: string[];
    }) => api.activities.addProcedureToActivity(activityId, procedureIds),
    onSuccess: () => {
      return queryClient.invalidateQueries({
        queryKey: [USE_ACTIVITY_PROCEDURES_QUERY_KEY],
      });
    },
    onSettled: (_, err) => {
      if (!err) {
        toast.success("Procedure added successfully!");
      }
    },
    onError: (err) => {
      toast.error(err.message);
    },
  });
};

export const useReorderActivity = (studyId: string) => {
  return useOptimisticMutation({
    queryKey: studyActivityKeys.list(studyId, { take: TAKE_ALL }),
    invalidates: studyActivityKeys.allList(studyId),
    mutationFn: (
      payload: ReorderActivityPayload & {
        newOrderedActivity: Activity[];
      },
    ) => api.activities.reorderActivity(payload),
    onMutate: () => {
      toast.success("Activities reordered successfully");
    },
    updater: (oldData: ActivityListResponse | undefined, payload) => {
      if (!oldData) return undefined;
      return {
        ...oldData,
        results: payload.newOrderedActivity,
      };
    },
    onError: (error) => {
      toast.error(error.message ?? "Failed to reorder activities");
    },
  });
};
