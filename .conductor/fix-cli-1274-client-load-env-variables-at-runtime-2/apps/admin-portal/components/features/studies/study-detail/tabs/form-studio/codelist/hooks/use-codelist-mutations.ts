import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  CreateCodelistPayload,
  UpdateCodelistPayload,
} from "@/lib/apis/codelist-definitions/types";

import { codelistKeys } from "./use-codelist-queries";

export const useCreateCodelist = () => {
  const { id: studyId } = useParams<{ id: string }>();

  return useMutation({
    mutationFn: (payload: Omit<CreateCodelistPayload, "studyId">) =>
      api.codeListDefinition.create({ ...payload, studyId: studyId! }),
    onError: (err) => toast.error(err?.message || "Failed to create codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist created successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });
};

export const useUpdateCodelist = () => {
  const { id: studyId } = useParams<{ id: string }>();

  return useMutation({
    mutationFn: (payload: Omit<UpdateCodelistPayload, "studyId">) =>
      api.codeListDefinition.update({ ...payload, studyId }),
    onError: (err) => toast.error(err?.message || "Failed to update codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist updated successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });
};

export const useArchiveCodelist = () =>
  useMutation({
    mutationFn: ({ id }: { id: string }) => api.codeListDefinition.archive(id),
    onError: (err) => toast.error(err?.message || "Failed to archive codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist archived successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });

export const usePublishCodelist = () =>
  useMutation({
    mutationFn: ({ id }: { id: string }) => api.codeListDefinition.publish(id),
    onError: (err) => toast.error(err?.message || "Failed to publish codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist published successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });

export const useDeleteCodelist = () =>
  useMutation({
    mutationFn: ({ id }: { id: string }) => api.codeListDefinition.delete(id),
    onError: (err) => toast.error(err?.message || "Failed to delete codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist deleted successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });

export const useCopyCodelist = () =>
  useMutation({
    mutationFn: ({ id }: { id: string }) =>
      api.codeListDefinition.newVersion(id),
    onError: (err) => toast.error(err?.message || "Failed to copy codelist"),
    onSettled: (_, err) =>
      !err && toast.success("Codelist copied successfully"),
    meta: {
      awaits: codelistKeys.allLists(),
    },
  });
