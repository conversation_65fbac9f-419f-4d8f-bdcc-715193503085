# Form Studio Fields Refactoring Plan

## Current State Analysis

The fields module is currently functional but has some maintainability issues:

- `field-modal.tsx` is 450+ lines with mixed responsibilities
- Field type configurations scattered across multiple files
- Repetitive validation and rendering logic
- Complex preprocessing in Zod schema

## Refactoring Goals

1. **Improve maintainability** without over-engineering
2. **Reduce code duplication** in validation and rendering
3. **Better separation of concerns** for easier debugging
4. **Prepare for future field types** without major restructuring

## Proposed Changes

### Phase 1: Split Field Modal (Priority: High)

**Current Issue**: `field-modal.tsx` handles form logic, validation, preview, and UI in 450+ lines

**Solution**: Break into focused components:

#### 1. `field-basic-form.tsx` (~80 lines)

Contains the common fields that every field type needs:

```typescript
export const FieldBasicForm = ({ isEditing }: { isEditing: boolean }) => {
  return (
    <div className="grid gap-4 grid-cols-3">
      {/* Field Type Selector */}
      <div className="space-y-1">
        <Label htmlFor="type">Field Type</Label>
        <Select
          name="type"
          options={FIELD_TYPES.map(type => ({ label: type, value: type }))}
          placeholder="Select field type"
          onChange={() => {
            // Reset config when type changes
            formMethods.setValue("config", { layoutDirection: "horizontal" });
          }}
        />
      </div>

      {/* Field Name */}
      <div className="space-y-1">
        <Label htmlFor="name">Field Name</Label>
        <InputField
          name="name"
          placeholder="Enter field name (e.g., patient_name)..."
        />
      </div>

      {/* Field Label */}
      <div className="space-y-1">
        <Label htmlFor="label">Field Label</Label>
        <InputField
          name="label"
          placeholder="Enter field label (e.g., Patient Name)..."
        />
      </div>

      {/* Field ID (only for editing) */}
      {isEditing && (
        <div className="space-y-1">
          <Label required htmlFor="displayId">Field ID</Label>
          <InputField name="displayId" placeholder="Enter field Id..." />
        </div>
      )}

      {/* Description */}
      <div className="space-y-1 col-span-3">
        <Label htmlFor="description">Description</Label>
        <Textarea
          name="description"
          placeholder="Describe what this field represents..."
        />
        <div className="flex justify-end gap-2 py-2.5">
          <Label htmlFor="config.isDisplayOnForm">Display on Form</Label>
          <Switch sizing="sm" name="config.isDisplayOnForm" />
        </div>
      </div>
    </div>
  );
};
```

#### 2. `field-config-form.tsx` (~100 lines)

Dynamically renders field-type-specific configuration:

```typescript
export const FieldConfigForm = () => {
  const currentType = useWatch({ name: "type" });
  const isCodeListField = CODE_LIST_FIELDS.includes(currentType);

  // Get the validation component for current field type
  const ConfigComponent = fieldTypeRegistry[currentType]?.configComponent;

  return (
    <div className="space-y-4">
      {/* CodeList Selection (for choice fields) */}
      {isCodeListField && (
        <div className="space-y-1">
          <Label required htmlFor="config.codeListId">CodeList</Label>
          <LazySelect
            name="config.codeListId"
            useInfiniteQuery={useInfiniteUsers} // TODO: Replace with actual CodeList
            getOptionLabel={(user) => `${user.firstName} ${user.lastName}`}
            getOptionValue={(user) => user.id}
            placeholder="Select code list"
          />
        </div>
      )}

      {/* Dynamic Configuration based on field type */}
      {ConfigComponent && <ConfigComponent />}
    </div>
  );
};
```

#### 3. `field-modal-actions.tsx` (~50 lines)

Handles preview toggle and form submission:

```typescript
export const FieldModalActions = ({
  onClose,
  onPreviewToggle,
  isPreview,
  isValid,
  isSubmitting
}: Props) => {
  return (
    <div className="flex flex-col justify-between gap-4 border-t p-5 sm:flex-row sm:gap-5">
      {/* Preview Toggle */}
      {isValid ? (
        <Tooltip content={isPreview ? "Edit" : "Preview"}>
          <Button onClick={onPreviewToggle} className="!p-2.5" variant="outline">
            {isPreview ? <Edit size={24} /> : <Eye size={24} />}
          </Button>
        </Tooltip>
      ) : (
        <Tooltip content="Complete required fields to preview">
          <Button className="!p-2.5" variant="outline" disabled>
            <Eye size={24} />
          </Button>
        </Tooltip>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col gap-4 sm:flex-row sm:gap-5">
        <CloseButton onClose={onClose} />
        <Button
          disabled={!isValid}
          type="submit"
          form="fields-form"
          isLoading={isSubmitting}
        >
          Save
        </Button>
      </div>
    </div>
  );
};
```

**Benefits**:

- Each component has a single responsibility
- Easier to test individual sections
- Better code organization and readability

### Phase 2: Field Type Registry (Priority: Medium)

**Current Issue**: Field type logic scattered across files with repetitive patterns

**Solution**: Centralized configuration system that replaces the current scattered approach:

#### Current State Problems:

- `validation-fields.tsx` has separate components but no connection to field types
- `getConfigByFieldType` in field-modal has repetitive switch logic
- Field type constants in `columns.tsx` separate from validation logic
- Preview rendering has another large switch statement

#### New Registry Structure:

```typescript
// field-types/index.ts - Main registry
export const fieldTypeRegistry: Record<FieldType, FieldTypeConfig> = {
  Text: textFieldConfig,
  TextArea: textFieldConfig,
  Integer: numberFieldConfig,
  Decimal: numberFieldConfig,
  Date: dateFieldConfig,
  DateTime: dateFieldConfig,
  RadioButton: choiceFieldConfig,
  Checkbox: choiceFieldConfig,
  Dropdown: choiceFieldConfig,
};

export type FieldTypeConfig = {
  configComponent: React.ComponentType;
  previewRenderer: React.ComponentType<PreviewProps>;
  defaultConfig: Partial<FieldConfig>;
  requiresCodeList: boolean;
  configProcessor: (config: any) => FieldConfig;
  validationSchema: ZodSchema;
};
```

#### Detailed Config Files:

**1. `field-types/configs/text-field-config.ts`**:

```typescript
import { TextValidations } from "../../validation-fields";
import { TextPreviewRenderer } from "../renderers/text-renderer";
import { z } from "zod";

export const textFieldConfig: FieldTypeConfig = {
  configComponent: TextValidations, // Reuse existing component
  previewRenderer: TextPreviewRenderer,
  defaultConfig: {
    maxLength: undefined,
    isDisplayOnForm: false,
  },
  requiresCodeList: false,

  // Move from getConfigByFieldType function
  configProcessor: (config) => ({
    ...(config?.maxLength && { maxLength: config.maxLength }),
    ...(config?.isDisplayOnForm && { isDisplayOnForm: config.isDisplayOnForm }),
  }),

  // Move from baseSchema
  validationSchema: z.object({
    maxLength: z.coerce
      .number({ invalid_type_error: "Max length must be a number" })
      .optional(),
    isDisplayOnForm: z.boolean().optional(),
  }),
};
```

**2. `field-types/configs/number-field-config.ts`**:

```typescript
export const numberFieldConfig: FieldTypeConfig = {
  configComponent: ({ type }) => <NumberValidations type={type === "Integer" ? "integer" : "decimal"} />,
  previewRenderer: NumberPreviewRenderer,
  defaultConfig: {
    min: undefined,
    max: undefined,
    unitOfMeasure: undefined,
    decimalPlaces: undefined, // Only for Decimal
  },
  requiresCodeList: false,

  configProcessor: (config) => ({
    ...(typeof config?.min === "number" && { min: config.min }),
    ...(typeof config?.max === "number" && { max: config.max }),
    ...(config?.unitOfMeasure && { unitOfMeasure: config.unitOfMeasure }),
    ...(typeof config?.decimalPlaces === "number" && { decimalPlaces: config.decimalPlaces }),
  }),

  validationSchema: z.object({
    min: z.coerce.number().optional(),
    max: z.coerce.number().optional(),
    unitOfMeasure: z.string().optional(),
    decimalPlaces: z.coerce.number().min(0).max(10).optional(),
  })
};
```

**3. `field-types/configs/date-field-config.ts`**:

```typescript
export const dateFieldConfig: FieldTypeConfig = {
  configComponent: DateValidations, // Reuse existing
  previewRenderer: DatePreviewRenderer,
  defaultConfig: {
    disablePastDates: false,
    disableFutureDates: false,
  },
  requiresCodeList: false,

  configProcessor: (config) => ({
    ...(config?.disablePastDates && {
      disablePastDates: config.disablePastDates,
    }),
    ...(config?.disableFutureDates && {
      disableFutureDates: config.disableFutureDates,
    }),
  }),

  validationSchema: z.object({
    disablePastDates: z.boolean().optional(),
    disableFutureDates: z.boolean().optional(),
  }),
};
```

**4. `field-types/configs/choice-field-config.ts`**:

```typescript
export const choiceFieldConfig: FieldTypeConfig = {
  configComponent: ChoiceValidations, // Reuse existing
  previewRenderer: ChoicePreviewRenderer,
  defaultConfig: {
    layoutDirection: "horizontal",
    codeListId: undefined,
  },
  requiresCodeList: true, // This is the key difference!

  configProcessor: (config) => ({
    ...(config?.codeListId && { codeListId: config.codeListId }),
    ...(config?.layoutDirection && { layoutDirection: config.layoutDirection }),
  }),

  validationSchema: z.object({
    codeListId: z.string().optional(), // Will be required via preprocessing
    layoutDirection: z
      .enum(["horizontal", "vertical"])
      .default("horizontal")
      .optional(),
  }),
};
```

#### How It Simplifies Current Code:

**Before** (in field-modal.tsx):

```typescript
// 90+ lines of switch statement
const getConfigByFieldType = (type: string, config: FieldValues["config"]) => {
  const baseConfig: Record<string, any> = {};
  switch (type) {
    case "Text":
    case "TextArea":
      if (config?.maxLength && typeof config.maxLength === "number") {
        baseConfig.maxLength = config.maxLength;
      }
      break;
    // ... 80+ more lines
  }
};
```

**After** (simplified):

```typescript
// 3 lines
const fieldConfig = fieldTypeRegistry[data.type];
const config = fieldConfig.configProcessor(data.config);
```

**Benefits**:

- **90% reduction** in configuration code complexity
- **Single source of truth** for each field type behavior
- **Easy to extend** - new field type = one new config file
- **Testable** - each config can be unit tested independently
- **Reusable** - same configs used for validation, processing, and rendering

### Phase 3: Clean Validation Logic (Priority: Medium)

**Current Issue**: Complex preprocessing and scattered validation rules

#### Current Validation Problems:

- Complex `z.preprocess` function with nested parsing (40+ lines)
- Validation logic duplicated between form validation and config processing
- Hard-coded field type checks scattered throughout
- Error messages not consistent

#### Solution: Simplified Validation with Registry

**Current Validation** (field-modal.tsx):

```typescript
// 40+ lines of complex preprocessing
const schema = z.preprocess((input, ctx) => {
  const codeListFields = baseSchema
    .pick({ config: true, type: true })
    .safeParse(input);
  if (codeListFields.success) {
    const codeListData = codeListFields.data;
    if (
      codeListData.type &&
      CODE_LIST_FIELDS.includes(codeListData.type) &&
      !codeListData.config?.codeListId
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "CodeList is required",
        path: ["config.codeListId"],
      });
    }
  }
  // ... more complex logic
  return input;
}, baseSchema);
```

**New Simplified Validation**:

```typescript
// field-types/utils/validation-composer.ts
export const createFieldSchema = (fieldType?: string) => {
  const baseSchema = z.object({
    name: z
      .string()
      .min(1, "Name is required")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "Name can only contain letters, numbers, and underscores",
      ),
    label: z.string().min(1, "Label is required"),
    description: z.string().optional(),
    type: z.enum(FIELD_TYPES),
    displayId: z.string().optional(),
    isEditing: z.boolean().optional(),
  });

  // Dynamic config validation based on field type
  if (fieldType && fieldTypeRegistry[fieldType]) {
    const fieldConfig = fieldTypeRegistry[fieldType];
    return baseSchema
      .extend({
        config: fieldConfig.validationSchema,
      })
      .refine(
        (data) => {
          // Simple validation using registry
          if (fieldConfig.requiresCodeList && !data.config?.codeListId) {
            return false;
          }
          if (data.isEditing && !data.displayId) {
            return false;
          }
          return true;
        },
        {
          message: "Required fields missing",
          path: ["config"],
        },
      );
  }

  return baseSchema.extend({
    config: z.object({}).optional(),
  });
};
```

**Benefits**:

- **75% reduction** in validation complexity
- **Dynamic validation** based on selected field type
- **Centralized error handling** with consistent messages
- **No more preprocessing** - cleaner schema composition
- **Type-safe** validation with better IntelliSense

### Phase 4: Improve Field Preview (Priority: Low)

**Current Issue**: Large switch statement in `field-preview.tsx`

#### Current Preview Problems:

- 100+ line `renderField` function with switch statement
- Duplicate field rendering logic (different from actual form components)
- Hard to maintain consistency with real form behavior

#### Solution: Extract Preview Renderers

**Current Approach** (field-preview.tsx):

```typescript
const renderField = (values: FieldValues) => {
  const { name, label, type, config } = values;

  switch (type) {
    case "Text":
      return <InputField name={name} placeholder={`Enter ${label.toLowerCase()}...`} />;
    case "TextArea":
      return <Textarea name={name} placeholder={`Enter ${label.toLowerCase()}...`} />;
    case "Integer":
    case "Decimal":
      return <InputNumber name={name} min={config?.min} max={config?.max} />;
    // ... 80+ more lines
  }
};
```

**New Registry-Based Approach**:

```typescript
// field-types/renderers/text-renderer.tsx
export const TextPreviewRenderer = ({ values }: PreviewRendererProps) => {
  const { name, label, config } = values;

  return (
    <InputField
      name={name}
      placeholder={`Enter ${label.toLowerCase()}...`}
      maxLength={config?.maxLength}
    />
  );
};

// field-preview.tsx - Simplified
const FieldPreview = ({ values }: Props) => {
  const { type } = values;
  const fieldConfig = fieldTypeRegistry[type];

  if (!fieldConfig) {
    return <div className="text-gray-500">Unknown field type</div>;
  }

  const PreviewRenderer = fieldConfig.previewRenderer;

  return (
    <div className="space-y-2">
      <Label htmlFor={values.name}>{values.label}</Label>
      <PreviewRenderer values={values} />
    </div>
  );
};
```

#### Preview Renderers Structure:

```
field-types/renderers/
├── text-renderer.tsx      # Text, TextArea
├── number-renderer.tsx    # Integer, Decimal
├── date-renderer.tsx      # Date, DateTime
└── choice-renderer.tsx    # RadioButton, Checkbox, Dropdown
```

**Example Renderer** (`number-renderer.tsx`):

```typescript
export const NumberPreviewRenderer = ({ values }: PreviewRendererProps) => {
  const { name, label, config, type } = values;

  return (
    <div className="space-y-2">
      <InputNumber
        name={name}
        placeholder={`Enter ${label.toLowerCase()}...`}
        min={config?.min}
        max={config?.max}
        step={type === "Integer" ? 1 : 0.1}
      />
      {config?.unitOfMeasure && (
        <p className="text-sm text-gray-500">
          Unit: {config.unitOfMeasure}
        </p>
      )}
    </div>
  );
};
```

**Benefits**:

- **80% reduction** in preview component complexity
- **Consistent rendering** with registry pattern
- **Easier to maintain** - each renderer is focused on one field type
- **Better reusability** - renderers can be used elsewhere if needed
- **Type safety** - proper TypeScript inference for each renderer

## Implementation Strategy

### What Will NOT Change

- Current file locations and main exports
- Existing API contracts and prop interfaces
- UI/UX behavior (same look and feel)
- Hook usage patterns (queries/mutations remain the same)

### What Will Change

- Internal component structure (better organized)
- Code organization (cleaner separation)
- Maintainability (easier to modify and extend)

### Migration Approach

1. **Incremental refactoring** - one phase at a time
2. **Backward compatibility** - existing imports continue working
3. **No breaking changes** - external API remains the same
4. **Preserve functionality** - all current features maintained

## File Structure After Refactoring

```
fields/
├── field-modal.tsx              # Main modal (simplified)
├── field-preview.tsx            # Preview component (simplified)
├── columns.tsx                  # Table columns (unchanged)
├── fields-tab.tsx               # Main tab component (unchanged)
├── validation-fields.tsx        # Validation components (unchanged)
├── components/                  # NEW: Modal sub-components
│   ├── field-basic-form.tsx
│   ├── field-config-form.tsx
│   └── field-modal-actions.tsx
├── field-types/                 # NEW: Field type registry
│   ├── index.ts
│   ├── configs/
│   └── utils/
└── hooks/                       # Existing hooks (unchanged)
    ├── use-field-library-queries.ts
    └── use-field-library-mutations.ts
```

## Success Metrics

- [ ] Reduced complexity in main files (field-modal.tsx < 200 lines)
- [ ] Eliminated code duplication in validation logic
- [ ] Consistent field type handling across all components
- [ ] Easy addition of new field types (< 30 lines of code)
- [ ] Maintained all existing functionality
- [ ] No performance regression

## Notes

- **CodeList Integration**: Current mockup with users will remain until real CodeList API is available
- **Field Types**: Prepared for future additions but optimized for current stable set
- **Preview Functionality**: Stays inline with modal as required for form submission workflow
- **Validation Complexity**: Kept simple as per current requirements, extensible for future needs
