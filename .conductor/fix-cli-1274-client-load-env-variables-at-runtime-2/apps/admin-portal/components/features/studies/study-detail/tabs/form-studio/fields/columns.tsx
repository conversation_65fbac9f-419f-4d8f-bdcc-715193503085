import { ColumnDef } from "@tanstack/react-table";
import { Md<PERSON><PERSON><PERSON>C<PERSON>, MdPublish } from "react-icons/md";

import {
  TableGenericButton,
  TableRemoveButton,
  TableViewButton,
} from "@/components/shared/table-action-buttons";
import { FormStudioStatusBadge } from "@/components/ui/badges";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";

export const FIELD_TYPES = [
  "Text",
  "TextArea",
  "Integer",
  "Decimal",
  "Date",
  "DateTime",
  "RadioButton",
  "Checkbox",
  "Dropdown",
  // "Autocomplete",
] as const;

type FieldLibraryActions = {
  onEdit: (field: FieldLibraryItem) => void;
  onCopy: (field: FieldLibraryItem) => void;
  onPublish: (field: FieldLibraryItem) => void;
  onArchive: (field: FieldLibraryItem) => void;
};

export const generateFieldLibraryColumns = (
  actions: FieldLibraryActions,
): ColumnDef<FieldLibraryItem>[] => [
  {
    accessorKey: "displayName",
    header: "Field Label",
    cell: ({ row }) => (
      <button
        onClick={() => actions.onEdit(row.original)}
        className="text-primary-500 font-medium hover:underline"
      >
        {row.original.displayName}
      </button>
    ),
  },
  {
    accessorKey: "fieldName",
    header: "Field Name",
    cell: ({ row }) => (
      <span className="font-mono text-sm">{row.original.fieldName}</span>
    ),
  },
  {
    accessorKey: "fieldType",
    header: "Field Type",
    cell: ({ row }) => (
      <PillBadge className="text-sm" variant="default">
        {row.original.fieldType}
      </PillBadge>
    ),
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => <FormStudioStatusBadge variant={row.original.status} />,
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const field = row.original;
      const canPublish = field.status === "DRAFT";
      const canArchive = field.status !== "ARCHIVED";

      return (
        <div className="flex items-center gap-2">
          <TableViewButton
            type="button"
            onClick={() => actions.onEdit(field)}
          />

          <TableGenericButton
            type="button"
            onClick={() => actions.onCopy(field)}
          >
            Copy
            <MdContentCopy className="h-4 w-4" />
          </TableGenericButton>

          {canPublish && (
            <TableGenericButton
              type="button"
              onClick={() => actions.onPublish(field)}
            >
              Publish
              <MdPublish className="h-4 w-4" />
            </TableGenericButton>
          )}

          {canArchive && (
            <TableRemoveButton
              onClick={() => actions.onArchive(field)}
              label="Archive"
            />
          )}
        </div>
      );
    },
  },
];
