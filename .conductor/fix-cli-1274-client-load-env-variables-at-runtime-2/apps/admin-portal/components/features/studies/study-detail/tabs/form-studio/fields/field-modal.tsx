import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { <PERSON>lt<PERSON> } from "flowbite-react";
import { Edit, Eye, X } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { TextCell } from "@/components/ui/form/edc-cells/text-cell";
import { Label } from "@/components/ui/form/label";
import { Switch } from "@/components/ui/form/toggle-switch";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { useInfiniteUsers } from "@/hooks/queries/use-infinite-users";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";
import { cn } from "@/lib/utils";

import { FIELD_TYPES } from "./columns";
import FieldPreview from "./field-preview";
import {
  useCreateField,
  useUpdateField,
} from "./hooks/use-field-library-mutations";
import {
  ChoiceValidations,
  DateValidations,
  NumberValidations,
  TextValidations,
} from "./validation-fields";

const CODE_LIST_FIELDS = ["RadioButton", "Checkbox", "Dropdown"];

const getConfigByFieldType = (type: string, config: FieldValues["config"]) => {
  const baseConfig: Record<string, any> = {};

  switch (type) {
    case "Text":
    case "TextArea":
      if (config?.maxLength && typeof config.maxLength === "number") {
        baseConfig.maxLength = config.maxLength;
      }
      break;

    case "Integer":
    case "Decimal":
      if (typeof config?.min === "number") {
        baseConfig.min = config.min;
      }
      if (typeof config?.max === "number") {
        baseConfig.max = config.max;
      }
      if (config?.unitOfMeasure) {
        baseConfig.unitOfMeasure = config.unitOfMeasure;
      }
      if (type === "Decimal" && typeof config?.decimalPlaces === "number") {
        baseConfig.decimalPlaces = config.decimalPlaces;
      }
      break;

    case "Date":
    case "DateTime":
      if (config?.dateFormat) {
        baseConfig.dateFormat = config.dateFormat;
      }
      if (config?.disablePastDates) {
        baseConfig.disablePastDates = config.disablePastDates;
      }
      if (config?.disableFutureDates) {
        baseConfig.disableFutureDates = config.disableFutureDates;
      }
      break;

    case "RadioButton":
    case "Checkbox":
    case "Dropdown":
      if (config?.codeListId) {
        baseConfig.codeListId = config.codeListId;
      }
      if (
        (type === "RadioButton" || type === "Checkbox") &&
        config?.layoutDirection
      ) {
        baseConfig.layoutDirection = config.layoutDirection;
      }
      break;
  }

  // Common config for all types
  if (config?.isDisplayOnForm) {
    baseConfig.isDisplayOnForm = config.isDisplayOnForm;
  }

  return Object.keys(baseConfig).length > 0 ? baseConfig : undefined;
};
const baseSchema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  label: z
    .string({
      required_error: "Label is required",
      invalid_type_error: "Label is required",
    })
    .min(1, "Label is required"),
  description: z.string().optional(),
  type: z.enum(FIELD_TYPES, {
    errorMap: () => ({ message: "Type is required" }),
  }),
  config: z
    .object({
      maxLength: z.coerce
        .number({ invalid_type_error: "Max length must be a number" })
        .optional(),
      min: z.coerce
        .number({ invalid_type_error: "Min value must be a number" })
        .optional(),
      max: z.coerce
        .number({ invalid_type_error: "Max value must be a number" })
        .optional(),
      unitOfMeasure: z.string().optional(),
      dateFormat: z.string().optional(),
      disableFutureDates: z.boolean().optional(),
      disablePastDates: z.boolean().optional(),
      layoutDirection: z
        .enum(["horizontal", "vertical"])
        .default("horizontal")
        .optional(),
      decimalPlaces: z.coerce
        .number({ invalid_type_error: "Decimal places must be a number" })
        .min(0, "Decimal places must be at least 0")
        .max(10, "Decimal places must be at most 10")
        .optional(),
      codeListId: z.string().optional(),
      isDisplayOnForm: z.boolean().optional(),
    })
    .optional(),
  // use this to run preprocess
  shortCode: z.string().optional(),
  isEditing: z.boolean().optional(),
});
const schema = z.preprocess((input, ctx) => {
  const configFields = baseSchema
    .pick({
      config: true,
      type: true,
    })
    .safeParse(input);

  if (configFields.success) {
    const configData = configFields.data;
    if (
      configData.type &&
      CODE_LIST_FIELDS.includes(configData.type) &&
      !configData.config?.codeListId
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "CodeList is required",
        path: ["config.codeListId"],
      });
    }

    if (
      (configData.type === "Date" || configData.type === "DateTime") &&
      !configData.config?.dateFormat
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Date format is required",
        path: ["config.dateFormat"],
      });
    }
  }

  const shortCodeFields = baseSchema
    .pick({
      shortCode: true,
      isEditing: true,
    })
    .safeParse(input);

  if (shortCodeFields.success) {
    const shortCodeFieldsData = shortCodeFields.data;
    if (shortCodeFieldsData.isEditing && !shortCodeFieldsData.shortCode) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Field ID is required",
        path: ["shortCode"],
      });
    }
  }

  return input;
}, baseSchema);

export type FieldValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedField: FieldLibraryItem | null;
  mode: "create" | "edit" | "copy";
};

export const FieldModal = function ({
  isOpen,
  onClose,
  selectedField,
  mode,
}: Props) {
  const [isPreview, setIsPreview] = useState(false);
  const [showPublishedWarning, setShowPublishedWarning] = useState(false);
  const { mutateAsync: createField, isPending: isCreatingField } =
    useCreateField();
  const { mutateAsync: updateField, isPending: isUpdatingField } =
    useUpdateField();

  const isEditing = mode === "edit";
  const isPublishedField = selectedField?.status === "PUBLISHED";

  const formMethods = useForm<FieldValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      name: selectedField?.fieldName || "",
      label: selectedField?.displayName || "",
      description: selectedField?.description || "",
      type: selectedField?.fieldType,
      shortCode: selectedField?.shortCode || "",
      config: {
        ...selectedField?.config,
      },
      isEditing: isEditing,
    },
  });
  const values = formMethods.getValues();

  const currentType = formMethods.watch("type");
  const isCodeListField = CODE_LIST_FIELDS.includes(currentType);
  const isValid = formMethods.formState.isValid;

  const validationFieldsMap = {
    Text: <TextValidations />,
    TextArea: <TextValidations />,
    Integer: <NumberValidations type="integer" />,
    Decimal: <NumberValidations type="decimal" />,
    Date: <DateValidations type="Date" />,
    DateTime: <DateValidations type="DateTime" />,
    RadioButton: <ChoiceValidations />,
    Checkbox: <ChoiceValidations />,
  };

  const actualSubmit = async (data: FieldValues) => {
    const config = getConfigByFieldType(data.type, data.config);

    const payload = {
      fieldName: data.name,
      fieldType: data.type,
      displayName: data.label,
      description: data.description || undefined,
      shortCode: data.shortCode || undefined,
      config,
    };

    isEditing && selectedField
      ? await updateField({ id: selectedField.id, ...payload })
      : await createField(payload);

    return onClose();
  };

  const onSubmit = async (data: FieldValues) => {
    if (isEditing && isPublishedField) {
      setShowPublishedWarning(true);
      return;
    }

    await actualSubmit(data);
  };
  const handlePreview = () => {
    setIsPreview(!isPreview);
  };
  const handleClosePreview = () => {
    setIsPreview(false);
  };

  const handleContinueEditing = async () => {
    setShowPublishedWarning(false);
    const formData = formMethods.getValues();
    await actualSubmit(formData);
  };

  const handleCancelEditing = () => {
    setShowPublishedWarning(false);
  };

  return (
    <>
      <Modal
        show={isOpen}
        theme={{
          body: {
            base: "h-full max-h-full max-w-full",
          },
          content: {
            base: "h-full max-h-full w-full !max-w-full p-4",
            inner: "h-full max-h-full max-w-full",
          },
        }}
      >
        <Modal.Body>
          <div className="h-full bg-white p-4">
            <div className="flex h-full flex-col rounded border ">
              <div className="flex items-center justify-between gap-2 p-5">
                <h3 className=" text-2xl font-bold lg:text-3xl dark:text-white">
                  Field Editor
                </h3>
                <button
                  onClick={onClose}
                  className="rounded-full p-2 transition-colors hover:bg-gray-100"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              <div className="flex-1 overflow-y-auto px-5 pb-5">
                {isPreview && (
                  <FieldPreview onClose={handleClosePreview} values={values} />
                )}
                <Form
                  schema={schema}
                  formMethods={formMethods}
                  onSubmit={onSubmit}
                  id="fields-form"
                  className={cn("space-y-5", isPreview && "hidden")}
                >
                  <div className="space-y-4 rounded-xl border p-5 shadow">
                    <div
                      className={cn(
                        "grid gap-4",
                        isEditing ? "grid-cols-4" : "grid-cols-3",
                      )}
                    >
                      <div className="space-y-1">
                        <Label htmlFor="type">Field Type</Label>
                        <Select
                          name="type"
                          options={FIELD_TYPES.map((type) => ({
                            label: type,
                            value: type,
                          }))}
                          placeholder="Select field type"
                          onChange={() => {
                            formMethods.setValue(
                              "config",
                              {
                                layoutDirection: "horizontal",
                              },
                              { shouldValidate: true },
                            );
                          }}
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="name">Field Name</Label>
                        <InputField
                          id="name"
                          name="name"
                          placeholder="Enter field name (e.g., patient_name, birth_date)..."
                        />
                      </div>
                      <div className="space-y-1">
                        <Label htmlFor="label">Field Label</Label>
                        <InputField
                          id="label"
                          name="label"
                          placeholder="Enter field label (e.g., Patient Name, Date of Birth)..."
                        />
                      </div>
                      {isEditing && (
                        <div className="space-y-1">
                          <Label required htmlFor="shortCode">
                            Field ID
                          </Label>
                          <InputField
                            id="shortCode"
                            name="shortCode"
                            placeholder="Enter field Id..."
                          />
                        </div>
                      )}
                    </div>
                    {isCodeListField && (
                      <div className="space-y-1">
                        <Label required htmlFor="config.codeListId">
                          CodeList
                        </Label>
                        <LazySelect
                          id="config.codeListId"
                          name="config.codeListId"
                          searchPlaceholder="Search user..."
                          useInfiniteQuery={useInfiniteUsers}
                          getOptionLabel={(user) =>
                            `${user.firstName} ${user.lastName}`
                          }
                          getOptionValue={(user) => user.id}
                          placeholder="Select user"
                        />
                      </div>
                    )}
                    <div className="space-y-1">
                      <Label htmlFor="description">Description</Label>
                      <Textarea
                        id="description"
                        name="description"
                        placeholder="Describe what this field represents..."
                      />
                      <div className="flex justify-end gap-2 py-2.5">
                        <Label htmlFor="config.isDisplayOnForm">
                          Display on Form
                        </Label>
                        <Switch sizing="sm" name="config.isDisplayOnForm" />
                      </div>
                    </div>
                  </div>

                  {!!currentType &&
                    !!validationFieldsMap[
                      currentType as keyof typeof validationFieldsMap
                    ] && (
                      <div className="space-y-2.5 rounded-xl border border-gray-200 p-5 shadow">
                        <div className="flex justify-end">
                          <span className="rounded-2xl bg-gray-50 px-2 py-1 text-xs font-medium text-gray-500">
                            Format
                          </span>
                        </div>

                        {currentType &&
                          validationFieldsMap[
                            currentType as keyof typeof validationFieldsMap
                          ]}
                      </div>
                    )}
                  <div className="grid grid-cols-2">
                    <TextCell name="name" />
                    <TextCell name="label" />
                  </div>
                </Form>
              </div>
              <div
                className={cn(
                  "flex flex-col justify-between gap-4 border-t p-5 sm:flex-row sm:gap-5",
                )}
              >
                {isValid ? (
                  <Tooltip content={isPreview ? "Edit" : "Preview"}>
                    <Button
                      onClick={handlePreview}
                      className="!p-2.5 [&_span]:!p-0"
                      variant="outline"
                    >
                      {isPreview ? <Edit size={24} /> : <Eye size={24} />}
                    </Button>
                  </Tooltip>
                ) : (
                  <Tooltip content="Complete required fields to preview">
                    <Button
                      onClick={handlePreview}
                      className="!p-2.5 [&_span]:!p-0"
                      variant="outline"
                      disabled
                    >
                      <Eye size={24} />
                    </Button>
                  </Tooltip>
                )}
                <div className="flex flex-col gap-4 sm:flex-row sm:gap-5">
                  <CloseButton onClose={onClose} />
                  <Button
                    disabled={!isValid}
                    type="submit"
                    form="fields-form"
                    variant="primary"
                    isLoading={isCreatingField || isUpdatingField}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </Modal.Body>
      </Modal>
      <ConfirmModal
        isOpen={showPublishedWarning}
        onClose={handleCancelEditing}
        onConfirm={handleContinueEditing}
        title="Edit Published Field"
        confirmLabel="Create New Version"
        isLoading={isCreatingField || isUpdatingField}
      >
        <p className="text-sm text-gray-600">
          This field has been published. Editing it will create a new version
          while keeping the original unchanged.
        </p>
      </ConfirmModal>
    </>
  );
};
