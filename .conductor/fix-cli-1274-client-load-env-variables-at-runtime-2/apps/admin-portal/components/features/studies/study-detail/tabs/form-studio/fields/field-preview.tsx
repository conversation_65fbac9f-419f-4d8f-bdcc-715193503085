import { Datepicker } from "@clincove/shared-ui";
import { X } from "lucide-react";
import { z } from "zod";

import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";

import type { FieldValues } from "./field-modal";

type Props = {
  onClose: () => void;
  values: FieldValues;
};

const dummyOptions = [
  { label: "Option 1", value: "option1" },
  { label: "Option 2", value: "option2" },
  { label: "Option 3", value: "option3" },
];
const createValidationSchema = (values: FieldValues) => {
  const { name, type, config, label } = values;

  if (!name) return z.object({});

  switch (type) {
    case "Text":
    case "TextArea":
      return z.object({
        [name]: config?.maxLength
          ? z
              .string({
                invalid_type_error: `${label} is required`,
                required_error: `${label} is required`,
              })
              .max(
                config.maxLength,
                `Maximum ${config.maxLength} characters allowed`,
              )
              .min(1, `${label} is required`)
          : z
              .string({
                invalid_type_error: `${label} is required`,
                required_error: `${label} is required`,
              })
              .min(1, `${label} is required`),
      });

    case "Integer":
    case "Decimal": {
      let numberSchema = z.number({
        invalid_type_error: `${label} is required`,
        required_error: `${label} is required`,
      });
      if (typeof config?.min === "number") {
        numberSchema = numberSchema.min(
          config.min,
          `Minimum value is ${config.min}`,
        );
      }
      if (typeof config?.max === "number") {
        numberSchema = numberSchema.max(
          config.max,
          `Maximum value is ${config.max}`,
        );
      }
      return z.object({
        [name]: numberSchema,
      });
    }

    case "Date":
    case "DateTime":
      return z.object({
        [name]: z
          .string({
            invalid_type_error: `${label} is required`,
            required_error: `${label} is required`,
          })
          .min(1, `${label} is required`),
      });

    case "Dropdown":
    case "RadioButton":
      return z.object({
        [name]: z.enum(["option1", "option2", "option3"], {
          errorMap: () => ({ message: "Please select an option" }),
        }),
      });

    case "Checkbox":
      return z.object({
        [name]: z.array(z.string()).min(1, "Please select at least one option"),
      });

    default:
      return z.object({
        [name]: z
          .string({
            invalid_type_error: `${label} is required`,
            required_error: `${label} is required`,
          })
          .min(1, `${label} is required`),
      });
  }
};
const renderField = (values: FieldValues) => {
  const { name, label, type, config } = values;

  if (!name || !label || !type) {
    return (
      <div className="py-8 text-center text-gray-500">
        Complete field configuration to see preview
      </div>
    );
  }

  switch (type) {
    case "Text":
      return (
        <InputField
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          maxLength={config?.maxLength}
        />
      );

    case "TextArea":
      return (
        <Textarea
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          maxLength={config?.maxLength}
        />
      );

    case "Integer":
    case "Decimal":
      return (
        <InputNumber
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
          min={config?.min}
          max={config?.max}
          step={type === "Integer" ? 1 : 0.1}
        />
      );

    case "Date":
    case "DateTime":
      return (
        <Datepicker
          name={name}
          placeholder={`Select ${label.toLowerCase()}...`}
          format={config?.dateFormat?.toLocaleLowerCase() as any}
          maxDate={config?.disableFutureDates ? new Date() : undefined}
          minDate={config?.disablePastDates ? new Date() : undefined}
        />
      );

    case "Dropdown":
      return (
        <Select
          name={name}
          options={dummyOptions}
          placeholder={`Select ${label.toLowerCase()}...`}
        />
      );

    case "RadioButton":
      return (
        <div
          className={`flex gap-4 ${config?.layoutDirection === "vertical" ? "flex-col" : "flex-row"}`}
        >
          {dummyOptions.map((option) => (
            <div key={option.value} className="flex items-center gap-2">
              <Radio
                name={name}
                value={option.value}
                id={`${name}-${option.value}`}
              />
              <Label htmlFor={`${name}-${option.value}`}>{option.label}</Label>
            </div>
          ))}
        </div>
      );

    case "Checkbox":
      return (
        <div
          className={`flex gap-4 ${config?.layoutDirection === "vertical" ? "flex-col" : "flex-row"}`}
        >
          {dummyOptions.map((option) => (
            <div key={option.value} className="flex items-center gap-2">
              <Checkbox
                name={`${name}[]`}
                value={option.value}
                id={`${name}-${option.value}`}
              />
              <Label htmlFor={`${name}-${option.value}`}>{option.label}</Label>
            </div>
          ))}
        </div>
      );

    default:
      return (
        <InputField
          name={name}
          placeholder={`Enter ${label.toLowerCase()}...`}
        />
      );
  }
};

const FieldPreview = ({ onClose, values }: Props) => {
  const { name, label, description, type, config } = values;

  const previewSchema = createValidationSchema(values);

  return (
    <div className="h-full gap-4 rounded-xl border border-blue-500 shadow dark:border-blue-400">
      <div className="flex items-center justify-between border-b border-b-blue-500 p-5 dark:border-b-blue-400">
        <span className="text-xl font-bold dark:text-white">Preview Mode</span>
        <button
          onClick={onClose}
          className="rounded-full bg-blue-500 p-2 text-white transition-colors hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      <div className="space-y-2.5 p-5">
        <Form
          onSubmit={(data) => {
            //
          }}
          mode="all"
          defaultValues={{
            [values.name]: "",
          }}
          schema={previewSchema}
        >
          {name && label && type ? (
            <div className="space-y-2">
              <Label htmlFor={name}>{label}</Label>
              {description && values.config?.isDisplayOnForm && (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {description}
                </p>
              )}
              {config?.unitOfMeasure && (
                <p className="text-sm text-gray-500">
                  Unit: {config.unitOfMeasure}
                </p>
              )}
              {renderField(values)}
            </div>
          ) : (
            renderField(values)
          )}
        </Form>
      </div>
    </div>
  );
};

export default FieldPreview;
