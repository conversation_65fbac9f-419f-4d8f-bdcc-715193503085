"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { FieldLibraryItem } from "@/lib/apis/field-library/types";

import { generateFieldLibraryColumns } from "./columns";
import { FieldModal } from "./field-modal";
import { FilterFields } from "./filter-fields";
import {
  useArchiveField,
  usePublishField,
} from "./hooks/use-field-library-mutations";
import { useFieldLibrary } from "./hooks/use-field-library-queries";

export const FieldsTab = () => {
  const { data, isPending, isPlaceholderData } = useFieldLibrary();
  const { mutateAsync: archiveField, isPending: isArchiving } =
    useArchiveField();
  const { mutateAsync: publishField, isPending: isPublishing } =
    usePublishField();

  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const {
    isOpen: isConfirmArchiveOpen,
    open: openConfirmArchive,
    close: closeConfirmArchive,
  } = useDisclosure();
  const [selectedField, setSelectedField] = useState<FieldLibraryItem | null>(
    null,
  );
  const [modalMode, setModalMode] = useState<"create" | "edit">("create");

  const openAddModal = () => {
    setSelectedField(null);
    setModalMode("create");
    openModal();
  };

  const openEditModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    setModalMode("edit");
    openModal();
  };

  const openCopyModal = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedField(null);
    setModalMode("create");
  };

  const handlePublish = async (field: FieldLibraryItem) => {
    await publishField({ id: field.id });
  };

  const handleArchiveClick = (field: FieldLibraryItem) => {
    setSelectedField(field);
    openConfirmArchive();
  };

  const handleConfirmArchive = async () => {
    if (!selectedField) return;

    await archiveField({ id: selectedField.id });
    closeConfirmArchive();
  };

  const handleCancelArchive = () => {
    closeConfirmArchive();
  };

  const columns = useMemo(
    () =>
      generateFieldLibraryColumns({
        onArchive: handleArchiveClick,
        onCopy: openCopyModal,
        onEdit: openEditModal,
        onPublish: handlePublish,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Fields
          </h3>

          <div className="flex items-center gap-4">
            <SearchField placeholder="Search fields..." />
            <FilterFields />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Field
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData || isPublishing}>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        )}
      </div>

      {isModalOpen && (
        <FieldModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          selectedField={selectedField}
          mode={modalMode}
        />
      )}

      <ConfirmModal
        isOpen={isConfirmArchiveOpen}
        onClose={handleCancelArchive}
        onConfirm={handleConfirmArchive}
        isLoading={isArchiving}
        title="Archive Field"
        confirmLabel="Archive"
      >
        <p className="text-gray-600 dark:text-gray-400">
          Are you sure you want to archive the field{" "}
          <strong>"{selectedField?.displayName}"</strong>?
        </p>
        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
          This action will make the field inactive and it will no longer be
          available for use in forms.
        </p>
      </ConfirmModal>
    </Card>
  );
};
