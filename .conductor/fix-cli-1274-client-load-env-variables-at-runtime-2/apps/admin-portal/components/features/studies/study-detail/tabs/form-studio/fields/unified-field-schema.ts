import { z } from "zod";

const FIELD_TYPES = [
  "Text",
  "TextArea",
  "Integer",
  "Decimal",
  "Date",
  "DateTime",
  "RadioButton",
  "Checkbox",
  "Dropdown",
] as const;

const CODE_LIST_FIELDS = ["RadioButton", "Checkbox", "Dropdown"];

// Define all possible config properties with their validation rules
const configSchema = z
  .object({
    // Text field config
    maxLength: z.coerce
      .number({ invalid_type_error: "Max length must be a number" })
      .positive("Max length must be positive")
      .optional(),

    // Number field config
    min: z.coerce
      .number({ invalid_type_error: "Min value must be a number" })
      .optional(),
    max: z.coerce
      .number({ invalid_type_error: "Max value must be a number" })
      .optional(),
    unitOfMeasure: z.string().min(1).optional(),
    decimalPlaces: z.coerce
      .number({ invalid_type_error: "Decimal places must be a number" })
      .min(0, "Decimal places must be at least 0")
      .max(10, "Decimal places must be at most 10")
      .optional(),

    // Date field config
    disableFutureDates: z.boolean().optional(),
    disablePastDates: z.boolean().optional(),

    // Choice field config
    codeListId: z.string().uuid("Invalid CodeList ID").optional(),
    layoutDirection: z
      .enum(["horizontal", "vertical"])
      .default("horizontal")
      .optional(),

    // Common config
    isDisplayOnForm: z.boolean().default(false).optional(),
  })
  .optional();

// Main field schema with dynamic validation
export const unifiedFieldSchema = z
  .object({
    name: z
      .string({ required_error: "Name is required" })
      .min(1, "Name is required")
      .regex(
        /^[a-zA-Z0-9_]+$/,
        "Name can only contain letters, numbers, and underscores",
      ),

    label: z
      .string({ required_error: "Label is required" })
      .min(1, "Label is required"),

    description: z.string().optional(),

    type: z.enum(FIELD_TYPES, {
      errorMap: () => ({ message: "Field type is required" }),
    }),

    config: configSchema,

    // Editing specific fields
    shortCode: z.string().optional(),
    isEditing: z.boolean().optional(),
  })
  .superRefine((data, ctx) => {
    // Dynamic validation based on field type
    const { type, config, isEditing, shortCode } = data;

    // CodeList validation for choice fields
    if (CODE_LIST_FIELDS.includes(type) && !config?.codeListId) {
      ctx.addIssue({
        code: "custom",
        message: "CodeList is required for choice fields",
        path: ["config", "codeListId"],
      });
    }

    // ShortCode validation for editing mode
    if (isEditing && !shortCode) {
      ctx.addIssue({
        code: "custom",
        message: "Field ID is required when editing",
        path: ["shortCode"],
      });
    }

    // Field-type specific validations
    if (type === "Decimal" && config?.decimalPlaces === undefined) {
      // Optional: Auto-set decimal places for decimal fields
      // data.config = { ...config, decimalPlaces: 2 };
    }

    // Cross-field validation (min/max)
    if (config?.min !== undefined && config?.max !== undefined) {
      if (config.min >= config.max) {
        ctx.addIssue({
          code: "custom",
          message: "Minimum value must be less than maximum value",
          path: ["config", "min"],
        });
      }
    }

    // Text field specific validation
    if (
      (type === "Text" || type === "TextArea") &&
      config?.maxLength !== undefined
    ) {
      if (config.maxLength < 1) {
        ctx.addIssue({
          code: "custom",
          message: "Max length must be at least 1",
          path: ["config", "maxLength"],
        });
      }
    }
  });

export type FieldFormData = z.infer<typeof unifiedFieldSchema>;

// Helper to get field-type specific default values
export const getDefaultConfigForFieldType = (fieldType: string) => {
  const defaults: Record<string, any> = {
    Text: { isDisplayOnForm: false },
    TextArea: { isDisplayOnForm: false },
    Integer: { isDisplayOnForm: false },
    Decimal: { isDisplayOnForm: false, decimalPlaces: 2 },
    Date: {
      isDisplayOnForm: false,
      disablePastDates: false,
      disableFutureDates: false,
    },
    DateTime: {
      isDisplayOnForm: false,
      disablePastDates: false,
      disableFutureDates: false,
    },
    RadioButton: { layoutDirection: "horizontal", isDisplayOnForm: false },
    Checkbox: { layoutDirection: "horizontal", isDisplayOnForm: false },
    Dropdown: { isDisplayOnForm: false },
  };

  return defaults[fieldType] || { isDisplayOnForm: false };
};

// Helper to clean config based on field type (removes irrelevant properties)
export const cleanConfigForFieldType = (
  fieldType: string,
  config: any = {},
) => {
  const relevantKeys: Record<string, string[]> = {
    Text: ["maxLength", "isDisplayOnForm"],
    TextArea: ["maxLength", "isDisplayOnForm"],
    Integer: ["min", "max", "unitOfMeasure", "isDisplayOnForm"],
    Decimal: [
      "min",
      "max",
      "unitOfMeasure",
      "decimalPlaces",
      "isDisplayOnForm",
    ],
    Date: ["disablePastDates", "disableFutureDates", "isDisplayOnForm"],
    DateTime: ["disablePastDates", "disableFutureDates", "isDisplayOnForm"],
    RadioButton: ["codeListId", "layoutDirection", "isDisplayOnForm"],
    Checkbox: ["codeListId", "layoutDirection", "isDisplayOnForm"],
    Dropdown: ["codeListId", "isDisplayOnForm"],
  };

  const allowedKeys = relevantKeys[fieldType] || ["isDisplayOnForm"];

  return Object.keys(config)
    .filter((key) => allowedKeys.includes(key))
    .reduce(
      (cleanConfig, key) => {
        if (
          config[key] !== undefined &&
          config[key] !== null &&
          config[key] !== ""
        ) {
          cleanConfig[key] = config[key];
        }
        return cleanConfig;
      },
      {} as Record<string, any>,
    );
};
