import { InputField, Select } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { Radio } from "@/components/ui/form/radio";
import { Switch } from "@/components/ui/form/toggle-switch";
import { cn } from "@/lib/utils";

const TextValidations = () => {
  return (
    <div className="grid grid-cols-3">
      <div className="space-y-1">
        <Label htmlFor="config.maxLength">Max Length</Label>
        <InputNumber
          name="config.maxLength"
          id="config.maxLength"
          placeholder="Enter maximum character length..."
          min={1}
          isAllowDecimalNumber={false}
        />
      </div>
    </div>
  );
};

type NumberValidationsProps = {
  type: "integer" | "decimal";
};

const NumberValidations = ({ type }: NumberValidationsProps) => {
  const isDecimal = type === "decimal";

  return (
    <div
      className={cn("grid gap-4", isDecimal ? "grid-cols-4" : "grid-cols-3")}
    >
      <div className="space-y-1">
        <Label htmlFor="config.min">Min Value</Label>
        <InputNumber
          name="config.min"
          id="config.min"
          placeholder="Enter minimum value..."
          isAllowDecimalNumber={isDecimal}
        />
      </div>
      <div className="space-y-1">
        <Label htmlFor="config.max">Max Value</Label>
        <InputNumber
          name="config.max"
          id="config.max"
          placeholder="Enter maximum value..."
          isAllowDecimalNumber={isDecimal}
        />
      </div>
      {isDecimal && (
        <div className="space-y-1">
          <Label htmlFor="config.decimalPlaces">Decimal Places</Label>
          <InputNumber
            name="config.decimalPlaces"
            id="config.decimalPlaces"
            placeholder="Enter decimal places (e.g., 2)..."
            min={0}
            max={10}
            isAllowDecimalNumber={false}
          />
        </div>
      )}
      <div className="space-y-1">
        <Label htmlFor="config.unitOfMeasure">Unit of Measure</Label>
        <InputField
          name="config.unitOfMeasure"
          id="config.unitOfMeasure"
          placeholder="Enter unit (e.g., kg, cm, years)..."
        />
      </div>
    </div>
  );
};

export const DATE_FORMAT = ["MM/DD/YYYY", "DD/MM/YYYY", "YYYY/MM/DD"];
export const TIME_FORMAT = "HH:mm:ss";

type DateValidationsProps = {
  type: "Date" | "DateTime";
};

const DateValidations = ({ type }: DateValidationsProps) => {
  const isDateTime = type === "DateTime";

  // Generate format options based on field type
  const dateFormatOptions = DATE_FORMAT.map((format) => {
    const fullFormat = isDateTime ? `${format} ${TIME_FORMAT}` : format;
    return {
      label: fullFormat,
      value: fullFormat,
    };
  });

  return (
    <div className="grid grid-cols-3 gap-4">
      <div className="space-y-1">
        <Label required htmlFor="config.dateFormat">
          Date Format
        </Label>
        <Select
          name="config.dateFormat"
          id="config.dateFormat"
          options={dateFormatOptions}
          placeholder="Select date format"
        />
      </div>

      <div className="flex items-center gap-4">
        <div className="flex justify-end gap-2">
          <Label htmlFor="config.disableFutureDates">
            Disable Future Dates
          </Label>
          <Switch sizing="sm" name="config.disableFutureDates" />
        </div>
        <div className="flex justify-end gap-2">
          <Label htmlFor="config.disablePastDates">Disable Past Dates</Label>
          <Switch sizing="sm" name="config.disablePastDates" />
        </div>
      </div>
    </div>
  );
};

const ChoiceValidations = () => {
  return (
    <div className="space-y-2">
      <Label>Layout Direction</Label>
      <div className="flex gap-6">
        <div className="flex items-center gap-2">
          <Radio
            name="config.layoutDirection"
            value="horizontal"
            id="layout-horizontal"
          />
          <Label htmlFor="layout-horizontal">Horizontal</Label>
        </div>
        <div className="flex items-center gap-2">
          <Radio
            name="config.layoutDirection"
            value="vertical"
            id="layout-vertical"
          />
          <Label htmlFor="layout-vertical">Vertical</Label>
        </div>
      </div>
    </div>
  );
};

export {
  ChoiceValidations,
  DateValidations,
  NumberValidations,
  TextValidations,
};
