import React, { useState } from "react";

import { Modal } from "@/components/ui/modal";

import { QuestionsPanel } from "./left-side/questions-panel";
import { FormBuilder } from "./right-side/form-builder";

const FormModal = () => {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [activePath, setActivePath] = useState("");

  const handleFolderSelect = (folderId: string, path: string) => {
    setSelectedFolderId(folderId);
    setActivePath(path);
  };

  return (
    <Modal
      show={false}
      theme={{
        body: {
          base: " h-full max-h-full max-w-full  ",
        },
        content: {
          base: "h-full max-h-full w-full !max-w-full p-4",
          inner: "h-full max-h-full max-w-full",
        },
      }}
    >
      <Modal.Body>
        <div className="grid h-full grid-cols-2 grid-rows-[100%]  gap-4 bg-white p-4">
          <QuestionsPanel
            selectedFolderId={selectedFolderId}
            onFolderSelect={handleFolderSelect}
          />
          <FormBuilder />
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default FormModal;
