"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>agEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToWindowEdges } from "@dnd-kit/modifiers";
import { LaptopMinimal, Minus, Plus, Type } from "lucide-react";
import React, { useState } from "react";

import { SearchField } from "@/components/shared";
import { Button } from "@/components/ui/button";

import { QuestionsTable } from "./questions-table";
import { QuestionsTree } from "./questions-tree";
import { TreeOverlay } from "./tree-overlay";
import { useMoveFolder, useMoveQuestion } from "./use-questions-mutations";
import { Question, QuestionFolder } from "./use-questions-queries";

type Props = {
  selectedFolderId: string | null;
  onFolderSelect: (folderId: string, path: string) => void;
};

export const QuestionsPanel = ({ selectedFolderId, onFolderSelect }: Props) => {
  const [draggingFolder, setDraggingFolder] = useState<QuestionFolder | null>(
    null,
  );
  const [draggingQuestion, setDraggingQuestion] = useState<Question | null>(
    null,
  );

  const { mutateAsync: moveFolder, isPending: isMovingFolder } =
    useMoveFolder();
  const { mutateAsync: moveQuestion, isPending: isMovingQuestion } =
    useMoveQuestion();

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 10,
    },
  });
  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 250,
      tolerance: 5,
    },
  });

  const sensors = useSensors(mouseSensor, touchSensor);

  const handleDragStart = (e: DragStartEvent) => {
    const unknownDraggingItem = e.active.data.current;

    if (unknownDraggingItem?.type === "question") {
      return setDraggingQuestion(unknownDraggingItem as Question);
    }
    setDraggingFolder(unknownDraggingItem as QuestionFolder);
  };

  const handleDragEnd = async (e: DragEndEvent) => {
    const source = e.active.data.current;
    const target = e.over?.data.current;

    setDraggingFolder(null);
    setDraggingQuestion(null);

    if (!source || !target) return;

    // Handle question move
    if (source?.type === "question") {
      const question = source as Question;
      if (question?.folderId === target?.id || !target?.id) return;

      await moveQuestion({
        questionId: question.id,
        folderId: target.id,
      });
      return;
    }

    // Handle folder move
    const folder = source as QuestionFolder;
    if (
      (!folder.parentDirectoryId && !target?.id) ||
      folder.id === target?.id ||
      folder.parentDirectoryId === target?.id
    )
      return;

    await moveFolder({
      folderId: folder.id,
      parentDirectory: target?.id || null,
    });
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[restrictToWindowEdges]}
    >
      <div className="flex h-full flex-col gap-4">
        <div className="flex items-center justify-between md:col-span-2">
          <h3 className="text-xl font-bold sm:text-2xl lg:text-3xl dark:text-white">
            Form Editor
          </h3>
          <Button variant="primary">
            <Plus /> New Question
          </Button>
        </div>
        <div className="grid flex-1 grid-cols-[250px_1fr] gap-4 md:grid-cols-[300px_1fr] md:flex-row lg:grid-cols-[320px_1fr]">
          <QuestionsTree
            selectedFolderId={selectedFolderId}
            onFolderSelect={onFolderSelect}
            draggingFolder={draggingFolder}
            draggingQuestion={draggingQuestion}
            isMovingFolder={isMovingFolder}
            isMovingQuestion={isMovingQuestion}
          />
          <QuestionsTable
            selectedFolderId={selectedFolderId}
            draggingQuestion={draggingQuestion}
            isMovingQuestion={isMovingQuestion}
          />
        </div>
      </div>

      <DragOverlay adjustScale={false} zIndex={9999} dropAnimation={null}>
        {!!draggingFolder && (
          <TreeOverlay
            folderName={draggingFolder?.name}
            questionCount={draggingFolder?.questionCount}
          />
        )}
      </DragOverlay>
    </DndContext>
  );
};
