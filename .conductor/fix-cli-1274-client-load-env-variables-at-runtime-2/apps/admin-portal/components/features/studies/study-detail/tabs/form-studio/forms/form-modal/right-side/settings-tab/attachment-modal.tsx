import { Checkbox } from "flowbite-react";
import { ChevronDown, ChevronRight, Folder, FolderOpen } from "lucide-react";
import React, { useState } from "react";

import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

type TreeFolder = {
  id: string;
  name: string;
  children?: TreeFolder[];
  formCount?: number;
};

type FolderTreeNodeProps = {
  folder: TreeFolder;
  level: number;
};

const FolderTreeNode = ({ folder, level }: FolderTreeNodeProps) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = folder.children && folder.children.length > 0;

  return (
    <>
      <li className="flex items-center gap-6 border-b px-4 py-2 last:border-b-0 hover:bg-gray-50 sm:px-6 dark:hover:bg-gray-800">
        <Checkbox id={folder.id} />
        <div
          className="flex flex-1 items-center justify-between"
          style={{ paddingLeft: `${level * 20}px` }}
        >
          <Label htmlFor={folder.id} className="flex items-center gap-2.5">
            {hasChildren ? (
              isExpanded ? (
                <FolderOpen className="size-5 text-gray-600 dark:text-gray-400" />
              ) : (
                <Folder className="size-5 text-gray-600 dark:text-gray-400" />
              )
            ) : (
              <Folder className="size-5 text-gray-600 dark:text-gray-400" />
            )}

            <span className="select-none text-sm font-medium text-gray-700 dark:text-gray-300">
              {folder.name}
            </span>

            {typeof folder.formCount === "number" && (
              <span className="ml-2 flex h-5 w-5 items-center justify-center rounded-full bg-blue-100 text-xs font-medium text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                {folder.formCount}
              </span>
            )}
          </Label>
          {hasChildren && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="w-fit rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-700"
            >
              {isExpanded ? (
                <ChevronDown className="size-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <ChevronRight className="size-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>
          )}
        </div>
      </li>

      {hasChildren &&
        isExpanded &&
        folder.children!.map((child) => (
          <FolderTreeNode key={child.id} folder={child} level={level + 1} />
        ))}
    </>
  );
};

export const AttachmentModal = () => {
  const mockFolders: TreeFolder[] = [
    {
      id: "1",
      name: "Medical Forms",
      formCount: 12,
      children: [
        {
          id: "1-1",
          name: "Patient Intake",
          formCount: 5,
          children: [
            { id: "1-1-1", name: "Demographics", formCount: 2 },
            { id: "1-1-2", name: "Medical History", formCount: 3 },
          ],
        },
        {
          id: "1-2",
          name: "Assessments",
          formCount: 7,
          children: [
            { id: "1-2-1", name: "Physical Exam", formCount: 4 },
            { id: "1-2-2", name: "Lab Results", formCount: 3 },
          ],
        },
      ],
    },
    {
      id: "2",
      name: "Administrative",
      formCount: 6,
      children: [
        { id: "2-1", name: "Consent Forms", formCount: 4 },
        { id: "2-2", name: "Insurance", formCount: 2 },
      ],
    },
    {
      id: "3",
      name: "Research",
      formCount: 8,
      children: [
        { id: "3-1", name: "Clinical Trials", formCount: 5 },
        { id: "3-2", name: "Surveys", formCount: 3 },
        { id: "3-4", name: "Surveys", formCount: 3 },
        { id: "3-5", name: "Surveys", formCount: 3 },
        { id: "3-6", name: "Surveys", formCount: 3 },
        { id: "3-7", name: "Surveys", formCount: 3 },
        { id: "3-8", name: "Surveys", formCount: 3 },
        { id: "3-9", name: "Surveys", formCount: 3 },
        { id: "3-10", name: "Surveys", formCount: 3 },
        { id: "3-11", name: "Surveys", formCount: 3 },
      ],
    },
  ];

  return (
    <WrapperModal
      size="4xl"
      isOpen={false}
      onClose={() => {}}
      title="Add Attachment"
    >
      <div className="flex flex-1 flex-col overflow-hidden rounded-lg border border-gray-200">
        <div className="sticky top-0 flex items-center gap-6 border-b px-4 py-3 lg:px-6">
          <Checkbox id="folders" />
          <Label htmlFor="folders">Folders </Label>
        </div>
        <ul className="max-h-96 flex-1 overflow-y-auto">
          {mockFolders.map((folder) => (
            <FolderTreeNode key={folder.id} folder={folder} level={0} />
          ))}
        </ul>
      </div>

      <div className="mt-4 flex justify-end gap-4">
        <Button variant="outline">Cancel</Button>
        <Button variant="primary">Save</Button>
      </div>
    </WrapperModal>
  );
};
