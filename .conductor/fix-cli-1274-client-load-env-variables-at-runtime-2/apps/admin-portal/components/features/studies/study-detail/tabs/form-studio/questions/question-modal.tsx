import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import { useParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteFields } from "@/hooks/queries/use-infinite-fields";
import { Question } from "@/lib/apis/questions/types";
import { cn } from "@/lib/utils";

import {
  useCreateQuestion,
  useUpdateQuestion,
} from "./hooks/use-questions-mutations";
import { LogQuestionColumns } from "./log-question-columns";

export const QUESTION_TYPES = [
  "SimpleQuestion",
  "GroupedQuestion",
  "LogQuestion",
] as const;

const baseSchema = z.object({
  questionType: z.enum(QUESTION_TYPES, {
    errorMap: () => ({ message: "Question type is required" }),
  }),
  questionName: z
    .string({
      required_error: "Question name is required",
      invalid_type_error: "Question name is required",
    })
    .min(1, "Question name is required"),
  description: z.string().optional(),
  displayId: z.string().optional(),
  config: z
    .object({
      fieldId: z.string().optional(),
      columns: z
        .array(
          z.object({
            field: z
              .object({
                id: z.string(),
                fieldName: z.string(),
                displayName: z.string(),
                fieldType: z.string(),
              })
              .optional(),
            description: z.string().optional(),
          }),
        )
        .optional(),
    })
    .optional(),
});

const schema = z.preprocess((input, ctx) => {
  const fieldData = baseSchema
    .pick({
      questionType: true,
      config: true,
    })
    .safeParse(input);

  if (fieldData.success) {
    const data = fieldData.data;
    if (data.questionType === "SimpleQuestion" && !data.config?.fieldId) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "Field variable is required for Simple Questions",
        path: ["config.fieldId"],
      });
    }
    if (
      data.questionType === "LogQuestion" &&
      (!data.config?.columns ||
        data.config.columns.length === 0 ||
        data.config.columns.some((col) => !col.field))
    ) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message:
          "At least one column with a selected field is required for Log Questions",
        path: ["config.columns"],
      });
    }
  }

  return input;
}, baseSchema);

export type QuestionValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedQuestion: Question | null;
  mode: "create" | "edit";
};

export const QuestionModal = function ({
  isOpen,
  onClose,
  selectedQuestion,
  mode,
}: Props) {
  const { id: studyId } = useParams<{ id: string }>();
  const isEditing = mode === "edit";

  const { mutateAsync: createQuestion, isPending: isCreating } =
    useCreateQuestion();
  const { mutateAsync: updateQuestion, isPending: isUpdating } =
    useUpdateQuestion();

  const formMethods = useForm<QuestionValues>({
    resolver: zodResolver(schema),
    mode: "onChange",
    defaultValues: {
      questionName: selectedQuestion?.questionName || "",
      questionType: selectedQuestion?.questionType,
      description: selectedQuestion?.description || "",
      displayId: selectedQuestion?.displayId || "",
      config: {
        fieldId: "",
        columns: [{ field: undefined, description: "" }],
      },
    },
  });

  const currentQuestionType = formMethods.watch("questionType");
  const isSimpleQuestion = currentQuestionType === "SimpleQuestion";
  const isLogQuestion = currentQuestionType === "LogQuestion";
  const isValid = formMethods.formState.isValid;
  const isSubmitting = isCreating || isUpdating;

  const onSubmit = async (data: QuestionValues) => {
    if (data.questionType === "SimpleQuestion" && data.config?.fieldId) {
      // SimpleQuestion requires fieldId
      const payload = {
        questionName: data.questionName,
        questionType: "SimpleQuestion" as const,
        studyId,
        description: data.description,
        config: {
          questionText: data.questionName,
          field: {
            fieldId: data.config.fieldId,
            isRequired: true,
          },
        },
      };

      isEditing && selectedQuestion
        ? await updateQuestion({ ...payload, id: selectedQuestion.id })
        : await createQuestion(payload);
    }
    // else if (data.questionType === "GroupedQuestion") {
    //   // GroupedQuestion has multiple fields (placeholder for now)
    //   const payload = {
    //     questionName: data.questionName,
    //     questionType: "GroupedQuestion" as const,
    //     description: data.description,
    //     config: {
    //       questionText: data.questionName,
    //       fields: [], // TODO: Implement fields selection UI
    //     },
    //   };

    //   isEditing && selectedQuestion
    //     ? await updateQuestion({ ...payload, id: selectedQuestion.id })
    //     : await createQuestion(payload);
    //  if (data.questionType === "LogQuestion" && data.config?.columns) {
    //   // LogQuestion has columns
    //   const payload = {
    //     questionName: data.questionName,
    //     questionType: "LogQuestion" as const,
    //     studyId,
    //     description: data.description,
    //     config: {
    //       questionText: data.questionName,
    //       columns: data.config.columns.map(col => ({
    //         fieldId: col.fieldId, // Include temporary placeholder fieldIds
    //         isRequired: true,
    //       })),
    //     },
    //   };

    //   isEditing && selectedQuestion
    //     ? await updateQuestion({ ...payload, id: selectedQuestion.id })
    //     : await createQuestion(payload);
    // }
    onClose();
  };

  return (
    <Modal
      show={isOpen}
      theme={{
        body: {
          base: "h-full max-h-full max-w-full",
        },
        content: {
          base: "h-full max-h-full w-full !max-w-full p-4",
          inner: "h-full max-h-full max-w-full",
        },
      }}
    >
      <Modal.Body>
        <div className="h-full bg-white p-4">
          <div className="flex h-full flex-col rounded border ">
            <div className="flex items-center justify-between gap-2 p-5">
              <h3 className=" text-2xl font-bold lg:text-3xl dark:text-white">
                Question Editor
              </h3>
              <button
                onClick={onClose}
                className="rounded-full p-2 transition-colors hover:bg-gray-100"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex-1 overflow-y-auto px-5 pb-5">
              <Form
                schema={schema}
                formMethods={formMethods}
                onSubmit={onSubmit}
                id="question-form"
                className="space-y-4 rounded-xl border p-5 shadow"
              >
                <div className="grid grid-cols-2 gap-4">
                  <div
                    className={cn(
                      "grid gap-4",
                      isSimpleQuestion ? "grid-cols-2" : "grid-cols-1",
                    )}
                  >
                    <div className="space-y-1">
                      <Label htmlFor="questionType">Question Type</Label>
                      <Select
                        name="questionType"
                        options={QUESTION_TYPES.map((type) => ({
                          label: type,
                          value: type,
                        }))}
                        onChange={() => {
                          formMethods.setValue("config.fieldId", "");
                          formMethods.setValue("config.columns", [
                            { field: undefined, description: "" },
                          ]);
                        }}
                        placeholder="Select question type"
                      />
                    </div>
                    {isSimpleQuestion && (
                      <div className="space-y-1">
                        <Label required htmlFor="config.fieldId">
                          Field Variable
                        </Label>
                        <LazySelect
                          id="config.fieldId"
                          name="config.fieldId"
                          searchPlaceholder="Search fields..."
                          useInfiniteQuery={useInfiniteFields}
                          getOptionLabel={(field) =>
                            `${field.displayName} (${field.fieldType})`
                          }
                          getOptionValue={(field) => field.id}
                          placeholder="Select field variable"
                          params={["PUBLISHED"]}
                        />
                      </div>
                    )}
                  </div>

                  <div
                    className={cn(
                      "grid gap-4",
                      isEditing ? "grid-cols-2" : "grid-cols-1",
                    )}
                  >
                    <div className="space-y-1">
                      <Label htmlFor="questionName">Question Name</Label>
                      <InputField
                        id="questionName"
                        name="questionName"
                        placeholder="Enter question name..."
                      />
                    </div>
                    {isEditing && (
                      <div className="space-y-1">
                        <Label htmlFor="displayId">Display ID</Label>
                        <InputField
                          id="displayId"
                          name="displayId"
                          placeholder="Enter display ID..."
                          disabled
                        />
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-1">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe what this question represents..."
                  />
                </div>

                {isLogQuestion && <LogQuestionColumns />}
              </Form>
            </div>
            <div className="flex flex-col justify-end gap-4 border-t p-5 sm:flex-row sm:gap-5">
              <CloseButton onClose={onClose} />
              <Button
                disabled={!isValid || isSubmitting}
                type="submit"
                form="question-form"
                variant="primary"
                isLoading={isSubmitting}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};
