import { ColumnDef } from "@tanstack/react-table";

import { TableEditButton } from "@/components/shared/table-action-buttons";

export type Variable = {
  id: string;
  name: string;
  description: string;
  value: string;
};

export const generateVariableColumns = (
  onEdit: (variable: Variable) => void,
): ColumnDef<Variable>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <button
        onClick={() => onEdit(row.original)}
        className="text-primary-500 hover:underline"
      >
        {row.original.name}
      </button>
    ),
  },
  {
    accessorKey: "value",
    header: "Value",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => (
      <div className="flex gap-2">
        <TableEditButton type="button" onClick={() => onEdit(row.original)} />
      </div>
    ),
  },
];
