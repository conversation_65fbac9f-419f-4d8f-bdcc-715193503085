import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { Variable } from "../columns";
import { variableKeys } from "./use-variables-queries";

// Type definitions for mutations
export type CreateVariablePayload = {
  name: string;
  description: string;
  value: string;
};

export type UpdateVariablePayload = CreateVariablePayload & {
  id: string;
};

export const useCreateVariable = () =>
  useMutation({
    mutationFn: (payload: CreateVariablePayload) => {
      return Promise.resolve({
        ...payload,
        id: Date.now().toString(),
      } as Variable);
    },
    onError: (err: any) =>
      toast.error(err?.message || "Failed to create variable"),
    onSettled: (_, err) =>
      !err && toast.success("Variable created successfully"),
    meta: {
      awaits: variableKeys.allLists(),
    },
  });

export const useUpdateVariable = () =>
  useMutation({
    mutationFn: (payload: UpdateVariablePayload) => {
      return Promise.resolve(payload as Variable);
    },
    onError: (err: any) =>
      toast.error(err?.message || "Failed to update variable"),
    onSettled: (_, err) =>
      !err && toast.success("Variable updated successfully"),
    meta: {
      awaits: variableKeys.allLists(),
    },
  });

export const useDeleteVariable = () =>
  useMutation({
    mutationFn: (id: string) => {
      return Promise.resolve({ success: true });
    },
    onError: (err: any) =>
      toast.error(err?.message || "Failed to delete variable"),
    onSettled: (_, err) =>
      !err && toast.success("Variable deleted successfully"),
    meta: {
      awaits: variableKeys.allLists(),
    },
  });
