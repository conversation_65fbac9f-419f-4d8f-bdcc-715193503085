import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import { useSort } from "@/hooks/use-sort";
import { MetadataParams } from "@/lib/apis/types";

import { Variable } from "../columns";

export const variableKeys = {
  all: () => ["variables"] as const,
  allLists: () => [...variableKeys.all(), "list"] as const,
  list: (params?: MetadataParams) =>
    [...variableKeys.allLists(), params] as const,
};

export const useVariables = () => {
  const { page, take } = usePagination();
  const { orderBy, orderDirection } = useSort();
  const { search } = useSearch("variableSearch");

  const params = {
    page,
    take,
    orderBy: orderBy || undefined,
    orderDirection: orderDirection || undefined,
    filter: {
      search: search || undefined,
    },
  };

  return useQuery({
    queryKey: variableKeys.list(params),
    queryFn: () => {
      const mockVariables: Variable[] = [
        {
          id: "1",
          name: "age",
          description: "Patient age at screening",
          value: "65",
        },
        {
          id: "2",
          name: "gender",
          description: "Patient gender",
          value: "Female",
        },
        {
          id: "3",
          name: "baseline_weight",
          description: "Patient weight at baseline visit",
          value: "72.5",
        },
        {
          id: "4",
          name: "study_duration",
          description: "Total study duration in weeks",
          value: "52",
        },
        {
          id: "5",
          name: "primary_diagnosis",
          description: "Primary diagnosis for study inclusion",
          value: "Type 2 Diabetes Mellitus",
        },
        {
          id: "6",
          name: "baseline_hba1c",
          description: "Hemoglobin A1c level at baseline",
          value: "8.2",
        },
        {
          id: "7",
          name: "concomitant_medications",
          description: "Number of concomitant medications",
          value: "3",
        },
        {
          id: "8",
          name: "visit_schedule",
          description: "Visit schedule pattern",
          value: "Week 0, 4, 8, 12, 16, 20, 24",
        },
      ];

      const filteredVariables = search
        ? mockVariables.filter(
            (variable) =>
              variable.name.toLowerCase().includes(search.toLowerCase()) ||
              variable.description
                .toLowerCase()
                .includes(search.toLowerCase()) ||
              variable.value.toLowerCase().includes(search.toLowerCase()),
          )
        : mockVariables;

      return Promise.resolve({
        results: filteredVariables,
        metadata: {
          currentPage: page || 1,
          totalPages: 1,
          totalCount: filteredVariables.length,
          pageSize: take || 10,
        },
      });
    },
    placeholderData: (prev) => prev,
  });
};
