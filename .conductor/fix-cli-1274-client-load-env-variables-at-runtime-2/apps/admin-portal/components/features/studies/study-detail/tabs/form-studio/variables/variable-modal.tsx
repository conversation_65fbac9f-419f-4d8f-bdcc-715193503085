import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";

import { Variable } from "./columns";

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required")
    .regex(
      /^[a-zA-Z0-9_]+$/,
      "Name can only contain letters, numbers, and underscores",
    ),
  description: z
    .string({
      required_error: "Description is required",
      invalid_type_error: "Description is required",
    })
    .min(1, "Description is required"),
  value: z
    .string({
      required_error: "Value is required",
      invalid_type_error: "Value is required",
    })
    .min(1, "Value is required"),
});

type FormValues = z.infer<typeof schema>;

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedVariable: Variable | null;
  onSave: (data: FormValues & { id?: string }) => void;
};

export const VariableModal = function ({
  isOpen,
  onClose,
  selectedVariable,
  onSave,
}: Props) {
  const isEditing = !!selectedVariable;

  const onSubmit = async (data: FormValues) => {
    const payload = isEditing ? { ...data, id: selectedVariable.id } : data;

    onSave(payload);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Variable`}
    >
      <Form
        defaultValues={{
          name: selectedVariable?.name || "",
          description: selectedVariable?.description || "",
          value: selectedVariable?.value || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="space-y-4">
          <div className="flex flex-col gap-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter variable name (e.g., patient_age, baseline_weight)..."
            />
          </div>
          <div className="flex flex-col gap-2">
            <Label htmlFor="value">Value</Label>
            <InputField
              id="value"
              name="value"
              placeholder="Enter the variable value..."
            />
          </div>

          <div className="flex flex-col gap-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Describe what this variable represents..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button type="submit" variant="primary">
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
