"use client";

import { Card } from "flowbite-react";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { SearchField } from "@/components/shared/search-field";
import { But<PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";

import { generateVariableColumns, Variable } from "./columns";
import { VariableModal } from "./variable-modal";

export const VariablesTab = () => {
  const {
    isOpen: isModalOpen,
    open: openModal,
    close: closeModal,
  } = useDisclosure();
  const [selectedVariable, setSelectedVariable] = useState<Variable | null>(
    null,
  );
  const [variables, setVariables] = useState<Variable[]>([
    {
      id: "1",
      name: "age",
      description: "Patient age at screening",
      value: "65",
    },
    {
      id: "2",
      name: "gender",
      description: "Patient gender",
      value: "Female",
    },
    {
      id: "3",
      name: "baseline_weight",
      description: "Patient weight at baseline visit",
      value: "72.5",
    },
    {
      id: "4",
      name: "study_duration",
      description: "Total study duration in weeks",
      value: "52",
    },
    {
      id: "5",
      name: "primary_diagnosis",
      description: "Primary diagnosis for study inclusion",
      value: "Type 2 Diabetes Mellitus",
    },
    {
      id: "6",
      name: "baseline_hba1c",
      description: "Hemoglobin A1c level at baseline",
      value: "8.2",
    },
    {
      id: "7",
      name: "concomitant_medications",
      description: "Number of concomitant medications",
      value: "3",
    },
    {
      id: "8",
      name: "visit_schedule",
      description: "Visit schedule pattern",
      value: "Week 0, 4, 8, 12, 16, 20, 24",
    },
  ]);

  const openAddModal = () => {
    setSelectedVariable(null);
    openModal();
  };

  const openEditModal = (variable: Variable) => {
    setSelectedVariable(variable);
    openModal();
  };

  const handleCloseModal = () => {
    closeModal();
    setSelectedVariable(null);
  };

  const handleSave = (data: any) => {
    if (data.id) {
      // Update existing variable
      setVariables((prev) =>
        prev.map((v) => (v.id === data.id ? { ...data } : v)),
      );
    } else {
      // Add new variable
      const newVariable = {
        ...data,
        id: Date.now().toString(),
      };
      setVariables((prev) => [...prev, newVariable]);
    }
  };

  const columns = useMemo(() => generateVariableColumns(openEditModal), []);

  const isPending = false; // Replace with actual loading state
  const isPlaceholderData = false; // Replace with actual placeholder state

  return (
    <Card className="border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800 [&>div]:p-0">
      <div className="border-b border-gray-200 p-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Variables
          </h3>

          <div className="flex items-center gap-4">
            {" "}
            <SearchField
              queryKey="variableSearch"
              placeholder="Search variables..."
            />
            <Button variant="primary" onClick={openAddModal}>
              <IoMdAdd className="h-4 w-4" /> New Variable
            </Button>
          </div>
        </div>
      </div>

      <div className="p-0">
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={variables} />
            {/* <TableDataPagination metadata={}  /> */}
          </LoadingWrapper>
        )}
      </div>

      <VariableModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        selectedVariable={selectedVariable}
        onSave={handleSave}
      />
    </Card>
  );
};
