import { type ColumnDef } from "@tanstack/react-table";
import { MdOutlineEdit } from "react-icons/md";

import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { Procedure } from "@/lib/apis/procedures";

export const getProcedureColumns = (
  onEdit: (procedure: Procedure) => void,
): ColumnDef<Procedure>[] => [
  {
    accessorKey: "name",
    header: "Name",
    cell: ({ row }) => (
      <span
        onClick={() => onEdit(row.original)}
        role="button"
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </span>
    ),
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => (
      <div className="line-clamp-2 text-gray-600 dark:text-gray-400">
        {row.getValue("description") || "No description"}
      </div>
    ),
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive");
      if (isActive) {
        return <PillBadge variant="success">Active</PillBadge>;
      }
      return <PillBadge variant="default">Inactive</PillBadge>;
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const procedure = row.original;
      return (
        <div
          className="text-primary-500 flex cursor-pointer items-center gap-1 text-xs font-medium"
          onClick={() => onEdit(procedure)}
        >
          <span className="whitespace-nowrap">Edit</span>
          <MdOutlineEdit />
        </div>
      );
    },
  },
];
