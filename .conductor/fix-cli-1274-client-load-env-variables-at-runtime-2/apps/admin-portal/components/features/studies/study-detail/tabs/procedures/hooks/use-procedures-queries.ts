import { useInfiniteQuery, useQuery } from "@tanstack/react-query";
import { useParams, useSearchParams } from "next/navigation";

import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";
import { TAKE_ALL } from "@/lib/constants";

export const studyProcedureKeys = {
  all: () => ["study-procedures"] as const,

  allList: (studyId: string) =>
    [...studyProcedureKeys.all(), studyId, "list"] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...studyProcedureKeys.allList(studyId), params] as const,
};

export const useProcedures = (id: string) => {
  return useQuery({
    queryKey: studyProcedureKeys.list(id, { take: TAKE_ALL }),
    queryFn: () => api.procedures.list(id),
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteProcedures = (search: string, initialPageSize = 50) => {
  const params = useParams();
  const searchParams = useSearchParams();
  const nonActive = searchParams.get("non-active") === "true";

  return useInfiniteQuery({
    queryKey: ["infinite-procedures", search, nonActive, params?.id],
    queryFn: ({ pageParam = 1 }) =>
      api.procedures.list(params?.id as string, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, isActive: nonActive ? undefined : true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
