import type { ColumnDef } from "@tanstack/react-table";
import { <PERSON><PERSON>ye, FiXCircle } from "react-icons/fi";
import { MdPublish } from "react-icons/md";

import { TableGenericButton } from "@/components/shared/table-action-buttons";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import type { Protocol } from "@/lib/apis/protocols";
import { cn, formatDate } from "@/lib/utils";

import { PROTOCOL_STATUS_VARIANT } from "./tabs/overview";

export const generateStudyProtocolColumns = ({
  onPublish,
  onSelect,
  selectedProtocol,
}: {
  onPublish: (protocolId: string) => void;
  onSelect: (protocol: Protocol | null) => void;
  selectedProtocol: Protocol | null;
}): ColumnDef<Protocol>[] => [
  {
    header: "Name",
    accessorKey: "name",
    cell: ({ row }) => (
      <span
        onClick={() => onSelect(row.original)}
        role="button"
        className="text-primary-500 hover:underline"
      >
        {row.getValue("name")}
      </span>
    ),
  },
  { header: "Version", accessorKey: "version" },
  {
    header: "Consent Required",
    accessorKey: "consentRequired",
    cell: ({ row }) => {
      if (row.original.consentRequired) {
        return <PillBadge variant="success">Yes</PillBadge>;
      }
      return <PillBadge variant="default">No</PillBadge>;
    },
  },
  {
    header: "Status",
    cell: ({ row }) => {
      return (
        <PillBadge
          variant={PROTOCOL_STATUS_VARIANT[row.original.status] ?? "default"}
        >
          {row.original.status}
        </PillBadge>
      );
    },
  },
  {
    header: "Date",
    accessorKey: "date",
    cell: ({ row }) => {
      if (!row.original.ammendmentDate) return null;
      return (
        <div>{formatDate(row.original.ammendmentDate, "MMM d, yyyy")}</div>
      );
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const protocol = row.original;
      const isSelected = selectedProtocol?.id === protocol.id;
      return (
        <div className="flex gap-4">
          <TableGenericButton
            onClick={() => onSelect(isSelected ? null : protocol)}
            type="button"
            className={cn(isSelected && "text-red-500 hover:text-red-600")}
          >
            {isSelected ? (
              <>
                Unselect
                <FiXCircle className="size-4" />
              </>
            ) : (
              <>
                Select
                <FiEye className="size-4" />
              </>
            )}
          </TableGenericButton>

          <button
            className="flex items-center gap-1 text-xs text-blue-500 hover:text-blue-600 disabled:cursor-not-allowed disabled:text-gray-300"
            onClick={() => onPublish(row.original.id as string)}
            disabled={protocol.isPublished}
          >
            Publish
            <MdPublish className="h-4 w-4" />
          </button>
        </div>
      );
    },
  },
];
