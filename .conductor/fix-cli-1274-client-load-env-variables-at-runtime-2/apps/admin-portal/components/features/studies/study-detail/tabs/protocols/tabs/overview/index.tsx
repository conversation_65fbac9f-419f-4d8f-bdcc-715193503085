import React, { useState } from "react";
import { CiEdit } from "react-icons/ci";

import { OverviewCard, OverviewItem } from "@/components/shared/overview-card";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Protocol } from "@/lib/apis/protocols";

import { ModalEditProtocol } from "./modal-edit-protocol";

export const PROTOCOL_STATUS_VARIANT = {
  "In Review": "warning",
  Approved: "primary",
  Active: "success",
  Expired: "danger",
} as const;

type Props = {
  selectedProtocol: Protocol | null;
  setSelectedProtocol: (protocol: Protocol | null) => void;
};
export const OverviewTab = ({
  selectedProtocol,
  setSelectedProtocol,
}: Props) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <OverviewCard
        rightContent={
          <Button
            disabled={selectedProtocol?.isPublished}
            variant="primary"
            onClick={() => setIsOpen(true)}
          >
            <CiEdit />
            Edit Protocol
          </Button>
        }
        title="Overview"
      >
        <div className="grid grid-cols-2 gap-4 gap-x-2">
          <OverviewItem label="Name" value={selectedProtocol?.name ?? "N/A"} />
          <OverviewItem
            label="Amendment Date"
            value={selectedProtocol?.ammendmentDate ?? "N/A"}
          />
          <OverviewItem
            label="Consent Required"
            value={selectedProtocol?.consentRequired ? "Yes" : "No"}
          />
          <OverviewItem label="Status">
            <PillBadge
              variant={
                selectedProtocol
                  ? PROTOCOL_STATUS_VARIANT[selectedProtocol?.status]
                  : "default"
              }
            >
              {selectedProtocol?.status}
            </PillBadge>
          </OverviewItem>
        </div>
      </OverviewCard>

      {isOpen && (
        <ModalEditProtocol
          selectedProtocol={selectedProtocol}
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          setSelectedProtocol={setSelectedProtocol}
        />
      )}
    </>
  );
};

export const ProtocolContentSkeleton = () => {
  return (
    <div className="flex flex-col">
      <div className="rounded-lg border border-gray-200 bg-white p-6 dark:border-gray-700 dark:bg-gray-800">
        <Skeleton className="mb-3 h-6 w-24" />
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-16" />
            <Skeleton className="h-4 w-20" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
          <div className="flex flex-col gap-1">
            <Skeleton className="h-5 w-24" />
            <Skeleton className="h-4 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
};
