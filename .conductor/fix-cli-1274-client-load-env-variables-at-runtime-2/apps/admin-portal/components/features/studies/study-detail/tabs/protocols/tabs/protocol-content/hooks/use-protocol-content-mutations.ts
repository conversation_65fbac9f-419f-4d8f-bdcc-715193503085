import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import type { AddProtocolContentPayload } from "@/lib/apis/protocols";

import { protocolKeys } from "../../../hooks/use-protocols-queries";
import { protocolContentKeys } from "./use-protocol-content-queries";

export const useAddProtocolContent = (protocolId?: string) => {
  return useMutation({
    mutationFn: (data: AddProtocolContentPayload) =>
      api.protocols.addContent(data),
    meta: {
      awaits: [
        protocolId ? protocolContentKeys.detail(protocolId) : [],
        protocolId ? protocolKeys.detail(protocolId) : [],
      ],
    },
    onSettled: (_, err) =>
      !err && toast.success("Protocol content updated successfully"),
    onError: (error) => {
      toast.error(error.message);
    },
  });
};
