import { Datepicker } from "@clincove/shared-ui";
import { useMutation } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { useState } from "react";
import toast from "react-hot-toast";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Checkbox, Form, InputField } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";
import api from "@/lib/apis";

import { useAddStudyProtocol } from "../../hooks/use-protocols-mutations";
import { protocolKeys } from "../../hooks/use-protocols-queries";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  consentRequired: z.boolean().default(false),
  ammendmentDate: z.string({
    required_error: "Date is required",
    invalid_type_error: "Date is required",
  }),
});

type ModalAddProtocolProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddProtocol = function ({
  isOpen,
  onClose,
}: ModalAddProtocolProps) {
  const { id } = useParams();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { mutateAsync: addProtocol } = useAddStudyProtocol(id as string);
  const { mutateAsync: uploadProtocolDocument } = useMutation({
    mutationFn: (data: { protocolId: string }) =>
      api.protocols.signedUploadUrl(data.protocolId),
    meta: {
      awaits: [protocolKeys.allLists(id as string)],
    },
  });

  async function onSubmit(data: z.infer<typeof schema>) {
    if (!id) return;

    setIsLoading(true);
    const protocol = await addProtocol({ ...data, hideToast: true });
    if (!protocol) return;

    try {
      const { url } = await uploadProtocolDocument({ protocolId: protocol.id });
      if (selectedFile) {
        const response = await fetch(url, {
          method: "PUT",
          body: selectedFile,
          headers: {
            "Content-Type": selectedFile.type,
          },
        });
        if (!response.ok) {
          throw new Error("Failed to upload document");
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      toast.success("Protocol added successfully");
      setIsLoading(false);
      onClose();
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    setSelectedFile(file);
  };

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add Protocol</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          defaultValues={{
            name: "",
            ammendmentDate: "",
            consentRequired: false,
          }}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <InputField
                id="name"
                name="name"
                placeholder="Enter protocol name..."
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="ammendmentDate">Date</Label>
              <Datepicker
                id="ammendmentDate"
                name="ammendmentDate"
                placeholder="Select date..."
                format="yyyy/mm/dd"
              />
            </div>

            <div className="space-y-1">
              <Label htmlFor="protocolDocument">Protocol Document</Label>
              <input
                type="file"
                id="protocolDocument"
                name="protocolDocument"
                onChange={handleFileChange}
                accept=".pdf"
                className="block w-full cursor-pointer rounded-lg border border-gray-300 bg-gray-50 text-sm text-gray-900 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400"
              />
            </div>
            <div className="flex items-center gap-2">
              <Checkbox id="consentRequired" name="consentRequired" />
              <Label htmlFor="consentRequired">Consent Required</Label>
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              disabled={isLoading}
              isLoading={isLoading}
            >
              Add Protocol
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
