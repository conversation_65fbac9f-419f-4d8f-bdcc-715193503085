import { useParams } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, <PERSON>Button } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

import { useAddActivityMutation } from "../../../activities/hooks/use-activities-mutations";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  isActive: z.boolean().default(true),
  description: z.string().optional(),
});

type ModalAddVisitProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddNewActivity = function ({
  isOpen,
  onClose,
}: ModalAddVisitProps) {
  const studyId = useParams()?.id as string;
  const { mutateAsync: create, isPending } = useAddActivityMutation(studyId);

  async function onSubmit(data: z.infer<typeof schema>) {
    await create({ ...data, studyId });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add New Activity</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
          defaultValues={{
            name: "",
            description: "",
            isActive: true,
          }}
        >
          <div className="grid grid-cols-1 gap-3 sm:gap-6">
            <div className="space-y-1">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name" />
            </div>

            <div className="space-y-1">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description"
                className="resize-none"
                rows={4}
              />
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              variant="primary"
              disabled={isPending}
              isLoading={isPending}
            >
              Add Activity
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
