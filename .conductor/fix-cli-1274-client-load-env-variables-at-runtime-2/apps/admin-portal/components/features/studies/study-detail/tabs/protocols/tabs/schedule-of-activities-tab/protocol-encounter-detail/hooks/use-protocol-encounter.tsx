import { useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const USE_PROTOCOL_ENCOUNTER_QUERY_KEY = "protocol-encounter";

/**
 * Custom hook to fetch a specific protocol encounter by protocol ID and encounter ID
 * @param id - Protocol ID
 * @param encounterId - Encounter ID
 * @param enabled - Flag to enable/disable the query
 * @returns Query result containing protocol encounter data
 */
export const useProtocolEncounter = (id?: string, encounterId?: string) => {
  return useQuery({
    queryKey: [USE_PROTOCOL_ENCOUNTER_QUERY_KEY, id, encounterId],
    queryFn: () => api.protocols.getProtocolEncounterById(id!, encounterId!),
    enabled: !!id && !!encounterId,
  });
};
