import type { ColumnDef } from "@tanstack/react-table";
import { MdDelete } from "react-icons/md";

import { ActiveStatusBadge } from "@/components/ui/badges";
import { PillBadge } from "@/components/ui/badges/pill-badge";
import { EncounterActivity } from "@/lib/apis/encounters";

export const generateEncounterActivitiesColumns = (
  onRemove: (activityId: string) => void,
  isPublished?: boolean,
) => {
  const columns: ColumnDef<EncounterActivity>[] = [
    { header: "Name", accessorKey: "name" },
    {
      header: "Description",
      accessorKey: "description",
    },
    {
      header: "Total Procedures",
      cell: ({ row }) => {
        return row.original.activityProcedures.length;
      },
    },
    {
      header: "System",
      accessorKey: "isSystem",
      cell: ({ getValue }) => {
        const value = getValue() as boolean;
        return (
          <PillBadge variant={value ? "success" : "default"}>
            {value ? "System" : "Custom"}
          </PillBadge>
        );
      },
    },
    {
      header: "Active",
      accessorKey: "isActive",
      cell: ({ getValue }) => {
        const value = getValue() as boolean;
        return <ActiveStatusBadge isActive={value} />;
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        return (
          <div className="flex gap-2">
            <button
              disabled={isPublished}
              className={`flex cursor-pointer items-center gap-1 ${
                isPublished
                  ? "text-gray-500"
                  : "text-red-500 hover:text-red-600"
              }`}
              onClick={() => onRemove(row.original.id)}
            >
              <MdDelete className="h-4 w-4" />
              Remove
            </button>
          </div>
        );
      },
    },
  ];

  return columns;
};
