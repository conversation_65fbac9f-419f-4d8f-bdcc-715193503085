import type { ColumnDef } from "@tanstack/react-table";

import { ActivityProcedure } from "@/lib/apis/activities";

export const generateActivityProceduresColumns = () => {
  const columns: ColumnDef<ActivityProcedure>[] = [
    { header: "Name", accessorKey: "name" },
    {
      header: "Description",
      accessorKey: "description",
    },
    {
      header: "is Active",
      accessorKey: "isActive",
    },
  ];

  return columns;
};
