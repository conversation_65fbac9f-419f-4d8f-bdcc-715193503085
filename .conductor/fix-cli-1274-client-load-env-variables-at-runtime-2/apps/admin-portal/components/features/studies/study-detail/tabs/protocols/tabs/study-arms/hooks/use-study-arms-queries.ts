import { useInfiniteQuery, useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const studyArmKeys = {
  all: () => ["study-arms"] as const,
  allLists: (protocolId: string) =>
    [...studyArmKeys.all(), protocolId, "list"] as const,
  list: (protocolId: string, params?: MetadataParams) =>
    [...studyArmKeys.allLists(protocolId), params] as const,
};

export const useProtocolStudyArms = (protocolId: string) => {
  const { page, take } = usePagination();

  const params = {
    page,
    take: take || 100,
  };

  return useQuery({
    queryKey: studyArmKeys.list(protocolId, params),
    queryFn: () => api.protocols.getProtocolStudyArms(protocolId, params),
    enabled: !!protocolId,
    placeholderData: (prevData) => prevData,
  });
};

export const useInfiniteStudyArms = (
  search: string,
  protocolId: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-study-arms", protocolId, search],
    queryFn: ({ pageParam = 1 }) =>
      api.protocols.getProtocolStudyArms(protocolId, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, isActive: true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
