import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { parseAsBoolean, useQueryState } from "nuqs";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import type { MetadataParams } from "@/lib/apis/types";

export const studyVisitTypeKeys = {
  all: () => ["study-visit-types"] as const,
  allList: (studyId: string) =>
    [...studyVisitTypeKeys.all(), studyId, "list"] as const,
  list: (studyId: string, params?: MetadataParams) =>
    [...studyVisitTypeKeys.allList(studyId), "list", params] as const,
};

export const useStudyVisitTypes = () => {
  const params = useParams();
  const studyId = params.id as string;
  const { page } = usePagination();
  const { search } = useSearch();
  const [isActive] = useQueryState("isActive", parseAsBoolean);

  const queryParams = {
    page,
    filter: {
      isActive,
      name: search || undefined,
    },
  };

  return useQuery({
    queryKey: studyVisitTypeKeys.list(studyId, queryParams),
    queryFn: () => api.visitTypes.getListByStudy({ ...queryParams, studyId }),
    placeholderData: (prevData) => prevData,
  });
};
