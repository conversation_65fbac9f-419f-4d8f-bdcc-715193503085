import { z } from "zod";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/button";
import { Form, InputField, Textarea } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { Modal } from "@/components/ui/modal";

const schema = z.object({
  name: z
    .string({ required_error: "Name is required" })
    .min(1, "Name is required"),
  description: z.string().optional(),
});

export type AddVisitTemplatePayload = z.infer<typeof schema>;

type ModalAddVisitTemplateProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AddVisitTemplatePayload) => Promise<void>;
  isAdding: boolean;
};

export const ModalAddVisitTemplate = function ({
  isOpen,
  onClose,
  onSubmit,
  isAdding,
}: ModalAddVisitTemplateProps) {
  return (
    <Modal show={isOpen} onClose={onClose} className="[&>div]:max-w-2xl">
      <Modal.Header>Add Visit</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={schema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <div className="grid grid-cols-1 gap-6">
            <div className="col-span-1 flex flex-col gap-2">
              <Label htmlFor="name">Name</Label>
              <InputField id="name" name="name" placeholder="Enter name..." />
            </div>

            <div className="col-span-1 flex flex-col gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description..."
              />
            </div>
          </div>

          <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
            <CloseButton onClose={onClose} />
            <Button type="submit" variant="primary" isLoading={isAdding}>
              Add Visit
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
