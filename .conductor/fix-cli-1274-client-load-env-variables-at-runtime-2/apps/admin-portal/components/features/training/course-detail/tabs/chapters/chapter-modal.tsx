import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form, InputField, Select, Textarea } from "@/components/ui/form";
import { InputNumber } from "@/components/ui/form/input-number-with-buttons";
import { Label } from "@/components/ui/form/label";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Chapter } from "@/lib/apis/training-modules";
import { capitalize } from "@/utils/string";

import {
  useAddChapter,
  useUpdateChapter,
} from "../../../hooks/use-training-module-mutations";

export const CHAPTER_TYPES = ["video", "blog", "mixed"] as const;

const schema = z.object({
  name: z
    .string({
      required_error: "Name is required",
      invalid_type_error: "Name is required",
    })
    .min(1, "Name is required"),
  type: z.enum(CHAPTER_TYPES, {
    errorMap: () => ({
      message: "Type is required",
    }),
  }),
  externalId: z.string().optional(),
  description: z.string().optional(),
  order: z.coerce
    .number({
      invalid_type_error: "Order must be a number",
    })
    .optional(),
});

type Props = {
  isOpen: boolean;
  onClose: () => void;
  selectedChapter: Chapter | null;
};

export const ChapterModal = function ({
  isOpen,
  onClose,
  selectedChapter,
}: Props) {
  const courseId = useParams().id as string;
  const { mutateAsync: addChapter, isPending: isAdding } =
    useAddChapter(courseId);
  const { mutateAsync: updateChapter, isPending: isUpdating } =
    useUpdateChapter(courseId);
  const isEditing = !!selectedChapter;

  const onSubmit = async (data: z.infer<typeof schema>) => {
    isEditing
      ? await updateChapter({
          ...data,
          id: selectedChapter.id,
        })
      : await addChapter(data);
    onClose();
  };

  return (
    <WrapperModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${isEditing ? "Edit" : "Add"} Chapter`}
    >
      <Form
        defaultValues={{
          name: selectedChapter?.name || "",
          description: selectedChapter?.description || "",
          type: selectedChapter?.type || "",
          order: selectedChapter?.order,
          externalId: selectedChapter?.externalId || "",
        }}
        mode="onChange"
        schema={schema}
        onSubmit={onSubmit}
      >
        <div className="grid gap-4 sm:grid-cols-2 sm:gap-6">
          <div className="space-y-2 sm:col-span-2">
            <Label htmlFor="name">Name</Label>
            <InputField
              id="name"
              name="name"
              placeholder="Enter chapter name..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type</Label>
            <Select
              id="type"
              name="type"
              options={CHAPTER_TYPES.map((type) => ({
                label: capitalize(type),
                value: type,
              }))}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="order">Order</Label>
            <InputNumber
              className="[&_input]:text-left"
              id="order"
              name="order"
              placeholder="Enter order..."
            />
          </div>

          <div className="space-y-2 sm:col-span-2">
            <Label htmlFor="externalId">External Id</Label>
            <InputField
              id="externalId"
              name="externalId"
              placeholder="Enter external id..."
            />
          </div>

          <div className="space-y-2 sm:col-span-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description..."
            />
          </div>
        </div>

        <div className="mt-4 flex flex-col justify-end gap-4 border-none pt-0 sm:mt-6 sm:flex-row sm:gap-5">
          <CloseButton onClose={onClose} />
          <Button
            type="submit"
            disabled={isAdding || isUpdating}
            isLoading={isAdding || isUpdating}
            variant="primary"
          >
            Save
          </Button>
        </div>
      </Form>
    </WrapperModal>
  );
};
