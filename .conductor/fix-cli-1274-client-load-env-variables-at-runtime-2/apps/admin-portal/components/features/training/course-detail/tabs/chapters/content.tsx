import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Chapter } from "@/lib/apis/training-modules";

import { useChaptersByCourseId } from "../../../hooks/use-training-module-queries";
import { ChapterModal } from "./chapter-modal";
import { generateChapterColumns } from "./columns";
import { ConfirmDeleteChapterModal } from "./confirm-delete-chapter-modal";

export const ChapterTab = () => {
  const courseId = useParams().id as string;

  const { isOpen, close, open } = useDisclosure();
  const [selectedChapter, setSelectedChapter] = useState<Chapter | null>(null);
  const [selectedChapterToDelete, setSelectedChapterToDelete] =
    useState<Chapter | null>(null);
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();
  const { data, isPending } = useChaptersByCourseId(courseId);

  const handleDeleteChapter = (chapter: Chapter) => {
    setSelectedChapterToDelete(chapter);
    openDeleteModal();
  };

  const handleCloseDeleteModal = () => {
    setSelectedChapterToDelete(null);
    closeDeleteModal();
  };

  const columns = useMemo(
    () =>
      generateChapterColumns({
        onView: (data) => {
          setSelectedChapter(data);
          open();
        },
        onDelete: handleDeleteChapter,
      }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [open],
  );

  const handleCloseModal = () => {
    setSelectedChapter(null);
    close();
  };

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-start justify-between p-4 pb-0 text-lg font-semibold sm:items-center">
          <h2 className="dark:text-gray-400">Chapters</h2>

          <Button onClick={open} variant="primary">
            <IoMdAdd />
            Add Chapter
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <ChapterModal
        isOpen={isOpen}
        onClose={handleCloseModal}
        selectedChapter={selectedChapter}
      />
      {selectedChapterToDelete && (
        <ConfirmDeleteChapterModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          selectedChapter={selectedChapterToDelete}
        />
      )}
    </>
  );
};
