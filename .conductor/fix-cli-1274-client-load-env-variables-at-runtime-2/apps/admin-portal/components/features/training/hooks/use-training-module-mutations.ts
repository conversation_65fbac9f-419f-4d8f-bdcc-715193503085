import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import api from "@/lib/apis";
import {
  AddChapterPayload,
  AddCoursePayload,
  AddEnrollmentPayload,
  AddModulePayload,
  UpdateChapterPayload,
  UpdateEnrollmentPayload,
  UpdateModulePayload,
} from "@/lib/apis/training-modules";

import { trainingModuleKeys } from "./use-training-module-queries";

export const useAddModule = () =>
  useMutation({
    mutationFn: (payload: AddModulePayload) =>
      api.trainingModules.addModule(payload),
    onError: (err) => toast.error(err?.message || "Fail to add module"),
    onSettled: (_, err) => !err && toast.success("Add module successfully"),
    meta: {
      awaits: trainingModuleKeys.allModuleLists(),
    },
  });

export const useUpdateModule = () =>
  useMutation({
    mutationFn: (payload: UpdateModulePayload) =>
      api.trainingModules.updateModule(payload),
    onError: (err) => toast.error(err?.message || "Fail to update module"),
    onSettled: (_, err) => !err && toast.success("Update module successfully"),
    meta: {
      awaits: trainingModuleKeys.allModuleLists(),
    },
  });

export const useDeleteModule = () =>
  useMutation({
    mutationFn: (id: string) => api.trainingModules.deleteModule(id),
    onError: (err) => toast.error(err?.message || "Fail to delete module"),
    onSettled: (_, err) => !err && toast.success("Delete module successfully"),
    meta: {
      awaits: trainingModuleKeys.allModuleLists(),
    },
  });

export const useAddCourse = () =>
  useMutation({
    mutationFn: (payload: AddCoursePayload) =>
      api.trainingModules.addCourse(payload),
    onError: (err) => toast.error(err?.message || "Fail to add course"),
    onSettled: (_, err) => !err && toast.success("Add course successfully"),
    meta: {
      awaits: trainingModuleKeys.allCourseLists(),
    },
  });

export const useUpdateCourse = (id: string) =>
  useMutation({
    mutationFn: (payload: AddCoursePayload) =>
      api.trainingModules.updateCourse({ ...payload, id }),
    onError: (err) => toast.error(err?.message || "Fail to update course"),
    onSettled: (_, err) => !err && toast.success("Update course successfully"),
    meta: {
      awaits: [
        trainingModuleKeys.allCourseLists(),
        trainingModuleKeys.courseDetail(id),
      ],
    },
  });

export const useSyncCourses = () =>
  useMutation({
    mutationFn: () => api.trainingModules.getCoursesFromStrapi(),
    onError: (err) => toast.error(err?.message || "Fail to sync courses"),
    onSettled: (_, err) => !err && toast.success("Sync courses successfully"),
    meta: {
      awaits: [trainingModuleKeys.all()],
    },
  });

export const useAddChapter = (courseId: string) =>
  useMutation({
    mutationFn: (payload: AddChapterPayload) =>
      api.trainingModules.addChapter({ ...payload, courseId }),
    onError: (err) => toast.error(err?.message || "Fail to add chapter"),
    onSettled: (_, err) => !err && toast.success("Add chapter successfully"),
    meta: {
      awaits: trainingModuleKeys.allChapterListsByCourseId(courseId),
    },
  });

export const useUpdateChapter = (courseId: string) =>
  useMutation({
    mutationFn: (payload: UpdateChapterPayload) =>
      api.trainingModules.updateChapter({ ...payload, courseId }),
    onError: (err) => toast.error(err?.message || "Fail to update chapter"),
    onSettled: (_, err) => !err && toast.success("Update chapter successfully"),
    meta: {
      awaits: trainingModuleKeys.allChapterListsByCourseId(courseId),
    },
  });

export const useAddEnrollment = (courseId: string) =>
  useMutation({
    mutationFn: (payload: Omit<AddEnrollmentPayload, "courseId">) =>
      api.trainingModules.addEnrollment({ ...payload, courseId }),
    onError: (err) => toast.error(err?.message || "Fail to add enrollment"),
    onSettled: (_, err) => !err && toast.success("Add enrollment successfully"),
    meta: {
      awaits: trainingModuleKeys.allEnrollmentListsByCourseId(courseId),
    },
  });

export const useUpdateEnrollment = (courseId: string) =>
  useMutation({
    mutationFn: (payload: Omit<UpdateEnrollmentPayload, "courseId">) =>
      api.trainingModules.updateEnrollment({ ...payload, courseId }),
    onError: (err) => toast.error(err?.message || "Fail to update enrollment"),
    onSettled: (_, err) =>
      !err && toast.success("Update enrollment successfully"),
    meta: {
      awaits: trainingModuleKeys.allEnrollmentListsByCourseId(courseId),
    },
  });

export const useDeleteEnrollment = (courseId: string) =>
  useMutation({
    mutationFn: (id: string) => api.trainingModules.deleteEnrollment(id),
    onError: (err) => toast.error(err?.message || "Fail to delete enrollment"),
    onSettled: (_, err) =>
      !err && toast.success("Delete enrollment successfully"),
    meta: {
      awaits: trainingModuleKeys.allEnrollmentListsByCourseId(courseId),
    },
  });

export const useDeleteCourse = () =>
  useMutation({
    mutationFn: (id: string) => api.trainingModules.deleteCourse(id),
    onError: (err) => toast.error(err?.message || "Fail to delete course"),
    onSettled: (_, err) => !err && toast.success("Delete course successfully"),
    meta: {
      awaits: trainingModuleKeys.allCourseLists(),
    },
  });

export const useDeleteChapter = (courseId: string) =>
  useMutation({
    mutationFn: (id: string) => api.trainingModules.deleteChapter(id),
    onError: (err) => toast.error(err?.message || "Fail to delete chapter"),
    onSettled: (_, err) => !err && toast.success("Delete chapter successfully"),
    meta: {
      awaits: trainingModuleKeys.allChapterListsByCourseId(courseId),
    },
  });
