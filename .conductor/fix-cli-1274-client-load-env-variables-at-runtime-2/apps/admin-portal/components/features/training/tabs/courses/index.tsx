import { functionalUpdate } from "@tanstack/react-table";
import { Card } from "flowbite-react";
import { useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { Button } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { useSort } from "@/hooks/use-sort";
import { Course } from "@/lib/apis/training-modules";

import { useCourses } from "../../hooks/use-training-module-queries";
import { generateCourseColumns } from "./columns";
import { ConfirmDeleteCourseModal } from "./confirm-delete-course-modal";
import { CourseModal } from "./course-modal";

export const CoursesTab = () => {
  const { isOpen, open, close } = useDisclosure();
  const { data, isPending, isPlaceholderData } = useCourses();
  const { changeSort, orderBy, orderDirection } = useSort();

  const [selectedCourseToDelete, setSelectedCourseToDelete] =
    useState<Course | null>(null);
  const {
    isOpen: isDeleteModalOpen,
    open: openDeleteModal,
    close: closeDeleteModal,
  } = useDisclosure();

  const handleDeleteCourse = (course: Course) => {
    setSelectedCourseToDelete(course);
    openDeleteModal();
  };

  const handleCloseDeleteModal = () => {
    setSelectedCourseToDelete(null);
    closeDeleteModal();
  };

  const columns = generateCourseColumns({
    onDelete: handleDeleteCourse,
  });

  const sorting = !orderBy
    ? []
    : [{ id: orderBy, desc: orderDirection === "desc" }];
  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
          <div className="dark:text-gray-400">Courses</div>
          <Button variant="primary" onClick={open}>
            <IoMdAdd />
            Add Course
          </Button>
        </div>
        {isPending ? (
          <TableLoading columns={columns} />
        ) : (
          <>
            <LoadingWrapper isLoading={isPlaceholderData}>
              <Table
                columns={columns}
                enableSorting
                data={data?.results ?? []}
                sorting={sorting}
                onSortingChange={(updater) => {
                  const newSorting = functionalUpdate(updater, sorting);
                  const sort = newSorting[0];
                  if (sort)
                    return changeSort(sort.id, sort.desc ? "desc" : "asc");
                  changeSort();
                }}
              />
            </LoadingWrapper>
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </>
        )}
      </Card>
      <CourseModal isOpen={isOpen} onClose={close} selectedCourse={null} />
      {selectedCourseToDelete && (
        <ConfirmDeleteCourseModal
          isOpen={isDeleteModalOpen}
          onClose={handleCloseDeleteModal}
          selectedCourse={selectedCourseToDelete}
        />
      )}
    </>
  );
};
