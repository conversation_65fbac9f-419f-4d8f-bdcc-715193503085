import { Card } from "flowbite-react";
import React, { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import LoadingWrapper from "@/components/shared/loading-wrapper";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import { useDisclosure } from "@/hooks/use-disclosure";
import { Module } from "@/lib/apis/training-modules";

import { useModules } from "../../hooks/use-training-module-queries";
import { generateModulesColumns } from "./columns";
import { ConfirmDeleteModuleModal } from "./confirm-delete-module-modal";
import { ModuleModal } from "./module-modal";

export const ModulesTab = () => {
  const {
    isOpen: isOpenModuleModal,
    open: onOpenModuleModal,
    close: onCloseModuleModal,
  } = useDisclosure();

  const [selectedModule, setSelectedModule] = useState<Module | null>(null);
  const [selectedModuleToDelete, setSelectedModuleToDelete] =
    useState<Module | null>(null);

  const { data, isPending, isPlaceholderData } = useModules();

  const handleCloseModuleModal = () => {
    onCloseModuleModal();
    setSelectedModule(null);
  };
  const handleCloseConfirmModal = () => {
    setSelectedModuleToDelete(null);
  };

  const columns = useMemo(
    () =>
      generateModulesColumns({
        onView: (module) => {
          setSelectedModule(module);
          onOpenModuleModal();
        },
        onDelete: async (module) => {
          setSelectedModuleToDelete(module);
        },
      }),
    [onOpenModuleModal],
  );

  return (
    <Card className="[&>div]:p-0">
      <div className="mb-4 flex items-center justify-between p-4 pb-0 text-lg font-semibold">
        <div className="dark:text-gray-400">Modules</div>
        <Button variant="primary" onClick={onOpenModuleModal}>
          <IoMdAdd />
          Add Modules
        </Button>
      </div>
      {isPending ? (
        <TableLoading columns={columns} />
      ) : (
        <>
          <LoadingWrapper isLoading={isPlaceholderData}>
            <Table columns={columns} data={data?.results ?? []} />
            {data?.metadata && <TableDataPagination metadata={data.metadata} />}
          </LoadingWrapper>
        </>
      )}
      {isOpenModuleModal && (
        <ModuleModal
          isOpen={isOpenModuleModal}
          onClose={handleCloseModuleModal}
          selectedModule={selectedModule}
        />
      )}
      {selectedModuleToDelete && (
        <ConfirmDeleteModuleModal
          isOpen={!!selectedModuleToDelete}
          onClose={handleCloseConfirmModal}
          selectedModule={selectedModuleToDelete}
        />
      )}
    </Card>
  );
};
