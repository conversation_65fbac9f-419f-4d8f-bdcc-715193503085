"use client";

import { Card } from "flowbite-react";
import { type FC, useMemo, useState } from "react";

import { HeaderActions } from "@/components/shared/header-actions";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { PageHeader } from "@/components/ui/page-header";
import { TableDataPagination } from "@/components/ui/pagination";
import { Table, TableLoading } from "@/components/ui/table";
import type { User } from "@/lib/apis/users/types";

import { useUpdateUserStatus } from "../hooks/use-users-mutations";
import { useUsers } from "../hooks/use-users-queries";
import { generateUsersColumns } from "./columns";
import { ModalAddUser } from "./modal-add-user";

const BREADCRUMB_ITEMS = [{ label: "Users" }];

const UserPageContent: FC = function () {
  const { data, isPending } = useUsers();
  const { mutateAsync: updateUserStatus } = useUpdateUserStatus();
  const columns = useMemo(
    () =>
      generateUsersColumns({
        onToggleStatus: (id, isActive) => {
          updateUserStatus({ id, isActive });
        },
      }),
    [updateUserStatus],
  );

  const [showModalAddUser, setShowModalAddUser] = useState(false);

  const exportCSVData = useMemo(() => {
    if (!data || !data.results) return [];

    const headers = [
      { label: "ID", key: "id" },
      { label: "First Name", key: "firstName" },
      { label: "Last Name", key: "lastName" },
      { label: "Email", key: "email" },
      { label: "Phone", key: "phone" },
      { label: "Status", key: "status" },
    ];

    return [
      headers.map((header) => header.label),
      ...data.results.map((user) =>
        headers.map((header) => {
          if (header.key === "status") {
            return user.isActive ? "Active" : "Inactive";
          }
          return user[header.key as keyof User];
        }),
      ),
    ];
  }, [data]);
  return (
    <>
      <div className="space-y-4">
        <Breadcrumb items={BREADCRUMB_ITEMS} />
        <PageHeader>Users</PageHeader>
        <Card className="[&>div]:p-0">
          <HeaderActions
            data={exportCSVData}
            buttonText="Add User"
            filename="users.csv"
            onButtonClick={() => setShowModalAddUser(true)}
          />
          {isPending ? (
            <TableLoading columns={columns} />
          ) : (
            <>
              <Table data={data?.results ?? []} columns={columns} />
              {data?.metadata && (
                <TableDataPagination metadata={data.metadata} />
              )}
            </>
          )}
        </Card>
      </div>
      {showModalAddUser && (
        <ModalAddUser
          isOpen={showModalAddUser}
          onClose={() => setShowModalAddUser(false)}
        />
      )}
    </>
  );
};

export default UserPageContent;
