import { useQuery } from "@tanstack/react-query";

import { usePagination } from "@/hooks/use-pagination";
import { useSearch } from "@/hooks/use-search";
import api from "@/lib/apis";
import { MetadataParams } from "@/lib/apis/types";

export const usersKeys = {
  all: () => ["users"] as const,
  allLists: () => [...usersKeys.all(), "list"] as const,
  list: (params?: MetadataParams) => [...usersKeys.allLists(), params] as const,
  allDetails: () => [...usersKeys.all(), "detail"] as const,
  detail: (id: string) => [...usersKeys.allDetails(), id] as const,
  allInvitations: () => [...usersKeys.all(), "invitations"] as const,
  invitations: (params?: MetadataParams) =>
    [...usersKeys.allInvitations(), params] as const,
};

export const useUsers = (param?: MetadataParams["filter"]) => {
  const { page, take } = usePagination();
  const { search } = useSearch();

  const params = {
    page,
    take,
    filter: { name: search, ...param },
  };

  return useQuery({
    queryKey: usersKeys.list(params),
    queryFn: () => api.users.list(params),
    placeholderData: (prevData) => prevData,
  });
};

export const useUser = (id?: string) => {
  return useQuery({
    queryKey: usersKeys.detail(id!),
    queryFn: () => api.users.get(id!),
    enabled: !!id,
  });
};

export const useUserInvitations = () => {
  const { search } = useSearch();
  const { page, take } = usePagination();
  const params = {
    page,
    take,
    filter: {
      email: search,
    },
  };

  return useQuery({
    queryKey: usersKeys.invitations(params),
    queryFn: () => api.users.listInvitations(params),
    placeholderData: (prev) => prev,
  });
};
