import type { ColumnDef } from "@tanstack/react-table";
import { Button } from "flowbite-react";
import { MdBlock } from "react-icons/md";

import { TableGenericButton } from "@/components/shared/table-action-buttons";
import type { UserInvitation } from "@/lib/apis/users/types";
import { formatDate } from "@/lib/utils";

import { InvitationStatusBadge } from "./invitation-status-badge";

export const generateInvitationsColumns = (
  onRevoke: (invitation: UserInvitation) => void,
  isRevoking: boolean,
) => {
  const columns: ColumnDef<UserInvitation>[] = [
    {
      header: "Name",
      accessorFn: (row) =>
        row.publicMetadata
          ? `${row.publicMetadata.firstName} ${row.publicMetadata.lastName}`.trim()
          : "-",
    },
    {
      header: "Email",
      accessorKey: "emailAddress",
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: ({ row }) => {
        const invitation = row.original;
        return <InvitationStatusBadge status={invitation.status} />;
      },
    },
    {
      header: "Created Date",
      accessorKey: "createdAt",
      cell: ({ row }) => {
        return (
          <span className="whitespace-nowrap">
            {formatDate(row.original.createdAt)}
          </span>
        );
      },
    },
    {
      header: "Last Updated",
      accessorKey: "updatedAt",
      cell: ({ row }) => {
        return (
          <span className="whitespace-nowrap">
            {formatDate(row.original.updatedAt)}
          </span>
        );
      },
    },
    {
      header: "Actions",
      id: "actions",
      cell: ({ row }) => {
        const invitation = row.original;
        if (invitation.revoked || invitation.status !== "pending") {
          return null;
        }

        return (
          <TableGenericButton
            type="button"
            className="text-red-500 hover:text-red-700"
            onClick={() => {
              onRevoke(invitation);
            }}
          >
            Revoke
            <MdBlock />
          </TableGenericButton>
        );
      },
    },
  ];

  return columns;
};
