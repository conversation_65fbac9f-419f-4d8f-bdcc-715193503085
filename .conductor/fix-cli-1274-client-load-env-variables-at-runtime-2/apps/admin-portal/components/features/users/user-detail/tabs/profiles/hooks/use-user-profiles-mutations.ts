import { useMutation } from "@tanstack/react-query";
import toast from "react-hot-toast";

import { usersKeys } from "@/components/features/users/hooks/use-users-queries";
import api from "@/lib/apis";
import type { ChangeUserProfileStatusPayload } from "@/lib/apis/users/types";

import { userProfileKeys } from "./use-user-profiles-queries";

type AddProfilePayload = {
  userId: string;
  groupId: string;
  roleId: string;
};

export const useAddProfile = (userId: string) => {
  return useMutation({
    mutationFn: (payload: Omit<AddProfilePayload, "userId">) =>
      api.users.addProfile({ ...payload, userId }),
    onSettled: (_, err) => !err && toast.success("Add profile successfully"),
    onError: (err) => toast.error(err?.message || "Fail to add profile"),
    meta: { awaits: userProfileKeys.list(userId) },
  });
};

export const useAddUserCustomPermission = (userId: string) => {
  return useMutation({
    mutationFn: (payload: { profileId: string; studyId: string }) => {
      return api.users.addUserCustomPermission({ ...payload });
    },
    onSettled: (_, err) => !err && toast.success("Add study successfully"),
    onError: (err) => {
      toast.error(err.message || "Failed to add study");
    },
    meta: { awaits: userProfileKeys.list(userId) },
  });
};

export const useDeleteUserCustomPermission = (userId: string) => {
  return useMutation({
    mutationFn: (payload: { profileId: string; studyId: string }) => {
      return api.users.deleteUserCustomPermission({ ...payload });
    },
    onSettled: (_, err) => !err && toast.success("Delete study successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to delete study");
    },
    meta: { awaits: userProfileKeys.list(userId) },
  });
};

export const useRestoreUserCustomPermission = (userId: string) => {
  return useMutation({
    mutationFn: (payload: { profileId: string }) => {
      return api.users.restoreUserCustomPermission({ ...payload });
    },
    onSettled: (_, err) => !err && toast.success("Restore study successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to restore study");
    },
    meta: { awaits: userProfileKeys.list(userId) },
  });
};

export const useUpdateRoleProfile = (userId: string) => {
  return useMutation({
    mutationFn: (payload: { roleId: string; profileId: string }) => {
      return api.users.updateRoleProfile({ ...payload });
    },
    onSettled: (_, err) => !err && toast.success("Update role successfully"),
    onError: (err) => {
      toast.error(err.message || "Fail to update role");
    },
    meta: { awaits: userProfileKeys.list(userId) },
  });
};

export const useUpdateProfileStatus = (userId: string) => {
  return useMutation({
    mutationFn: (payload: Omit<ChangeUserProfileStatusPayload, "userId">) => {
      return api.users.changeProfileStatus({ ...payload, userId });
    },
    onSettled: (_, err, payload) =>
      !err &&
      toast.success(
        `${payload.isActive ? "Enabled" : "Disabled"} profile successfully`,
      ),
    onError: (err) => {
      toast.error(err.message);
    },
    meta: {
      awaits: [userProfileKeys.list(userId), usersKeys.detail(userId)],
    },
  });
};
