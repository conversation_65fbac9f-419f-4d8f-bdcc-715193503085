import { skipToken, useInfiniteQuery, useQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const userProfileKeys = {
  all: () => ["user-profiles"] as const,
  allLists: () => [...userProfileKeys.all(), "list"] as const,
  list: (userId: string) => [...userProfileKeys.allLists(), userId] as const,
  allCustomPermissions: (userId: string) =>
    [...userProfileKeys.list(userId), "custom-permission"] as const,
  customPermission: (userId: string, profileId: string) =>
    [...userProfileKeys.allCustomPermissions(userId), profileId] as const,
  allInfiniteLists: () => [...userProfileKeys.all(), "infinite"] as const,
  infiniteList: (userId: string, search?: string) =>
    [...userProfileKeys.allInfiniteLists(), userId, search] as const,
};

export const useUserProfiles = (userId: string) => {
  return useQuery({
    queryKey: userProfileKeys.list(userId),
    queryFn: () => api.users.profiles(userId),
    enabled: !!userId,
  });
};

export const useInfiniteUserProfiles = (
  search: string,
  userId: string,
  initialPageSize = 10,
) => {
  return useInfiniteQuery({
    queryKey: userProfileKeys.infiniteList(userId, search),
    queryFn: ({ pageParam = 1 }) =>
      api.users.profileList(userId, {
        page: pageParam,
        take: initialPageSize,
        filter: { name: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};

export const useUserCustomPermission = (userId: string, profileId?: string) => {
  return useQuery({
    queryKey: profileId
      ? userProfileKeys.customPermission(userId, profileId)
      : [],
    queryFn: profileId
      ? () => api.users.getUserCustomPermission(profileId)
      : skipToken,
  });
};
