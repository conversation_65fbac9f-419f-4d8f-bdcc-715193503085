import { Card } from "flowbite-react";
import { useParams } from "next/navigation";
import { useMemo, useState } from "react";
import { IoMdAdd } from "react-icons/io";

import { Button } from "@/components/ui/button";
import { ConfirmModal } from "@/components/ui/modal/confirm-modal";
import { Table, TableLoading } from "@/components/ui/table";
import { Profile } from "@/lib/apis/groups/types";

import { generateUserProfilesColumns } from "./columns";
import { useUpdateProfileStatus } from "./hooks/use-user-profiles-mutations";
import { useUserProfiles } from "./hooks/use-user-profiles-queries";
import { ModalAddUserProfile } from "./modal-add-user-profile";
import { ModalEditProfile } from "./modal-edit-profile";
import StudiesModal from "./studies-modal";

export const ProfilesSection = () => {
  const { id } = useParams();
  const { data, isLoading } = useUserProfiles(id as string);
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const [editingProfile, setEditingProfile] = useState<Profile | null>(null);

  const [isModalAddProfileOpen, setIsModalAddProfileOpen] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [profileToDisable, setProfileToDisable] = useState<{
    profileId: string;
    isActive: boolean;
  } | null>(null);

  const { mutateAsync: updateProfileStatus, isPending } =
    useUpdateProfileStatus(id as string);

  const onClickProfileRow = (data: Profile) => {
    setSelectedProfile(data);
  };
  const onCloseStudiesModal = () => {
    setSelectedProfile(null);
  };

  const onEditProfile = (data: Profile) => {
    setEditingProfile(data);
  };

  const onCloseEditProfileModal = () => {
    setEditingProfile(null);
  };

  const handleDisableProfile = async (profileId: string, isActive: boolean) => {
    // Check if this is the only active profile left when trying to disable
    if (!isActive) {
      const activeProfiles =
        data?.results?.filter((profile) => profile.isActive) || [];
      const isLastActiveProfile =
        activeProfiles.length === 1 && activeProfiles[0].id === profileId;

      if (isLastActiveProfile) {
        setProfileToDisable({ profileId, isActive });
        setShowConfirmModal(true);
        return;
      }
    }

    await updateProfileStatus({
      profileId,
      isActive,
    });
  };

  const handleConfirmDisable = async () => {
    if (profileToDisable) {
      await updateProfileStatus({
        profileId: profileToDisable.profileId,
        isActive: profileToDisable.isActive,
      });
    }
    setShowConfirmModal(false);
    setProfileToDisable(null);
  };

  const handleCloseConfirmModal = () => {
    setShowConfirmModal(false);
    setProfileToDisable(null);
  };

  const columns = useMemo(
    () =>
      generateUserProfilesColumns(
        handleDisableProfile,
        onClickProfileRow,
        onEditProfile,
      ),
    [data?.results, id, updateProfileStatus],
  );

  return (
    <>
      <Card className="[&>div]:p-0">
        <div className="mb-3 flex items-center justify-between px-6 pt-4">
          <span className="mb-3 text-lg font-semibold dark:text-gray-400">
            Profiles
          </span>
          {data?.results &&
          data.results.filter(
            (profile) => profile.currentGroup.type === "clincove",
          ).length === 0 ? (
            <Button
              variant="primary"
              onClick={() => setIsModalAddProfileOpen(true)}
            >
              <IoMdAdd />
              Add Profile
            </Button>
          ) : null}
        </div>
        {isLoading ? (
          <TableLoading columns={columns} />
        ) : (
          <Table columns={columns} data={data?.results ?? []} />
        )}
      </Card>
      <ModalAddUserProfile
        isOpen={isModalAddProfileOpen}
        onClose={() => setIsModalAddProfileOpen(false)}
      />

      {selectedProfile && (
        <StudiesModal
          selectedProfile={selectedProfile}
          setSelectedProfile={setSelectedProfile}
          onClose={onCloseStudiesModal}
        />
      )}
      {editingProfile && (
        <ModalEditProfile
          selectedProfile={editingProfile}
          onClose={onCloseEditProfileModal}
        />
      )}

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCloseConfirmModal}
        onConfirm={handleConfirmDisable}
        title="Disable Profile"
        confirmLabel="Disable"
        isLoading={isPending}
      >
        <span className="dark:text-white">
          If you disable this profile, the associated user account will also be
          disabled.
        </span>
      </ConfirmModal>
    </>
  );
};
