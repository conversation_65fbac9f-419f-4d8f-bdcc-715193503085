import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { useState } from "react";
import { z } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteGroups } from "@/hooks/queries/use-infinite-groups";
import { useInfiniteRoles } from "@/hooks/queries/use-infinite-roles";
import { RoleWithExtendType } from "@/lib/apis/roles";
import { capitalize } from "@/utils/string";

import { ADMIN_GROUP } from "../../../default/modal-add-user";
import { useAddProfile } from "./hooks/use-user-profiles-mutations";

const addProfileSchema = z.object({
  roleId: z
    .string({ required_error: "Role is required" })
    .min(1, "Role is required"),
  groupId: z
    .string({ required_error: "Group is required" })
    .min(1, "Group is required"),
});

type ModalAddUserProfileProps = {
  isOpen: boolean;
  onClose: () => void;
};

export const ModalAddUserProfile = ({
  isOpen,
  onClose,
}: ModalAddUserProfileProps) => {
  const { id } = useParams();
  const { mutateAsync: addProfile, isPending } = useAddProfile(id as string);

  async function onSubmit(data: z.infer<typeof addProfileSchema>) {
    if (!id) return;
    await addProfile({
      ...data,
      roleId: JSON.parse(data.roleId).id,
    });
    onClose();
  }

  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Add Profile</Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={addProfileSchema}
          onSubmit={onSubmit}
          formProps={{ shouldFocusError: false }}
        >
          <UserProfileForm onClose={onClose} isSubmitting={isPending} />
        </Form>
      </Modal.Body>
    </Modal>
  );
};

type UserProfileFormProps = {
  onClose: () => void;
  isSubmitting?: boolean;
};

const UserProfileForm = ({ onClose, isSubmitting }: UserProfileFormProps) => {
  const [selectedRole, setSelectedRole] = useState<RoleWithExtendType | null>(
    null,
  );
  return (
    <div className="space-y-4">
      <div className="grid gap-2">
        <div className=" flex flex-col gap-2">
          <Label htmlFor="roleId">Role</Label>
          <LazySelect
            name="roleId"
            id="roleId"
            searchPlaceholder="Search role..."
            useInfiniteQuery={useInfiniteRoles}
            getOptionLabel={(role) => `${role.name} (${capitalize(role.type)})`}
            getOptionValue={(role) => JSON.stringify(role)}
            params={[]}
            onSelect={(e) => {
              setSelectedRole(JSON.parse(e as string));
            }}
            placeholder="Select role"
          />
        </div>

        <div className=" flex flex-col gap-2">
          <Label htmlFor="groupId">Group</Label>
          <LazySelect
            name="groupId"
            id="groupId"
            searchPlaceholder="Search groups..."
            useInfiniteQuery={useInfiniteGroups}
            getOptionLabel={(group) =>
              `${group.name} (${group.type === ADMIN_GROUP ? "Admin" : capitalize(group.type || "")})`
            }
            getOptionValue={(group) => group.id}
            params={[
              selectedRole?.type === "admin" ? ADMIN_GROUP : selectedRole?.type,
            ]}
            placeholder="Select group"
          />
        </div>
      </div>
      <div className="flex flex-col justify-end gap-4 sm:flex-row">
        <CloseButton onClose={onClose} />
        <Button type="submit" variant="primary" isLoading={isSubmitting}>
          Add Profile
        </Button>
      </div>
    </div>
  );
};
