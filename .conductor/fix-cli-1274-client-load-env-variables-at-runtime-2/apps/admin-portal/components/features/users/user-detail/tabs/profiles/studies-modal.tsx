import { useParams } from "next/navigation";
import React, { Dispatch, SetStateAction } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { WrapperModal } from "@/components/ui/modal/wrapper-modal";
import { Tabs, TabsItem } from "@/components/ui/tabs";
import { Profile } from "@/lib/apis/groups/types";

import { ActivePermissions } from "./active-permissions-tab";
import { customPermissionColumn } from "./columns";
import { DefaultPermissions } from "./default-permissions-tab";
import { useRestoreUserCustomPermission } from "./hooks/use-user-profiles-mutations";

type Props = {
  onClose: () => void;
  selectedProfile: Profile;
  setSelectedProfile: Dispatch<SetStateAction<Profile | null>>;
};

const StudiesModal = ({
  onClose,
  selectedProfile,
  setSelectedProfile,
}: Props) => {
  const userId = useParams().id as string;
  const { mutateAsync, isPending: isRestoring } =
    useRestoreUserCustomPermission(userId);

  const handleRestorePermission = () => {
    mutateAsync(
      {
        profileId: selectedProfile.id,
      },
      {
        onSuccess: (response) => {
          setSelectedProfile((prev) =>
            prev
              ? {
                  ...prev,
                  hasCustomPermissions: response.hasCustomStudyPermissions,
                }
              : null,
          );
        },
      },
    );
  };

  return (
    <WrapperModal
      isOpen={!!selectedProfile}
      onClose={onClose}
      title="Studies"
      className="[&>div]:max-w-3xl"
    >
      <div className="grid gap-2">
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile?.currentGroup?.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Group Type
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile?.currentGroup?.type || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Profile Name
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile?.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">Site</span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {selectedProfile?.currentGroup?.site?.name || "N/A"}
          </span>
        </div>
        <div className="grid grid-cols-2 gap-x-2 sm:grid-cols-3">
          <span className="text-md font-medium dark:text-gray-400">
            Custom Permissions
          </span>
          <span className="text-sm text-gray-500 sm:col-span-2">
            {customPermissionColumn[`${selectedProfile.hasCustomPermissions}`]}
          </span>
        </div>
      </div>
      <div className="mt-2 flex justify-end">
        <Button
          onClick={handleRestorePermission}
          disabled={!selectedProfile.hasCustomPermissions || isRestoring}
          isLoading={isRestoring}
          variant="primary"
        >
          Restore to default
        </Button>
      </div>
      <Tabs>
        <TabsItem title="Active Permissions">
          <ActivePermissions
            selectedProfile={selectedProfile}
            groupId={selectedProfile.currentGroupId}
            setSelectedProfile={setSelectedProfile}
          />
        </TabsItem>
        <TabsItem title="Default Permissions">
          <DefaultPermissions groupId={selectedProfile.currentGroupId} />
        </TabsItem>
      </Tabs>
    </WrapperModal>
  );
};

export default StudiesModal;
