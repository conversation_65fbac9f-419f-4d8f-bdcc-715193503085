import { useUpdateContact } from "@/hooks/use-update-contact";
import type { Contact, CreateContactPayload } from "@/lib/apis/contacts";

import { ContactModal } from "./contact-modal";

type EditContactProps = {
  showModalEditContact: boolean;
  setShowModalEditContact: (show: boolean) => void;
  contact: Contact;
  isForStudy?: boolean;
};

export const EditContactModal = ({
  showModalEditContact,
  setShowModalEditContact,
  contact,
}: EditContactProps) => {
  const { mutateAsync: updateContact, isPending: isUpdatingContact } =
    useUpdateContact();

  const onSubmit = async (data: CreateContactPayload) => {
    await updateContact({ id: contact.id, ...data });
    setShowModalEditContact(false);
  };

  return (
    <ContactModal
      show={showModalEditContact}
      onClose={() => setShowModalEditContact(false)}
      onSubmit={onSubmit}
      isLoading={isUpdatingContact}
      isEditing
      contact={contact}
    />
  );
};
