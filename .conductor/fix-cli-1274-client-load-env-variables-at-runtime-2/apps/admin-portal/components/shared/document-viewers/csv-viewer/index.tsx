import <PERSON> from "papaparse";
import { useEffect, useState } from "react";

import { DocumentError } from "../error-document";
import { LoadingDocument } from "../loading-document";

type CsvData = {
  data: string[][];
  errors: Papa.ParseError[];
};

/**
 * Custom component for viewing CSV files in a nicely formatted table
 * Uses PapaParse to parse the CSV data
 */
export const CsvViewer = ({ url }: { url: string }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [csvData, setCsvData] = useState<CsvData | null>(null);

  useEffect(() => {
    const fetchCsvData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`Failed to fetch CSV file: ${response.statusText}`);
        }

        const csvText = await response.text();
        const parsedData = Papa.parse<string[]>(csvText, {
          delimiter: ",", // auto-detect
          skipEmptyLines: true,
        });

        if (parsedData.errors.length > 0) {
          console.warn("CSV parsing warnings:", parsedData.errors);
        }

        setCsvData({
          data: parsedData.data,
          errors: parsedData.errors,
        });
      } catch (err) {
        console.error("Error loading CSV file:", err);
        setError((err as Error).message || "Failed to load CSV data");
      } finally {
        setLoading(false);
      }
    };

    fetchCsvData();
  }, [url]);

  if (loading) {
    return <LoadingDocument />;
  }

  if (error) {
    return <DocumentError message={error} />;
  }

  return (
    <div className="h-full w-full overflow-auto bg-white dark:bg-gray-800">
      {csvData && csvData.data.length > 0 ? (
        <table className="w-full border-collapse text-sm">
          <thead>
            <tr className="bg-gray-50 dark:bg-gray-700">
              {csvData.data[0].map((header, index) => (
                <th
                  key={index}
                  className="whitespace-nowrap border border-gray-200 px-4 py-2 text-left font-bold text-gray-800 dark:border-gray-600 dark:text-gray-200"
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {csvData.data.slice(1).map((row, rowIndex) => (
              <tr
                key={rowIndex}
                className="odd:bg-white even:bg-gray-50 dark:odd:bg-gray-800 dark:even:bg-gray-700"
              >
                {row.map((cell, cellIndex) => (
                  <td
                    key={cellIndex}
                    className="whitespace-nowrap border border-gray-200 px-4 py-2 text-gray-900 dark:border-gray-600 dark:text-gray-200"
                  >
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      ) : (
        <div className="flex h-full flex-col items-center justify-center">
          <p className="text-gray-500 dark:text-gray-400">
            No data found in CSV file
          </p>
        </div>
      )}
    </div>
  );
};
