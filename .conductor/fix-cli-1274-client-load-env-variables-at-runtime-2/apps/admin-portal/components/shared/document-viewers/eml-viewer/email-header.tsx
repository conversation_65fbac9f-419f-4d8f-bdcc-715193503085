import { EmailContent } from ".";
import { formatEmailAddresses } from "./utils";

interface EmailHeaderProps {
  emailContent: EmailContent;
}

export const EmailHeader = ({ emailContent }: EmailHeaderProps) => {
  return (
    <div className="email-header mb-4 border-b border-gray-200 pb-4 dark:border-gray-700">
      {emailContent.subject && (
        <h2 className="mb-3 text-xl font-bold text-gray-900 dark:text-white">
          {emailContent.subject}
        </h2>
      )}
      <div className="email-metadata space-y-2 text-sm">
        {emailContent.from && emailContent.from.length > 0 && (
          <div className="flex">
            <span className="mr-2 font-semibold text-gray-700 dark:text-gray-300">
              From:
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {formatEmailAddresses(emailContent.from)}
            </span>
          </div>
        )}
        {emailContent.to && emailContent.to.length > 0 && (
          <div className="flex">
            <span className="mr-2 font-semibold text-gray-700 dark:text-gray-300">
              To:
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {formatEmailAddresses(emailContent.to)}
            </span>
          </div>
        )}
        {emailContent.cc && emailContent.cc.length > 0 && (
          <div className="flex">
            <span className="mr-2 font-semibold text-gray-700 dark:text-gray-300">
              Cc:
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {formatEmailAddresses(emailContent.cc)}
            </span>
          </div>
        )}
        {emailContent.date && (
          <div className="flex">
            <span className="mr-2 font-semibold text-gray-700 dark:text-gray-300">
              Date:
            </span>
            <span className="text-gray-600 dark:text-gray-400">
              {emailContent.date}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};
