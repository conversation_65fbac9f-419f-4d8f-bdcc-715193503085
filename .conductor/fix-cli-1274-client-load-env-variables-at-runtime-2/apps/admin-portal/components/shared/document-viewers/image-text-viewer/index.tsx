import "./styles.css";
import "@cyntler/react-doc-viewer/dist/index.css";

import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
type Props = {
  url: string;
};

export const ImageTextViewer = ({ url }: Props) => {
  return (
    <DocViewer
      documents={[{ uri: url }]}
      pluginRenderers={DocViewerRenderers}
      config={{
        header: {
          disableHeader: true,
        },
      }}
      className="h-full w-full [&_txt-renderer]:p-0"
    />
  );
};
