import { Label, TextInput } from "flowbite-react";
import { HiSearch } from "react-icons/hi";

import { useSearch } from "@/hooks/use-search";
import { cn } from "@/lib/utils";

type SearchFieldProps = {
  label?: string;
  queryKey?: string;
  placeholder?: string;
  className?: string;
};

export const SearchField = ({
  label,
  placeholder,
  queryKey,
  className,
}: SearchFieldProps) => {
  const { search, changeSearch } = useSearch(queryKey);
  return (
    <form
      className={cn("flex-1", className)}
      onSubmit={(e) => {
        e.preventDefault();
      }}
    >
      <Label htmlFor="search" className="sr-only">
        {label}
      </Label>
      <TextInput
        className="leading-5.25 h-9 w-full text-sm [&>div]:h-full [&_input]:h-9"
        icon={HiSearch}
        defaultValue={search}
        id="search"
        name="search"
        sizing="sm"
        placeholder={placeholder}
        onChange={(e) => changeSearch(e.target.value)}
      />
    </form>
  );
};
