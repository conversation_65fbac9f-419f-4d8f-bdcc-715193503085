import type { VariantProps } from "class-variance-authority";
import { cva } from "class-variance-authority";
import { Badge } from "flowbite-react";

import { cn } from "@/lib/utils";

const formStatusBadgeVariants = cva(
  "inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full whitespace-nowrap before:content-[''] before:block before:w-1.5 before:h-1.5 before:rounded-full",
  {
    variants: {
      variant: {
        Draft:
          "bg-teal-50 text-teal-500 dark:bg-teal-900/30 dark:text-teal-400 before:bg-teal-500",
        Published:
          "bg-blue-50 text-blue-500 dark:bg-blue-900/30 dark:text-blue-400 before:bg-blue-500",
      },
    },
    defaultVariants: {
      variant: "Draft",
    },
  },
);

type FormStatusBadgeProps = VariantProps<typeof formStatusBadgeVariants> & {
  className?: string;
  children: React.ReactNode;
};

export const FormStatusBadge = ({
  className,
  variant,
  children,
}: FormStatusBadgeProps) => {
  return (
    <Badge className={cn(formStatusBadgeVariants({ variant }), className)}>
      {children}
    </Badge>
  );
};
