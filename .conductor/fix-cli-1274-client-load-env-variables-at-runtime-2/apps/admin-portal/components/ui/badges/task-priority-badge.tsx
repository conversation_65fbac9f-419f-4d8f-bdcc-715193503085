import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export const TASK_PRIORITIES = ["low", "medium", "high"] as const;

export type TaskPriority = (typeof TASK_PRIORITIES)[number];

const taskPriorityVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 before:bg-gray-900 dark:bg-gray-700 dark:text-white dark:before:bg-white",
        low: "bg-green-50 text-green-500 before:bg-green-500 dark:bg-green-700 dark:text-green-200 dark:before:bg-green-200",
        medium:
          "bg-orange-50 text-orange-400 before:bg-orange-400 dark:bg-orange-700 dark:text-orange-200 dark:before:bg-orange-200",
        high: "bg-red-50 text-red-400 before:bg-red-400 dark:bg-red-700 dark:text-red-200 dark:before:bg-red-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type PriorityProps = {
  className?: string;
  variant?: TaskPriority;
};

export const TaskPriorityBadge = ({ variant, className }: PriorityProps) => {
  return (
    <PillBadge
      className={cn(
        taskPriorityVariants({ variant }),
        "gap-0 rounded-2xl capitalize",
        className,
      )}
    >
      {variant}
    </PillBadge>
  );
};
