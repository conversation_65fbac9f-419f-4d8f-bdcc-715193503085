import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export const TASK_STATUSES = [
  { value: "notStarted", label: "Not Started" },
  { value: "inProgress", label: "In Progress" },
  { value: "completed", label: "Completed" },
  { value: "cancelled", label: "Cancelled" },
] as const;

export type TaskStatus = (typeof TASK_STATUSES)[number]["value"];

const taskStatusVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 before:bg-gray-900 dark:bg-gray-700 dark:text-white dark:before:bg-white",
        notStarted:
          "bg-teal-50 text-teal-500 before:bg-teal-500 dark:bg-teal-700 dark:text-teal-200 dark:before:bg-teal-200",
        inProgress:
          "bg-green-50 text-green-500 before:bg-green-500 dark:bg-green-700 dark:text-green-200 dark:before:bg-green-200",
        completed:
          "bg-purple-50 text-purple-500 before:bg-purple-500 dark:bg-purple-700 dark:text-purple-200 dark:before:bg-purple-200",
        cancelled:
          "bg-red-50 text-red-400 before:bg-red-400 dark:bg-red-700 dark:text-red-200 dark:before:bg-red-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type StatusProps = {
  className?: string;
  variant: TaskStatus;
};

export const TaskStatusBadge = ({ variant, className }: StatusProps) => {
  const status = TASK_STATUSES.find((status) => status.value === variant);
  return (
    <PillBadge
      className={cn(
        taskStatusVariants({ variant }),
        "gap-0 rounded-2xl",
        className,
      )}
    >
      {status?.label}
    </PillBadge>
  );
};
