import { cva } from "class-variance-authority";

import { cn } from "@/lib/utils";

import { PillBadge } from "./pill-badge";

export const VISIT_CATEGORIES = [
  { value: "screening", label: "Screening" },
  { value: "dosing", label: "Dosing" },
  { value: "follow_up", label: "Follow-up" },
  { value: "baseline", label: "Baseline" },
  { value: "end_of_treatment", label: "End of Treatment" },
  { value: "end_of_study", label: "End of Study" },
  { value: "optional", label: "Optional" },
  { value: "long_term_follow_up", label: "Long Term Follow-up" },
] as const;

export type VisitCategory = (typeof VISIT_CATEGORIES)[number]["value"];

const visitCategoryVariants = cva(
  "px-2.5 py-0.5 rounded-md text-xs font-medium leading-4.5 whitespace-nowrap before:w-1.5 before:h-1.5 before:rounded-full before:mr-1.5 before:inline-block",
  {
    variants: {
      variant: {
        default:
          "bg-gray-100 text-gray-900 before:bg-gray-900 dark:bg-gray-700 dark:text-white dark:before:bg-white",
        screening:
          "bg-blue-100 text-blue-500 before:bg-blue-500 dark:bg-blue-700 dark:text-blue-200 dark:before:bg-blue-200",
        dosing:
          "bg-purple-100 text-purple-500 before:bg-purple-500 dark:bg-purple-700 dark:text-purple-200 dark:before:bg-purple-200",
        follow_up:
          "bg-green-100 text-green-500 before:bg-green-500 dark:bg-green-700 dark:text-green-200 dark:before:bg-green-200",
        baseline:
          "bg-orange-100 text-orange-500 before:bg-orange-500 dark:bg-orange-700 dark:text-orange-200 dark:before:bg-orange-200",
        end_of_treatment:
          "bg-red-100 text-red-500 before:bg-red-500 dark:bg-red-700 dark:text-red-200 dark:before:bg-red-200",
        end_of_study:
          "bg-gray-100 text-gray-500 before:bg-gray-500 dark:bg-gray-700 dark:text-gray-200 dark:before:bg-gray-200",
        optional:
          "bg-yellow-100 text-yellow-500 before:bg-yellow-500 dark:bg-yellow-700 dark:text-yellow-200 dark:before:bg-yellow-200",
        long_term_follow_up:
          "bg-teal-100 text-teal-500 before:bg-teal-500 dark:bg-teal-700 dark:text-teal-200 dark:before:bg-teal-200",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

type VisitCategoryProps = {
  className?: string;
  variant: VisitCategory;
};

export const VisitCategoryBadge = ({
  variant,
  className,
}: VisitCategoryProps) => {
  const category = VISIT_CATEGORIES.find(
    (category) => category.value === variant,
  );
  return (
    <PillBadge
      className={cn(
        visitCategoryVariants({ variant }),
        "gap-0 rounded-2xl",
        className,
      )}
    >
      {category?.label}
    </PillBadge>
  );
};
