import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  Sector,
} from "recharts";
import { <PERSON>LabelProps, PieSectorDataItem } from "recharts/types/polar/Pie";
import { useMediaQuery } from "usehooks-ts";

const baseColors = [
  { light: "#1e40af", dark: "#60a5fa" }, // blue-800 / blue-400
  { light: "#059669", dark: "#34d399" }, // emerald-600 / emerald-400
  { light: "#d97706", dark: "#fbbf24" }, // amber-600 / amber-400
  { light: "#7c3aed", dark: "#a78bfa" }, // violet-600 / violet-400
  { light: "#db2777", dark: "#f472b6" }, // pink-600 / pink-400
  { light: "#0891b2", dark: "#22d3ee" }, // cyan-600 / cyan-400
  { light: "#65a30d", dark: "#a3e635" }, // lime-600 / lime-400
  { light: "#ea580c", dark: "#fb923c" }, // orange-600 / orange-400
  { light: "#9333ea", dark: "#c084fc" }, // purple-600 / purple-400
  { light: "#be185d", dark: "#ec4899" }, // pink-700 / pink-500
  { light: "#0e7490", dark: "#06b6d4" }, // cyan-700 / cyan-500
  { light: "#dc2626", dark: "#f87171" }, // red-600 / red-400
];

const generateContrastingColor = (index: number) => {
  if (index < baseColors.length) {
    return baseColors[index];
  }

  const hue = ((index - baseColors.length) * 137.5) % 360;
  return {
    light: `hsl(${hue}, 70%, 35%)`,
    dark: `hsl(${hue}, 70%, 65%)`,
  };
};

export const getDonutChartColor = (index: number) => {
  const colorPair = generateContrastingColor(index);
  return {
    light: colorPair.light,
    dark: colorPair.dark,
    default: colorPair.light,
  };
};

const renderActiveShape = (key: string | number) => {
  const shape = (props: PieSectorDataItem) => {
    const RADIAN = Math.PI / 180;
    const {
      cx = 0,
      cy = 0,
      midAngle = 0,
      innerRadius,
      outerRadius = 0,
      startAngle,
      endAngle,
      fill,
      payload,
      value,
    } = props;
    const sin = Math.sin(-RADIAN * midAngle);
    const cos = Math.cos(-RADIAN * midAngle);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;
    const textAnchor = cos >= 0 ? "start" : "end";

    return (
      <g>
        <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill}>
          {payload[key]}
        </text>
        <Sector
          cx={cx}
          cy={cy}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          startAngle={startAngle}
          endAngle={endAngle}
          fill={fill}
        />
        <Sector
          cx={cx}
          cy={cy}
          startAngle={startAngle}
          endAngle={endAngle}
          innerRadius={outerRadius + 6}
          outerRadius={outerRadius + 10}
          fill={fill}
        />
        <path
          d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
          stroke={fill}
          fill="none"
        />
        <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
        <text
          x={ex + (cos >= 0 ? 1 : -1) * 12}
          y={ey}
          textAnchor={textAnchor}
          className="fill-black dark:fill-white"
        >{`${value}`}</text>
      </g>
    );
  };
  return shape;
};

const renderLabel = (props: PieLabelProps) => {
  const RADIAN = Math.PI / 180;
  const {
    cx,
    cy,
    midAngle = 0,
    innerRadius,
    outerRadius,
    startAngle,
    endAngle,
    fill,
    value,
  } = props;
  const sin = Math.sin(-RADIAN * midAngle);
  const cos = Math.cos(-RADIAN * midAngle);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 20) * cos;
  const my = cy + (outerRadius + 20) * sin;
  const ex = mx + (cos >= 0 ? 1 : -1) * 8;
  const ey = my;
  const textAnchor = cos >= 0 ? "start" : "end";

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
      />
      <path
        d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`}
        stroke={fill}
        fill="none"
      />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text
        x={ex + (cos >= 0 ? 1 : -1) * 10}
        y={ey}
        textAnchor={textAnchor}
        className="fill-black dark:fill-white"
      >{`${value}`}</text>
    </g>
  );
};

type Props<T, K extends keyof T> = {
  data: T[];
  dataKey: K;
  nameKey: K;
  totalLabel?: string;
};

export const DonutChart = <T, K extends Exclude<keyof T, symbol>>({
  data,
  dataKey,
  nameKey,
  totalLabel,
}: Props<T, K>) => {
  const isMobile = useMediaQuery("(width <= 640px)");

  return (
    <ResponsiveContainer width="100%" height="100%">
      <PieChart accessibilityLayer>
        <Pie
          data={data}
          outerRadius={isMobile ? 100 : 130}
          innerRadius={isMobile ? 70 : 100}
          dataKey={dataKey}
          nameKey={nameKey}
          activeShape={!isMobile ? renderActiveShape(nameKey) : undefined}
          label={isMobile ? renderLabel : undefined}
        >
          {data?.map((_, index) => (
            <Cell
              key={`cell-${index}`}
              fill={getDonutChartColor(index).default}
            />
          ))}

          {totalLabel && (
            <Legend
              verticalAlign="bottom"
              content={(props) => {
                return typeof data?.length === "number" ? (
                  <p className="text-center dark:text-white">
                    Total {totalLabel}:{" "}
                    <span className="font-semibold">{data?.length}</span>
                  </p>
                ) : null;
              }}
            />
          )}
        </Pie>
      </PieChart>
    </ResponsiveContainer>
  );
};
