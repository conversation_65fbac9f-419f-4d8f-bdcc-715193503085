"use client";

import "react-day-picker/style.css";
import "./date-picker.css";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  Placement,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import React, {
  createContext,
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useId,
  useMemo,
  useState,
} from "react";
import { DayPicker } from "react-day-picker";

import { cn } from "@/lib/utils";

import { Button } from "../../button";

// Event name for datepicker toggle events
const DATEPICKER_TOGGLE_EVENT = "datepicker-toggle";

/**
 * Parses an ISO date string with proper timezone handling
 */
const parseISODateString = (
  isoString: string,
  enableTimezone: boolean = true,
): Date => {
  if (isoString === "invalid-date") {
    return new Date();
  }

  try {
    if (enableTimezone) {
      return new Date(isoString);
    } else {
      const datePart = isoString.split("T")[0];
      const [year, month, day] = datePart.split("-").map(Number);
      return new Date(Date.UTC(year, month - 1, day, 12, 0, 0, 0));
    }
  } catch (error) {
    console.error("Error parsing ISO date:", error);
    return new Date(isoString);
  }
};

export type DatePickerUIProps = Omit<
  React.HTMLAttributes<HTMLDivElement>,
  "defaultValue"
> & {
  value?: Date | string | null;
  defaultValue?: Date | string | null;
  onChange?: (date: Date | null) => void;
  onSave?: (date: Date | null) => void;
  placeholder?: string;
  disabled?: boolean;
  placement?: Placement;
  minDate?: Date;
  maxDate?: Date;
  highlightDates?: Date[];
  enableTimezone?: boolean;
  disableClearBtn?: boolean;
  disableTodayBtn?: boolean;
  customActionBtns?: React.ReactNode;
};

type DatePickerContextType = {
  // State
  isOpen: boolean;
  month: Date;
  selectedDate: Date | null;

  // Props
  disabled?: boolean;
  minDate?: Date;
  maxDate?: Date;
  highlightDates: Date[];
  enableTimezone: boolean;
  disableClearBtn: boolean;
  disableTodayBtn: boolean;
  customActionBtns?: React.ReactNode;
  placeholder?: string;

  // Actions
  setIsOpen: (isOpen: boolean) => void;
  setMonth: (month: Date) => void;
  handleDateSelect: (date: Date | null) => void;
  onSave?: (date: Date | null) => void;

  // Floating UI
  refs: any;
  floatingStyles: any;
  context: any;
  getReferenceProps: () => any;
  getFloatingProps: () => any;
};

const DatePickerContext = createContext<DatePickerContextType | null>(null);

export const useDatePicker = () => {
  const context = useContext(DatePickerContext);
  if (!context) {
    throw new Error("useDatePicker must be used within a DatePickerUI");
  }
  return context;
};

export const DatePickerUI = forwardRef<HTMLDivElement, DatePickerUIProps>(
  (
    {
      value,
      defaultValue,
      onChange,
      onSave,
      disabled,
      className,
      minDate,
      maxDate,
      customActionBtns,
      highlightDates = [],
      placement = "bottom-start",
      enableTimezone = true,
      disableClearBtn = false,
      disableTodayBtn = false,
      placeholder = "Select date",
      children,
      ...props
    },
    ref,
  ) => {
    const [isOpen, setIsOpen] = useState(false);

    // Internal state for uncontrolled usage
    const [internalValue, setInternalValue] = useState<Date | null>(() => {
      if (defaultValue) {
        if (defaultValue instanceof Date) return defaultValue;
        if (typeof defaultValue === "string") {
          try {
            return parseISODateString(defaultValue, enableTimezone);
          } catch {
            return null;
          }
        }
      }
      return null;
    });

    // Determine if this is controlled or uncontrolled
    const isControlled = value !== undefined;

    // Convert value to Date object
    const selectedDate = useMemo(() => {
      const currentValue = isControlled ? value : internalValue;
      if (!currentValue) return null;
      if (currentValue instanceof Date) return currentValue;
      if (typeof currentValue === "string") {
        try {
          return parseISODateString(currentValue, enableTimezone);
        } catch {
          return null;
        }
      }
      return null;
    }, [value, internalValue, isControlled, enableTimezone]);

    // Initialize month based on selected date, highlighted dates, or current date
    const initialMonth = useMemo(() => {
      if (selectedDate) return selectedDate;

      if (highlightDates && highlightDates.length > 0) {
        const validHighlightDate = highlightDates.find(
          (date) => date instanceof Date && !isNaN(date.getTime()),
        );
        if (validHighlightDate) {
          return validHighlightDate;
        }
      }
      return new Date();
    }, [selectedDate, highlightDates]);

    const [month, setMonth] = useState<Date>(initialMonth);

    // Update month when selected date changes
    useEffect(() => {
      if (selectedDate && !isNaN(selectedDate.getTime())) {
        setMonth(selectedDate);
      }
    }, [selectedDate]);

    // Update month when highlightDates change
    useEffect(() => {
      if (!selectedDate && highlightDates && highlightDates.length > 0) {
        const validHighlightDate = highlightDates.find(
          (date) => date instanceof Date && !isNaN(date.getTime()),
        );
        if (validHighlightDate) {
          setMonth(validHighlightDate);
        }
      }
    }, [highlightDates, selectedDate]);

    // Generate unique ID for this datepicker instance
    const datepickerId = useId();

    // Helper function to reset to default/original value
    const resetToDefault = useCallback(() => {
      if (isControlled) {
        // For controlled mode, reset to the original value or null
        const resetValue = value !== undefined ? value : null;
        onChange?.(
          resetValue instanceof Date
            ? resetValue
            : resetValue && typeof resetValue === "string"
              ? parseISODateString(resetValue, enableTimezone)
              : null,
        );
      } else {
        // For uncontrolled mode, reset to default value
        const resetValue = defaultValue
          ? defaultValue instanceof Date
            ? defaultValue
            : typeof defaultValue === "string"
              ? parseISODateString(defaultValue, enableTimezone)
              : null
          : null;
        setInternalValue(resetValue);
        onChange?.(resetValue);
      }
    }, [isControlled, value, defaultValue, enableTimezone, onChange]);

    // Listen for other datepickers opening
    useEffect(() => {
      const handleDatepickerToggle = (e: CustomEvent) => {
        const { id, isOpen: toggledOpen } = e.detail;
        if (id !== datepickerId && toggledOpen && isOpen) {
          setIsOpen(false);
        }
      };

      document.addEventListener(
        DATEPICKER_TOGGLE_EVENT,
        handleDatepickerToggle as EventListener,
      );

      return () => {
        document.removeEventListener(
          DATEPICKER_TOGGLE_EVENT,
          handleDatepickerToggle as EventListener,
        );
      };
    }, [datepickerId, isOpen]);

    // Broadcast when this datepicker opens/closes
    useEffect(() => {
      const event = new CustomEvent(DATEPICKER_TOGGLE_EVENT, {
        detail: { id: datepickerId, isOpen },
      });
      document.dispatchEvent(event);

      if (isOpen && highlightDates && highlightDates.length > 0) {
        const validHighlightDate = highlightDates.find(
          (date) => date instanceof Date && !isNaN(date.getTime()),
        );
        if (validHighlightDate) {
          setMonth(validHighlightDate);
        }
      }
    }, [isOpen, datepickerId, highlightDates]);

    // Add escape key handler
    useEffect(() => {
      if (!isOpen) return;

      const handleEscapeKey = (e: KeyboardEvent) => {
        if (e.key === "Escape") {
          setIsOpen(false);
          resetToDefault();
        }
      };

      document.addEventListener("keydown", handleEscapeKey);
      return () => {
        document.removeEventListener("keydown", handleEscapeKey);
      };
    }, [isOpen, resetToDefault]);

    // Setup floating UI for the calendar dropdown
    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: (open) => {
        setIsOpen(open);
        // Reset to default value when closing
        if (!open) {
          resetToDefault();
        }
      },
      placement,
      middleware: [
        offset(8),
        flip({
          fallbackPlacements: ["top-start"],
          fallbackStrategy: "bestFit",
          padding: 1,
          crossAxis: false,
        }),
        shift({
          padding: 1,
        }),
      ],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      useClick(context),
      useDismiss(context, {
        outsidePress: true,
        bubbles: false,
        referencePress: false,
      }),
    ]);

    const handleDateSelect = (date: Date | null) => {
      if (isControlled) {
        onChange?.(date);
      } else {
        setInternalValue(date);
        onChange?.(date);
      }
    };

    const contextValue: DatePickerContextType = {
      // State
      isOpen,
      month,
      selectedDate,

      // Props
      disabled,
      minDate,
      maxDate,
      highlightDates,
      enableTimezone,
      disableClearBtn,
      disableTodayBtn,
      customActionBtns,
      placeholder,

      // Actions
      setIsOpen,
      setMonth,
      handleDateSelect,
      onSave,

      // Floating UI
      refs,
      floatingStyles,
      context,
      getReferenceProps,
      getFloatingProps,
    };

    // Separate out our custom props from HTML props
    const { defaultValue: _, onChange: __, ...htmlProps } = props as any;

    return (
      <DatePickerContext.Provider value={contextValue}>
        <div className={cn("space-y-1", className)} ref={ref} {...htmlProps}>
          {children}
        </div>
      </DatePickerContext.Provider>
    );
  },
);

DatePickerUI.displayName = "DatePickerUI";

export const DatePickerTrigger = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ children, className, ...props }, ref) => {
  const { refs, getReferenceProps, setIsOpen, disabled } = useDatePicker();

  if (children) {
    // If children are provided, render them with trigger functionality
    return (
      <div
        ref={refs.setReference}
        {...getReferenceProps()}
        className={cn("w-full", className)}
        onClick={() => {
          if (!disabled) {
            setIsOpen(true);
          }
        }}
        {...props}
      >
        {children}
      </div>
    );
  }

  // Default trigger - simple button
  return (
    <DatePickerDefaultTrigger ref={ref} className={className} {...props} />
  );
});

DatePickerTrigger.displayName = "DatePickerTrigger";

// Default button trigger component
const DatePickerDefaultTrigger = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const {
    refs,
    getReferenceProps,
    setIsOpen,
    selectedDate,
    disabled,
    placeholder,
  } = useDatePicker();

  return (
    <div
      ref={refs.setReference}
      {...getReferenceProps()}
      className={cn("relative flex h-fit items-center gap-2", className)}
      {...props}
    >
      <button
        type="button"
        className={cn(
          "bg-background ring-offset-background flex h-10 w-full items-center justify-between rounded-md border border-gray-300 px-3 py-2 text-sm",
          "placeholder:text-muted-foreground",
          "focus:ring-ring focus:outline-none focus:ring-2 focus:ring-offset-2",
          disabled && "cursor-not-allowed opacity-50",
          !disabled && "hover:bg-gray-50",
        )}
        onClick={() => {
          if (!disabled) {
            setIsOpen(true);
          }
        }}
        disabled={disabled}
      >
        <span>
          {selectedDate ? selectedDate.toLocaleDateString() : placeholder}
        </span>
        <svg
          className="h-4 w-4 opacity-50"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </button>
    </div>
  );
});

DatePickerDefaultTrigger.displayName = "DatePickerDefaultTrigger";

export const DatePickerPanel = forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const {
    isOpen,
    setIsOpen,
    month,
    setMonth,
    selectedDate,
    disabled,
    minDate,
    maxDate,
    highlightDates,
    enableTimezone,
    disableTodayBtn,
    customActionBtns,
    handleDateSelect,
    onSave,
    refs,
    floatingStyles,
    context,
    getFloatingProps,
  } = useDatePicker();

  if (!isOpen) return null;

  return (
    <FloatingPortal>
      <FloatingFocusManager
        context={context}
        modal={false}
        order={["reference", "content"]}
        returnFocus={true}
      >
        <div
          ref={refs.setFloating}
          style={floatingStyles}
          {...getFloatingProps()}
          className={cn("z-50 rounded-lg border bg-white shadow-md", className)}
          {...props}
        >
          <DayPicker
            mode="single"
            defaultMonth={month}
            month={month}
            onMonthChange={setMonth}
            selected={selectedDate || undefined}
            onSelect={(date) => {
              if (!date) return;

              const isInRange =
                (!minDate || date >= minDate) && (!maxDate || date <= maxDate);

              if (isInRange) {
                if (enableTimezone) {
                  handleDateSelect(date);
                } else {
                  const utcDate = new Date(
                    Date.UTC(
                      date.getFullYear(),
                      date.getMonth(),
                      date.getDate(),
                      12,
                      0,
                      0,
                      0,
                    ),
                  );
                  handleDateSelect(utcDate);
                }
              }
            }}
            disabled={
              typeof disabled === "boolean"
                ? disabled
                : [
                    ...(maxDate
                      ? [
                          {
                            after: maxDate,
                          },
                        ]
                      : []),
                    ...(minDate
                      ? [
                          {
                            before: minDate,
                          },
                        ]
                      : []),
                  ]
            }
            modifiers={{
              highlighted: highlightDates
                .filter(
                  (date): date is Date =>
                    date instanceof Date && !isNaN(date.getTime()),
                )
                .map(
                  (date) =>
                    new Date(
                      date.getFullYear(),
                      date.getMonth(),
                      date.getDate(),
                    ),
                ),
              highlightedFirst: (() => {
                const validDates = highlightDates.filter(
                  (date): date is Date =>
                    date instanceof Date && !isNaN(date.getTime()),
                );
                return validDates.length > 0
                  ? [
                      new Date(
                        validDates[0].getFullYear(),
                        validDates[0].getMonth(),
                        validDates[0].getDate(),
                      ),
                    ]
                  : [];
              })(),
              highlightedLast: (() => {
                const validDates = highlightDates.filter(
                  (date): date is Date =>
                    date instanceof Date && !isNaN(date.getTime()),
                );
                return validDates.length > 0
                  ? [
                      new Date(
                        validDates[validDates.length - 1].getFullYear(),
                        validDates[validDates.length - 1].getMonth(),
                        validDates[validDates.length - 1].getDate(),
                      ),
                    ]
                  : [];
              })(),
            }}
            className="w-fit p-3"
            classNames={{
              selected: "!bg-primary-500 rounded-lg hover:!bg-primary-500",
            }}
            modifiersClassNames={{
              highlighted: "rdp-day_highlighted",
              highlightedFirst: "rdp-day_highlighted-first",
              highlightedLast: "rdp-day_highlighted-last",
            }}
          />

          <div className="flex justify-between gap-2.5 px-3 pb-3">
            {!disableTodayBtn && (
              <Button
                onClick={() => {
                  const today = new Date();
                  if (enableTimezone) {
                    handleDateSelect(today);
                  } else {
                    const utcToday = new Date(
                      Date.UTC(
                        today.getFullYear(),
                        today.getMonth(),
                        today.getDate(),
                        12,
                        0,
                        0,
                        0,
                      ),
                    );
                    handleDateSelect(utcToday);
                  }
                }}
                variant="primary"
                className="flex-1"
              >
                Today
              </Button>
            )}

            <Button
              type="button"
              className="flex-1 dark:text-black"
              variant="outline"
              onClick={() => {
                onSave?.(selectedDate);
                setIsOpen(false);
              }}
            >
              Save
            </Button>

            {customActionBtns}
          </div>
        </div>
      </FloatingFocusManager>
    </FloatingPortal>
  );
});

DatePickerPanel.displayName = "DatePickerPanel";
