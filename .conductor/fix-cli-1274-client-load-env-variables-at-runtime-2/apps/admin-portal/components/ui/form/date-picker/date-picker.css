.rdp-root {
  @apply font-poppins;
}

.rdp-day_selected:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #000 !important;
  color: white !important;
}
.rdp-day_range_middle:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #f3f4f6 !important;
  color: black !important;
}
.rdp-day_range_start:not(.rdp-day_disabled):not(.rdp-day_outside),
.rdp-day_range_end:not(.rdp-day_disabled):not(.rdp-day_outside) {
  background-color: #000 !important;
  color: white !important;
}
.rdp-button:hover:not([disabled]):not(.rdp-day_selected) {
  background-color: #e5e7eb !important;
}
.rdp-months {
  display: flex !important;
  justify-content: center;
  flex-wrap: nowrap !important;
}
.rdp-month_caption {
  display: flex !important;
  justify-content: center !important;
}
.rdp-nav {
  width: 100% !important;
  display: flex !important;
  justify-content: space-between !important;
}

.rdp-range_end .rdp-day_button {
  background-color: #003 !important;
  border-radius: 8px !important;
  border-style: none !important;
}
.rdp-range_start .rdp-day_button {
  background-color: #003 !important;
  border-radius: 8px !important;
  border-style: none !important;
}

.rdp-today:not(.rdp-outside) {
  color: #003 !important;
  background-color: #e5e7eb;
  border-radius: 8px;
}

.rdp-range_middle {
  background-color: #f3f4f6 !important;
}
.rdp-today.rdp-range_middle {
  background-color: #f3f4f6 !important;
  border-radius: 0px !important;
}

.rdp-day,
.rdp-day_button {
  font-size: 12px !important;
  height: 32px !important;
  width: 32px !important;
}

.rdp-day:hover:not(.rdp-range_middle):not([data-selected="true"]) {
  background-color: #f3f4f6 !important;
  border-radius: 8px !important;
}

.rdp-button_previous,
.rdp-button_next {
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  color: #003 !important;
}
.rdp-chevron {
  fill: #003 !important;
  width: 14px !important;
  height: 14px !important;
}

.rdp-caption_label {
  font-size: 14px !important;
  font-weight: 500 !important;
}

.rdp-selected {
  font-weight: 600 !important;
}

@media (width <= 640px) {
  .rdp-months {
    flex-direction: column;
    gap: 0;
  }
}
