"use client";

import "react-day-picker/style.css";
import "./date-picker.css";

import { Placement } from "@floating-ui/react";
import { format } from "date-fns";
import { CalendarRange } from "lucide-react";
import { forwardRef, useState } from "react";
import type { DateRange } from "react-day-picker";
import { DayPicker } from "react-day-picker";
import { Controller, useFormContext } from "react-hook-form";
import { MdOutlineClear } from "react-icons/md";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

import { Button } from "../../button";
import {
  Dropdown,
  DropdownContent,
  DropdownTrigger,
} from "../../dropdown/dropdown";

export type DateRangePickerProps = Merge<
  React.HTMLAttributes<HTMLDivElement>,
  {
    name: string;
    placeholder?: string;
    shouldShowError?: boolean;
    disabled?: boolean;
    className?: string;
    placement?: Placement;
    minDate?: Date;
    maxDate?: Date;
  }
>;

const DateRangePicker = forwardRef<HTMLDivElement, DateRangePickerProps>(
  (
    {
      name,
      placeholder = "Select date range",
      shouldShowError = true,
      disabled,
      className,
      placement = "bottom-start",
      maxDate,
      minDate,
      ...props
    },
    ref,
  ) => {
    const { control } = useFormContext();
    const [isOpen, setIsOpen] = useState(false);
    const [month, setMonth] = useState<Date>(new Date());

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";
          const range = field.value as DateRange | undefined;

          // Format the display text for the selected range
          const displayText =
            range?.from && range?.to
              ? `${format(range.from, "MMM dd, yyyy")} - ${format(range.to, "MMM dd, yyyy")}`
              : range?.from
                ? `${format(range.from, "MMM dd, yyyy")} - Select end date`
                : placeholder;
          const isPlaceholder = !range?.from && !range?.to;
          const handleRangeSelect = (range: DateRange | undefined) => {
            field.onChange(range);
            // Don't close the dropdown when a date is selected
          };

          return (
            <div ref={ref} className={cn("space-y-1", className)} {...props}>
              <Dropdown
                open={isOpen}
                onOpenChange={setIsOpen}
                placement={placement}
              >
                <DropdownTrigger className="w-full">
                  <Button
                    variant="outline"
                    disabled={disabled}
                    className={cn(
                      "h-11 w-full !p-2.5 px-[14px] text-left font-normal hover:!bg-inherit [&>span]:flex-1 [&>span]:!p-0 [&>span]:text-sm [&_div]:flex-1 [&_div]:justify-between",
                      "border border-gray-300 !bg-gray-50 text-gray-700 hover:!bg-gray-100 hover:!bg-inherit dark:border-gray-500 dark:!bg-gray-700 dark:text-white dark:hover:!bg-gray-600",
                      !range && "text-back",
                      hasError && "!border-red-500 !bg-red-50 !ring-red-500",
                      isPlaceholder && "text-[#9ca3af] dark:text-[#9ca3af]",
                    )}
                  >
                    {displayText}

                    {range?.from || range?.to ? (
                      <MdOutlineClear
                        onClick={(e) => {
                          e.stopPropagation();
                          field.onChange(null);
                        }}
                        className="size-4"
                      />
                    ) : (
                      <CalendarRange className="size-4" />
                    )}
                  </Button>
                </DropdownTrigger>
                <DropdownContent className="w-auto min-w-fit rounded-lg border border-gray-200 p-0">
                  <DayPicker
                    mode="range"
                    defaultMonth={month}
                    onMonthChange={setMonth}
                    selected={range}
                    onSelect={handleRangeSelect}
                    disabled={
                      typeof disabled === "boolean"
                        ? disabled
                        : [
                            ...(maxDate
                              ? [
                                  {
                                    after: maxDate,
                                  },
                                ]
                              : []),
                            ...(minDate
                              ? [
                                  {
                                    before: minDate,
                                  },
                                ]
                              : []),
                          ]
                    }
                    className="w-fit p-3"
                  />
                </DropdownContent>
              </Dropdown>
              {hasError && shouldShowError && (
                <span className="error-message text-sm text-red-500">
                  {errorMessage}
                </span>
              )}
            </div>
          );
        }}
      />
    );
  },
);

DateRangePicker.displayName = "DateRangePicker";

export { DateRangePicker };
