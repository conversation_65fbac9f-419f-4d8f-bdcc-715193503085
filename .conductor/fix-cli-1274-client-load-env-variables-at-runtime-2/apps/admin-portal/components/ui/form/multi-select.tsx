"use client";

import {
  autoUpdate,
  flip,
  FloatingFocus<PERSON>anager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { Check, ChevronDown } from "lucide-react";
import { forwardRef, useMemo, useState } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { HiSearch } from "react-icons/hi";
import { MdOutlineClear } from "react-icons/md";

import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

export type SelectProps = Omit<FlowbiteSelectProps, "name" | "onChange"> & {
  name: string;
  shouldShowError?: boolean;
  placeholder?: string;
  isDisabled?: boolean;
  options?: { label: string; value: string }[];
  onChange?: (value: string[]) => void;
  className?: string;
  searchable?: boolean;
  searchPlaceholder?: string;
};

const MultiSelect = forwardRef<HTMLDivElement, SelectProps>(
  (
    {
      name,
      shouldShowError = true,
      placeholder = "Select an option",
      isDisabled = false,
      options = [],
      onChange,
      className,
      searchable = false,
      searchPlaceholder = "Search...",
      ...props
    },
    ref,
  ) => {
    const { control } = useFormContext();
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 300);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: (open) => {
        setIsOpen(open);
        setSearchTerm("");
      },
      placement: "bottom-start",
      middleware: [
        offset(8),
        flip({
          fallbackPlacements: ["top-start"],
          fallbackStrategy: "bestFit",
          padding: 1,
          crossAxis: false,
        }),
        shift({
          padding: 1,
        }),
        size({
          apply({ rects, elements }) {
            Object.assign(elements.floating.style, {
              width: `${rects.reference.width}px`,
            });
          },
          padding: 1,
        }),
      ],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      useClick(context),
      useDismiss(context),
    ]);

    // Filter options based on search term
    const filteredOptions = useMemo(() => {
      if (!searchable || !debouncedSearchTerm) {
        return options;
      }
      return options.filter((option) =>
        option.label.toLowerCase().includes(debouncedSearchTerm.toLowerCase()),
      );
    }, [options, debouncedSearchTerm, searchable]);

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorMessage = errors[name]?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          const selectedOption = options.filter((option) =>
            field.value.includes(option.value),
          );

          return (
            <div
              className={cn(
                "relative",
                hasError && shouldShowError && "mb-5",
                className,
              )}
              ref={ref}
            >
              {/* Select Button */}
              <div ref={refs.setReference} {...getReferenceProps()}>
                <button
                  type="button"
                  className={cn(
                    "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-2",
                    "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
                    isDisabled
                      ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                      : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
                    hasError
                      ? "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500"
                      : "focus:ring-primary-500 dark:focus:ring-primary-500",
                    "transition-all duration-200 ease-in-out",
                  )}
                  //   disabled={isDisabled}
                >
                  <span
                    className={cn(
                      "flex-1 truncate",
                      !selectedOption.length &&
                        "text-[#9ca3af] dark:text-[#9ca3af]",
                    )}
                  >
                    {selectedOption.length
                      ? selectedOption.map((option) => option.label).join(", ")
                      : placeholder}
                  </span>
                  {selectedOption.length ? (
                    <MdOutlineClear
                      role="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        field.onChange([]);
                        onChange?.([]);
                      }}
                      className="size-4"
                    />
                  ) : (
                    <ChevronDown
                      className={cn(
                        "size-4 shrink-0 transition-transform duration-200",
                        "text-gray-400",
                        "group-focus:text-blue-500",
                        hasError && "!text-red-500",
                        isOpen ? "-rotate-180 transform" : "",
                      )}
                    />
                  )}
                </button>

                {hasError && shouldShowError && (
                  <span className="error-message absolute left-0 top-full">
                    {errorMessage}
                  </span>
                )}
              </div>

              {/* Dropdown Panel */}
              {isOpen && (
                <FloatingPortal>
                  <FloatingFocusManager context={context} modal={false}>
                    <div
                      ref={refs.setFloating}
                      style={floatingStyles}
                      {...getFloatingProps()}
                      className="z-50 rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-700"
                    >
                      {/* Search Input */}
                      {searchable && (
                        <div className="border-b border-gray-200 p-2 dark:border-gray-600">
                          <div className="relative">
                            <HiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              placeholder={searchPlaceholder}
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border border-gray-300 bg-white py-2 pl-9 pr-3 text-sm placeholder-gray-500 focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400"
                              autoFocus
                            />
                          </div>
                        </div>
                      )}

                      <div className="max-h-60 overflow-y-auto py-1">
                        {filteredOptions.length === 0 ? (
                          <div className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                            {searchable && searchTerm
                              ? "No results found"
                              : "No options available"}
                          </div>
                        ) : (
                          filteredOptions.map((option) => {
                            const isSelected = field.value.includes(
                              option.value,
                            );
                            return (
                              <button
                                key={option.value}
                                type="button"
                                className={cn(
                                  "flex w-full items-center justify-between px-3 py-2 text-left text-sm text-gray-900 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600",
                                  isSelected && "bg-gray-50 dark:bg-gray-600",
                                )}
                                onClick={() => {
                                  field.onChange(
                                    isSelected
                                      ? field.value.filter(
                                          (opt: string) => opt !== option.value,
                                        )
                                      : [...field.value, option.value],
                                  );
                                  onChange?.(
                                    isSelected
                                      ? field.value.filter(
                                          (opt: string) => opt !== option.value,
                                        )
                                      : [...field.value, option.value],
                                  );
                                  // setIsOpen(false);
                                }}
                              >
                                <span className="block truncate">
                                  {option.label}
                                </span>
                                {isSelected && (
                                  <Check size={20} color="#31C48D" />
                                )}
                              </button>
                            );
                          })
                        )}
                      </div>
                    </div>
                  </FloatingFocusManager>
                </FloatingPortal>
              )}
            </div>
          );
        }}
      />
    );
  },
);

MultiSelect.displayName = "MultiSelect";

export { MultiSelect };
