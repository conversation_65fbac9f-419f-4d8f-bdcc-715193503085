import { useQuery } from "@tanstack/react-query";
import type { ComponentProps } from "react";

import api from "@/lib/apis";

import { Select } from "./select";

export const SelectGroups = (props: ComponentProps<typeof Select>) => {
  const { data } = useQuery({
    queryKey: ["groups"],
    queryFn: () => api.groups.list({}),
  });
  return (
    <Select
      {...props}
      placeholder="Select group"
      options={
        data?.results?.map((group) => ({
          label: group.name,
          value: group.id,
        })) || []
      }
    />
  );
};
