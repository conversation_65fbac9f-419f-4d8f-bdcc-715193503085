"use client";

import {
  autoUpdate,
  flip,
  FloatingFocusManager,
  FloatingPortal,
  offset,
  shift,
  size,
  useClick,
  useDismiss,
  useFloating,
  useInteractions,
} from "@floating-ui/react";
import type { SelectProps as FlowbiteSelectProps } from "flowbite-react";
import { forwardRef, useEffect, useMemo, useState } from "react";
import { Controller, get, useFormContext } from "react-hook-form";
import { HiSearch } from "react-icons/hi";
import { MdOutlineClear } from "react-icons/md";

import { useDebounce } from "@/hooks/use-debounce";
import { cn } from "@/lib/utils";

export type SelectProps = Omit<FlowbiteSelectProps, "name" | "onChange"> & {
  name: string;
  shouldShowError?: boolean;
  placeholder?: string;
  options?: Readonly<
    { label: React.ReactNode; value: string; disabled?: boolean }[]
  >;
  onChange?: (value: string) => void;
  className?: string;
  dependentFieldNames?: string[];
  searchable?: boolean;
  searchPlaceholder?: string;
  readOnly?: boolean;
};

const Select = forwardRef<HTMLDivElement, SelectProps>(
  (
    {
      name,
      shouldShowError = true,
      placeholder = "Select an option",
      options = [],
      onChange,
      className,
      disabled,
      dependentFieldNames,
      searchable = false,
      searchPlaceholder = "Search...",
      readOnly = false,
      ...props
    },
    ref,
  ) => {
    const { control, setValue, watch } = useFormContext();
    const [isOpen, setIsOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const debouncedSearchTerm = useDebounce(searchTerm, 300);

    const { refs, floatingStyles, context } = useFloating({
      open: isOpen,
      onOpenChange: (open) => {
        setIsOpen(open);
        setSearchTerm("");
      },
      placement: "bottom-start",
      middleware: [
        offset(8),
        flip({
          fallbackPlacements: ["top-start"],
          fallbackStrategy: "bestFit",
          padding: 1,
          crossAxis: false,
        }),
        shift({
          padding: 1,
        }),
        size({
          apply({ rects, elements }) {
            Object.assign(elements.floating.style, {
              width: `${rects.reference.width}px`,
            });
          },
          padding: 1,
        }),
      ],
      whileElementsMounted: autoUpdate,
    });

    const { getReferenceProps, getFloatingProps } = useInteractions([
      useClick(context, { enabled: !readOnly }),
      useDismiss(context),
    ]);

    // Filter options based on search term
    const filteredOptions = useMemo(() => {
      if (!searchable || !debouncedSearchTerm) {
        return options;
      }
      return options.filter((option) =>
        String(option.label)
          .toLowerCase()
          .includes(debouncedSearchTerm.toLowerCase()),
      );
    }, [options, debouncedSearchTerm, searchable]);

    useEffect(() => {
      const { unsubscribe } = watch((_, { name: fieldName }) => {
        if (fieldName && dependentFieldNames?.includes(fieldName)) {
          setValue(name, "", {
            shouldValidate: true,
          });
        }
      });
      return () => unsubscribe();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [watch]);

    return (
      <Controller
        name={name}
        control={control}
        render={({ field, formState: { errors } }) => {
          const errorObj = get(errors, name);
          const errorMessage = errorObj?.message?.valueOf();
          const hasError = typeof errorMessage === "string";

          const selectedOption = options.find(
            (option) => option.value === field.value,
          );

          return (
            <div
              className={cn(
                "relative",
                // hasError && shouldShowError && "mb-5",
                className,
              )}
              ref={ref}
            >
              {/* Select Button */}
              <div
                ref={refs.setReference}
                {...getReferenceProps()}
                className="relative w-full"
              >
                <button
                  type="button"
                  className={cn(
                    "flex w-full items-center justify-between rounded-lg border bg-gray-50 px-2.5 py-2.5 text-left text-sm focus:outline-none focus:ring-2",
                    "dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",
                    disabled
                      ? "cursor-not-allowed border-gray-200 bg-gray-50 text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400"
                      : "border-gray-300 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600",
                    hasError
                      ? "border-red-500 focus:ring-red-500 dark:border-red-500 dark:focus:ring-red-500"
                      : "focus:ring-primary-500 dark:focus:ring-primary-500",
                    "transition-all duration-200 ease-in-out",
                  )}
                  disabled={disabled}
                >
                  <span
                    className={cn(
                      "truncate",
                      !selectedOption && "text-[#6B7280] dark:text-[#9ca3af]",
                    )}
                  >
                    {selectedOption ? selectedOption.label : placeholder}
                  </span>
                  {selectedOption && !readOnly ? (
                    <span
                      onClick={(e) => {
                        e.stopPropagation();
                        field.onChange("");
                        field.onBlur();
                        onChange?.("");
                      }}
                    >
                      <MdOutlineClear className="size-4" />
                    </span>
                  ) : (
                    <svg
                      className={cn(
                        "h-5 w-5 shrink-0 text-gray-400 transition-transform duration-200 dark:text-gray-400",
                        isOpen ? "rotate-180 transform" : "",
                      )}
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </button>
              </div>
              {hasError && shouldShowError && (
                <span className="text-sm text-red-500 dark:text-red-400">
                  {errorMessage}
                </span>
              )}

              {/* Dropdown Panel */}
              {isOpen && (
                <FloatingPortal>
                  <FloatingFocusManager context={context} modal={false}>
                    <div
                      ref={refs.setFloating}
                      style={floatingStyles}
                      {...getFloatingProps()}
                      className="z-50 rounded-lg border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-700"
                    >
                      {/* Search Input */}
                      {searchable && (
                        <div className="border-b border-gray-200 p-2 dark:border-gray-600">
                          <div className="relative">
                            <HiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              placeholder={searchPlaceholder}
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="focus:border-primary-500 focus:ring-primary-500 w-full rounded-md border border-gray-300 bg-white py-2 pl-9 pr-3 text-sm placeholder-gray-500 focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:placeholder-gray-400"
                              autoFocus
                            />
                          </div>
                        </div>
                      )}

                      <div className="select-options max-h-60 overflow-y-auto py-1">
                        {filteredOptions.length === 0 ? (
                          <div className="flex h-10 items-center justify-center px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                            {searchable && searchTerm
                              ? "No results found"
                              : "No options available"}
                          </div>
                        ) : (
                          filteredOptions.map((option) => {
                            const isSelected = field.value === option.value;
                            return (
                              <button
                                key={option.value}
                                type="button"
                                disabled={option.disabled}
                                className={cn(
                                  "w-full px-3 py-2 text-left text-sm",
                                  "hover:bg-gray-100 disabled:cursor-not-allowed disabled:text-red-400 dark:hover:bg-gray-600 dark:disabled:text-red-500",
                                  isSelected
                                    ? "bg-primary-50 text-primary-600 dark:bg-primary-600/20 dark:text-primary-400"
                                    : "text-gray-900 dark:text-gray-200",
                                )}
                                onClick={() => {
                                  field.onChange(option.value);
                                  field.onBlur();
                                  onChange?.(option.value);
                                  setSearchTerm("");
                                  setIsOpen(false);
                                }}
                              >
                                <span className="block truncate">
                                  {option.label}
                                </span>
                              </button>
                            );
                          })
                        )}
                      </div>
                    </div>
                  </FloatingFocusManager>
                </FloatingPortal>
              )}
            </div>
          );
        }}
      />
    );
  },
);

Select.displayName = "Select";

export { Select };
