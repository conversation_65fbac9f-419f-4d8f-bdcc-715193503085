"use client";
import { Spin<PERSON> } from "flowbite-react";
import { Check, Pencil, X } from "lucide-react";
import { ComponentPropsWithoutRef, forwardRef, useRef, useState } from "react";
import { useOnClickOutside } from "usehooks-ts";

import { cn } from "@/lib/utils";

export type InlineEditProps = {
  /**
   * The current value to display and edit
   */
  value?: string;
  /**
   * Callback when the value is changed
   */
  onChange?: (value: string) => void;
  /**
   * Callback when the edit is saved
   */
  onSave?: (value: string) => void;
  /**
   * Callback when the edit is canceled
   */
  onCancel?: () => void;
  /**
   * Whether to use a multiline textarea instead of a single-line input
   */
  multiline?: boolean;
  /**
   * Whether the component is disabled
   */
  disabled?: boolean;
  /**
   * Placeholder text when empty
   */
  placeholder?: string;
  /**
   * Additional class name for the component wrapper
   */
  className?: string;
  /**
   * Additional class name for the input/textarea element
   */
  inputClassName?: string;
  /**
   * Additional class name for the save button
   */
  saveButtonClassName?: string;
  /**
   * Additional class name for the cancel button
   */
  cancelButtonClassName?: string;
  /**
   * Additional class name for the view mode container
   */
  viewModeClassName?: string;
  /**
   * Additional class name for the edit icon
   */
  editIconClassName?: string;
  isLoading?: boolean;
};

/**
 * InlineEdit component that allows editing text inline
 * Shows as normal text, but transforms to an editable field when clicked
 */
export const InlineEdit = forwardRef<
  HTMLDivElement,
  InlineEditProps & Omit<ComponentPropsWithoutRef<"div">, "onChange">
>(
  (
    {
      value,
      onChange,
      onSave,
      onCancel,
      multiline = false,
      disabled = false,
      placeholder = "Click to edit",
      className,
      inputClassName,
      saveButtonClassName,
      cancelButtonClassName,
      viewModeClassName,
      editIconClassName,
      isLoading,
      ...props
    },
    ref,
  ) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editValue, setEditValue] = useState(value);
    const editContainerRef = useRef<HTMLDivElement>(null);

    // Handle starting edit mode
    const handleStartEdit = () => {
      if (disabled) return;
      setIsEditing(true);
      setEditValue(value);
    };

    // Handle saving the edit
    const handleSave = () => {
      onChange?.(editValue ?? "");
      onSave?.(editValue ?? "");
      setIsEditing(false);
    };

    // Handle canceling the edit
    const handleCancel = () => {
      setEditValue(value);
      onCancel?.();
      setIsEditing(false);
    };

    // Handle key presses
    const handleKeyDown = (
      e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>,
    ) => {
      if (e.key === "Enter" && !multiline) {
        e.preventDefault();
        handleSave();
      } else if (e.key === "Escape") {
        e.preventDefault();
        handleCancel();
      }
    };

    // Handle clicking outside to save changes
    useOnClickOutside(editContainerRef, () => {
      if (isEditing) {
        handleSave();
      }
    });

    return (
      <div
        ref={ref}
        title={value || undefined}
        className={cn(
          "group/inline relative inline-block",
          disabled && "cursor-not-allowed opacity-70",
          className,
        )}
        {...props}
      >
        {isEditing ? (
          // Edit mode
          <div
            ref={editContainerRef}
            className={cn(
              "flex items-center gap-2",
              isEditing && "items-start",
            )}
          >
            {/* Input field */}
            {multiline ? (
              // Multiline textarea
              <textarea
                ref={(ref) => ref?.focus()}
                value={editValue}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setEditValue(e.target.value)
                }
                onKeyDown={handleKeyDown}
                className={cn(
                  "focus:border-primary-500 focus:ring-primary-500 min-h-[100px] w-full resize-none rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm dark:text-white",
                  inputClassName,
                )}
                disabled={disabled}
                rows={4}
              />
            ) : (
              // Single line input
              <input
                type="text"
                ref={(ref) => ref?.focus()}
                value={editValue}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setEditValue(e.target.value)
                }
                onKeyDown={handleKeyDown}
                className={cn(
                  "focus:border-primary-500 focus:ring-primary-500 h-10 w-full rounded-lg border border-gray-300 bg-transparent px-3 py-2 text-sm dark:text-white",
                  inputClassName,
                )}
                disabled={disabled}
              />
            )}

            {/* Action buttons - positioned to the right of the input */}
            <div className="flex flex-shrink-0 gap-1">
              <button
                type="button"
                onClick={handleCancel}
                className={cn(
                  "flex h-10 w-10 items-center justify-center rounded-md bg-gray-400 text-white hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2",
                  cancelButtonClassName,
                )}
                aria-label="Cancel"
              >
                <X className="h-5 w-5" />
              </button>
              <button
                type="button"
                onClick={handleSave}
                className={cn(
                  "bg-primary-700 hover:bg-primary-800 focus:ring-primary-500 flex h-10 w-10 items-center justify-center rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2",
                  saveButtonClassName,
                  isLoading && "animate-spin",
                )}
                aria-label="Save"
                disabled={isLoading}
              >
                {isLoading ? (
                  <Spinner size="sm" className="fill-white" />
                ) : (
                  <Check className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>
        ) : (
          // View mode
          <div
            className={cn(
              "relative flex w-full cursor-pointer items-center gap-1 rounded-md",
              !value && "italic text-gray-400",
              viewModeClassName,
            )}
            onClick={handleStartEdit}
          >
            <span className="truncate dark:text-white">
              {value?.trim() || placeholder}
            </span>

            {/* Edit icon on hover - positioned to the right of text */}
            <Pencil
              className={cn(
                "ml-2 size-4 shrink-0 text-gray-400 opacity-0 transition-opacity group-hover/inline:opacity-100",
                editIconClassName,
              )}
            />
          </div>
        )}
      </div>
    );
  },
);

InlineEdit.displayName = "InlineEdit";
