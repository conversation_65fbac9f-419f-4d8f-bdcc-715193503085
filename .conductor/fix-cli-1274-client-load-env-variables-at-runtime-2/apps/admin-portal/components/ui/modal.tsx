import { Modal as FlowbiteModal } from "flowbite-react";

import { cn } from "@/lib/utils";

const ModalHeader = (
  props: React.ComponentProps<typeof FlowbiteModal.Header>,
) => (
  <FlowbiteModal.Header
    {...props}
    className={cn("border-none p-5 pb-0", props.className)}
    theme={{
      ...props.theme,
      title: "text-base font-semibold uppercase leading-6 text-gray-400",
    }}
  />
);
const ModalBody = (props: React.ComponentProps<typeof FlowbiteModal.Body>) => (
  <FlowbiteModal.Body {...props} className={cn("my-6", props.className)} />
);
const ModalFooter = (
  props: React.ComponentProps<typeof FlowbiteModal.Footer>,
) => (
  <FlowbiteModal.Footer
    {...props}
    className={cn("border-none p-5 pt-0", props.className)}
  />
);
const Modal = FlowbiteModal;

export { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer, ModalHeader };
