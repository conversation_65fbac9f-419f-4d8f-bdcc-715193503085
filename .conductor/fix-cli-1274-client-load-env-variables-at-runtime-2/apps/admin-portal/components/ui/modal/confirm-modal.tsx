import { ComponentProps } from "react";

import { But<PERSON>, <PERSON>Button } from "../button";
import { WrapperModal } from "./wrapper-modal";

type Props = {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  onConfirm: () => void;
  isLoading?: boolean;
  confirmLabel?: string;
  size?: ComponentProps<typeof WrapperModal>["size"];
};

export const ConfirmModal = ({
  isOpen,
  onClose,
  children,
  onConfirm,
  isLoading,
  size,
  title = "Confirmation",
  confirmLabel = "Confirm",
}: Props) => {
  return (
    <WrapperModal size={size} isOpen={isOpen} onClose={onClose} title={title}>
      {children}
      <div className="mt-4 flex flex-col justify-end gap-4 sm:col-span-2 sm:flex-row">
        <CloseButton onClose={onClose} />
        <Button onClick={onConfirm} variant="primary" isLoading={isLoading}>
          {confirmLabel}
        </Button>
      </div>
    </WrapperModal>
  );
};
