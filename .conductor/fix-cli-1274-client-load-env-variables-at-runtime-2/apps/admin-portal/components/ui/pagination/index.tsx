"use client";
import { useTranslations } from "next-intl";
import { SetStateAction } from "react";
import { HiOutlineChevronLeft, HiOutlineChevronRight } from "react-icons/hi";
import ReactPaginate from "react-paginate";

import { usePagination } from "@/hooks/use-pagination";
import type { MetadataResponse } from "@/lib/apis/types";
import { cn } from "@/lib/utils";

type Props =
  | {
      isUseExternalState?: false;
      metadata: MetadataResponse;
    }
  | {
      isUseExternalState?: true;
      metadata: MetadataResponse;
      page: number;
      setPage: React.Dispatch<SetStateAction<number>>;
    };

export const TableDataPagination = (props: Props) => {
  const t = useTranslations();

  const { page, goToPage } = usePagination();

  const handlePageClick = (event: { selected: number }) => {
    props.isUseExternalState
      ? props.setPage(event.selected + 1)
      : goToPage(event.selected + 1);
  };
  const { currentPage, itemsPerPage, totalCount, totalPages } = props.metadata;
  const showing = currentPage > 0 ? (currentPage - 1) * itemsPerPage + 1 : 1;
  const maxRange =
    currentPage * itemsPerPage > totalCount
      ? totalCount
      : currentPage * itemsPerPage;

  return (
    <div
      className={cn(
        "flex p-4",
        props.metadata ? "justify-between" : "justify-end",
        !props.metadata.totalCount && "hidden",
      )}
    >
      {totalCount > 0 && (
        <div className="leading-5.25 text-sm text-gray-500">
          {t("Common.Showing")}{" "}
          <span className="leading-5.25 font-semibold text-gray-900 dark:text-white">
            {showing} - {maxRange}
          </span>{" "}
          {t("Common.of")}{" "}
          <span className="leading-5.25 font-semibold text-gray-900 dark:text-white">
            {props.metadata.totalCount}
          </span>
        </div>
      )}
      <div className="flex-col items-end justify-between peer-[.is-done]:!flex lg:flex-row">
        <div className="flex w-fit rounded-md border bg-white text-sm dark:border-gray-700 dark:bg-gray-800">
          <ReactPaginate
            className="flex items-center"
            pageLinkClassName="h-8 w-[34px] flex items-center justify-center text-gray-500 dark:text-gray-200 font-medium"
            pageClassName="text-gray-500 font-medium [&:nth-child(odd)]:border-x [&:nth-child(2)]:border-l [&:nth-last-child(2)]:border-r border-input dark:border-gray-700"
            activeClassName="bg-primary-100 dark:bg-primary-500 dark:[&>a]:text-white [&>a]:text-primary-700"
            breakLabel="..."
            forcePage={props.isUseExternalState ? props.page - 1 : page - 1}
            breakLinkClassName="text-gray-500 h-fit leading-none font-medium"
            breakClassName="text-gray-500 h-8 w-[34px] flex items-center justify-center font-medium [&:nth-child(odd)]:border-x [&:nth-child(2)]:border-l border-input dark:border-gray-700"
            nextLabel={<NextPage />}
            onPageChange={handlePageClick}
            pageRangeDisplayed={3}
            pageCount={totalPages}
            previousLabel={<PreviousPage />}
            marginPagesDisplayed={1}
            renderOnZeroPageCount={null}
            nextLinkClassName="flex h-8 items-center justify-center py-2.5 px-3"
            previousLinkClassName="flex h-8 items-center justify-center py-2.5 px-3"
          />
        </div>
      </div>
    </div>
  );
};

const NextPage = () => {
  return <HiOutlineChevronRight className="dark:text-gray-400" />;
};

const PreviousPage = () => {
  return <HiOutlineChevronLeft className="dark:text-gray-400" />;
};
