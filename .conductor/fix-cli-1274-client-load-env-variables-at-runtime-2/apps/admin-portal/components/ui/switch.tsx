"use client";

import { theme, ToggleSwitch } from "flowbite-react";
// import { forwardRef } from "react";
// import { Controller, useFormContext } from "react-hook-form";
import type { Merge } from "type-fest";

import { cn } from "@/lib/utils";

export type SwitchFieldProps = Merge<
  Omit<React.ComponentProps<typeof ToggleSwitch>, "onChange" | "checked">,
  {
    shouldShowError?: boolean;
    name: string;
  }
>;

// const SwitchField = forwardRef<HTMLInputElement, SwitchFieldProps>(
//   ({ name, shouldShowError = true, ...props }, ref) => {
//     const { control } = useFormContext();

//     return (
//       <Controller
//         name={name}
//         control={control}
//         render={({ field, formState: { errors } }) => {
//           const errorMessage = errors[name]?.message?.valueOf();
//           const hasError = typeof errorMessage === "string";

//           return (
//             <>
//               <ToggleSwitch
//                 {...field}
//                 {...props}
//                 ref={ref}
//                 checked={field.value}
//                 className={cn(
//                   "w-fit !outline-none !ring-0 [&>div]:!ring-0",
//                   props.className,
//                   hasError && "!border-red-500 !ring-red-500",
//                 )}
//                 theme={{
//                   ...theme,
//                   toggle: {
//                     ...theme.toggleSwitch.toggle,
//                     checked: {
//                       off: cn(
//                         theme.toggleSwitch.toggle.checked.off,
//                         "border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700",
//                         "after:rounded-full after:bg-white",
//                       ),
//                       on: cn(
//                         theme.toggleSwitch.toggle.checked.on,
//                         "!border-primary-600 !bg-primary-600 dark:border-primary-500 dark:bg-primary-500",
//                         "after:rounded-full after:bg-white",
//                       ),
//                     },
//                   },
//                 }}
//               />
//               {hasError && shouldShowError && (
//                 <span className="text-sm text-red-500">{errorMessage}</span>
//               )}
//             </>
//           );
//         }}
//       />
//     );
//   },
// );

// SwitchField.displayName = "SwitchField";

// export { SwitchField };

export type SwitchProps = Merge<
  Omit<React.ComponentProps<typeof ToggleSwitch>, "onChange" | "checked">,
  {
    checked: boolean;
    onChange: (checked: boolean) => void;
  }
>;

const Switch = ({ checked, onChange, ...props }: SwitchProps) => {
  return (
    <ToggleSwitch
      checked={checked}
      onChange={onChange}
      color="primary"
      sizing="sm"
      className={cn(
        "w-fit !outline-none !ring-0 [&>div]:!ring-0",
        props.className,
      )}
      theme={{
        ...theme,
        toggle: {
          ...theme.toggleSwitch.toggle,
          checked: {
            off: cn(
              theme.toggleSwitch.toggle.checked.off,
              "border-gray-200 bg-gray-200 dark:border-gray-600 dark:bg-gray-700",
              "after:rounded-full after:bg-white",
            ),
            on: cn(
              theme.toggleSwitch.toggle.checked.on,
              "!border-primary-600 !bg-primary-600 dark:border-primary-500 dark:bg-primary-500",
              "after:rounded-full after:bg-white",
            ),
          },
        },
      }}
      {...props}
    />
  );
};

Switch.displayName = "Switch";

export { Switch };
