"use client";
import {
  closestCenter,
  DndContext,
  type DragEndEvent,
  type DragStartEvent,
  KeyboardSensor,
  MouseSensor,
  PointerSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";
import {
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import type {
  ColumnDef,
  ColumnFiltersState,
  Row,
  RowSelectionState,
  SortingState,
  Table as TableType,
  VisibilityState,
} from "@tanstack/react-table";
import {
  flexRender,
  functionalUpdate,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Table, TableCell, TableRow } from "flowbite-react";
import type { CSSProperties, PropsWithChildren, ReactNode } from "react";
import { Fragment, useImperativeHandle, useState } from "react";
import { FaSort, FaSortDown, FaSortUp } from "react-icons/fa6";
import { IoIosArrowForward } from "react-icons/io";

import { useSort } from "@/hooks/use-sort";
import { cn } from "@/lib/utils";

import { Skeleton } from "../skeleton";

export type TableInstance<TData> = {
  table: TableType<TData>;
};

type TableDragAbleProps = {
  enable?: boolean;
  handleDragEnd?: (event: DragEndEvent) => void;
  handleDragStart?: (event: DragStartEvent) => void;
  disableDragOn?: string[];
};

type CollapsibleRowConfig<TData> = {
  enable: boolean;
  renderSubRow: (row: TData) => ReactNode;
  getRowId?: (row: TData) => string;
};

type TableDataProps<TData, TValue> = {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  tableRef?: React.ForwardedRef<TableInstance<TData>>;
  headCellStyle?: string;
  headStyle?: string;
  enableSorting?: boolean;
  isLoading?: boolean;
  draggable?: TableDragAbleProps;
  collapsible?: CollapsibleRowConfig<TData>;
  sorting?: SortingState;
  onSortingChange?: (
    updater: SortingState | ((old: SortingState) => SortingState),
  ) => void;
  draggingRowId?: string;
  manualSorting?: boolean;
};

const TableData = <TData, TValue>({
  data,
  columns,
  tableRef,
  headCellStyle = "text-gray-500 p-4 !rounded-none",
  headStyle,
  enableSorting = false,
  isLoading = false,
  draggable,
  collapsible,
  sorting: externalSorting,
  onSortingChange: externalOnSortingChange,
  draggingRowId,
  manualSorting = true,
}: TableDataProps<TData, TValue>) => {
  const { orderBy, orderDirection, changeSort } = useSort();
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const sorting = externalSorting
    ? externalSorting
    : !orderBy
      ? []
      : [{ id: orderBy, desc: orderDirection === "desc" }];

  const table = useReactTable({
    data,
    columns,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: externalOnSortingChange
      ? externalOnSortingChange
      : (updater) => {
          const newSorting = functionalUpdate(updater, sorting);
          const sort = newSorting[0];
          if (sort) return changeSort(sort.id, sort.desc ? "desc" : "asc");
          return changeSort();
        },
    onColumnVisibilityChange: setColumnVisibility,
    manualPagination: true,
    autoResetPageIndex: false,
    manualSorting: manualSorting,
    getRowId: (row) => (row as unknown as { id: string }).id,
    onRowSelectionChange: setRowSelection,
    state: {
      columnFilters,
      columnVisibility,
      rowSelection,
      sorting,
    },
  });

  useImperativeHandle(tableRef, () => ({ table }), [table]);

  const toggleRowExpanded = (rowId: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [rowId]: !prev[rowId],
    }));
  };

  const getRowId = (row: TData, index: number): string => {
    if (collapsible?.getRowId) {
      return collapsible.getRowId(row);
    }
    if ((row as unknown as { id: string }).id) {
      return (row as unknown as { id: string }).id;
    }
    return `row-${index}`;
  };

  return (
    <div className="overflow-x-auto">
      <TableBodyContainer
        enableDragging={draggable?.enable || false}
        handleDragEnd={draggable?.handleDragEnd}
        handleDragStart={draggable?.handleDragStart}
        table={table}
      >
        <Table>
          <Table.Head className={headStyle}>
            {table.getHeaderGroups().map((headerGroup) => (
              <Fragment key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => {
                  return (
                    <Table.HeadCell
                      key={`${header.id}-${index}`}
                      className={cn(
                        headCellStyle,
                        enableSorting &&
                          header.column.getCanSort() &&
                          "cursor-pointer",
                        "whitespace-nowrap px-6",
                      )}
                      style={{
                        width: (header.column.columnDef.meta as any)?.width,
                        minWidth: (header.column.columnDef.meta as any)?.width,
                      }}
                      onClick={
                        enableSorting
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                    >
                      <div className="flex gap-2">
                        <span>
                          {header.isPlaceholder
                            ? null
                            : flexRender(
                                header.column.columnDef.header,
                                header.getContext(),
                              )}{" "}
                        </span>
                        {enableSorting && (
                          <span>
                            {header.column.getCanSort() &&
                              !header.column.getIsSorted() && <FaSort />}
                            {header.column.getIsSorted() === "asc" && (
                              <FaSortUp />
                            )}
                            {header.column.getIsSorted() === "desc" && (
                              <FaSortDown />
                            )}
                          </span>
                        )}
                      </div>
                    </Table.HeadCell>
                  );
                })}
              </Fragment>
            ))}
          </Table.Head>

          <Table.Body className="divide-y dark:divide-gray-700">
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => {
                const rowId = getRowId(row.original, index);
                const isExpanded = expandedRows[rowId] || false;

                if (
                  draggable?.enable &&
                  !draggable?.disableDragOn?.includes(row.id)
                ) {
                  return (
                    <Fragment key={`${row.id}-${index}`}>
                      <DraggableRow row={row}>
                        {row.getVisibleCells().map((cell, idx) => (
                          <Table.Cell
                            key={`${cell.id}-${idx}`}
                            style={{
                              width: (cell.column.columnDef.meta as any)?.width,
                              minWidth: (cell.column.columnDef.meta as any)
                                ?.width,
                            }}
                            className={cn(
                              "max-w-80 last:max-w-none",
                              collapsible?.enable &&
                                idx === 0 &&
                                "cursor-pointer",
                            )}
                            onClick={
                              collapsible?.enable && idx === 0
                                ? () => toggleRowExpanded(rowId)
                                : undefined
                            }
                          >
                            {isLoading ? (
                              <Skeleton className="h-4" />
                            ) : (
                              <>
                                {collapsible?.enable && idx === 0 && (
                                  <span className="mr-2 inline-block">
                                    <IoIosArrowForward
                                      className={cn(
                                        "transition-transform",
                                        isExpanded && "rotate-90",
                                      )}
                                    />
                                  </span>
                                )}
                                {flexRender(
                                  cell.column.columnDef.cell,
                                  cell.getContext(),
                                )}
                              </>
                            )}
                          </Table.Cell>
                        ))}
                      </DraggableRow>
                      {collapsible?.enable && isExpanded && (
                        <TableRow className="bg-gray-50 dark:bg-gray-800/50">
                          <TableCell
                            colSpan={columns.length}
                            className="px-4 py-2"
                          >
                            <div className="rounded-md p-2">
                              {collapsible.renderSubRow(row.original)}
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </Fragment>
                  );
                }
                return (
                  <Fragment key={`${row.id}-${index}`}>
                    <Table.Row
                      data-state={row.getIsSelected() && "selected"}
                      className={cn(
                        "even:bg-gray-50 dark:even:bg-gray-700/20",
                        draggingRowId === rowId && "opacity-35",
                      )}
                    >
                      {row.getVisibleCells().map((cell, idx) => (
                        <Table.Cell
                          key={`${cell.id}-${idx}`}
                          style={{
                            width: (cell.column.columnDef.meta as any)?.width,
                            minWidth: (cell.column.columnDef.meta as any)
                              ?.width,
                          }}
                          className={cn(
                            "max-w-80 last:max-w-none",
                            collapsible?.enable &&
                              idx === 0 &&
                              "cursor-pointer",
                          )}
                          onClick={
                            collapsible?.enable && idx === 0
                              ? () => toggleRowExpanded(rowId)
                              : undefined
                          }
                        >
                          {isLoading ? (
                            <Skeleton className="h-4 dark:bg-gray-400" />
                          ) : (
                            <>
                              {collapsible?.enable && idx === 0 && (
                                <span className="mr-2 inline-block">
                                  <IoIosArrowForward
                                    className={cn(
                                      "transition-transform",
                                      isExpanded && "rotate-90",
                                    )}
                                  />
                                </span>
                              )}
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </>
                          )}
                        </Table.Cell>
                      ))}
                    </Table.Row>
                    {collapsible?.enable && isExpanded && (
                      <TableRow className="bg-gray-50 dark:bg-gray-800/50">
                        <TableCell
                          colSpan={columns.length}
                          className="px-4 py-2"
                        >
                          <div className="rounded-md p-2">
                            {collapsible.renderSubRow(row.original)}
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </Fragment>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results
                </TableCell>
              </TableRow>
            )}
          </Table.Body>
        </Table>
      </TableBodyContainer>
    </div>
  );
};

TableData.displayName = "TableData";

export { TableData };

const DraggableRow = ({
  row,
  children,
}: PropsWithChildren<{ row: Row<any> }>) => {
  const {
    transform,
    transition,
    setNodeRef,
    isDragging,
    attributes,
    listeners,
  } = useSortable({
    id: row.id,
  });

  const style: CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition: transition || undefined,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 0,
    position: "relative",
  };

  return (
    <Table.Row
      ref={setNodeRef}
      data-state={row.getIsSelected() && "selected"}
      className={cn(
        "transition-colors duration-200 even:bg-gray-50 dark:even:bg-gray-700/20",
        isDragging &&
          "bg-primary-50 dark:bg-primary-900/20 cursor-grabbing shadow-lg",
        !isDragging && "cursor-grab",
      )}
      {...attributes}
      {...listeners}
      style={style}
    >
      {children}
      {isDragging && (
        <div className="bg-primary-500/10 pointer-events-none absolute inset-0" />
      )}
    </Table.Row>
  );
};

const TableBodyContainer = ({
  children,
  enableDragging,
  handleDragEnd,
  handleDragStart,
  table,
}: PropsWithChildren<{
  enableDragging: boolean;
  handleDragEnd?: (event: DragEndEvent) => void;
  handleDragStart?: (event: DragStartEvent) => void;
  table: TableType<any>;
}>) => {
  const sensors = useSensors(
    useSensor(MouseSensor, {}),
    useSensor(TouchSensor, {}),
    useSensor(KeyboardSensor, {}),
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  if (!enableDragging) {
    return <>{children}</>;
  }

  return (
    <DndContext
      collisionDetection={closestCenter}
      modifiers={[restrictToVerticalAxis]}
      onDragEnd={handleDragEnd}
      onDragStart={handleDragStart}
      sensors={sensors}
    >
      <SortableContext
        items={table.getRowModel().rows.map((row) => row.id)}
        strategy={verticalListSortingStrategy}
      >
        {children}
      </SortableContext>
    </DndContext>
  );
};
