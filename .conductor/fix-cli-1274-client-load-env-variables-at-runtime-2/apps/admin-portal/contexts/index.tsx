import type { PropsWithChildren } from "react";

import { sidebarCookie } from "@/lib/sidebar-cookie";

import ReactQueryProvider from "./react-query-provider";
import { SidebarProvider } from "./sidebar-context";

export const Providers = async ({ children }: PropsWithChildren) => {
  const sidebarState = await sidebarCookie.get();
  return (
    <ReactQueryProvider>
      <SidebarProvider initialCollapsed={sidebarState.isCollapsed}>
        {children}
      </SidebarProvider>
    </ReactQueryProvider>
  );
};
