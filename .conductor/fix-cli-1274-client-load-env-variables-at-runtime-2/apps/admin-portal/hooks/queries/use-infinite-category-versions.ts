import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteCategoryVersion = (
  search: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["category-versions", search],
    queryFn: ({ pageParam = 1 }) =>
      api.artifactCategories.getVersions({
        page: pageParam,
        take: initialPageSize,
        orderBy: "version",
        orderDirection: "desc",
        filter: { version: search },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
