import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";
import { LocalStudyParams } from "@/lib/apis/sites";

export const useInfiniteLocalStudyPatients = (
  search: string,
  params: LocalStudyParams,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-local-study-patient", search, params],
    queryFn: ({ pageParam = 1 }) =>
      api.studies.getPatients({
        payload: params,
        params: {
          page: pageParam,
          take: initialPageSize,
          filter: { name: search },
        },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
