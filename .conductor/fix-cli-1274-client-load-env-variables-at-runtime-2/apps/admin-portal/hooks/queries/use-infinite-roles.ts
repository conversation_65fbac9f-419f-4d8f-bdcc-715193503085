import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/apis";

export const useInfiniteRoles = (
  search: string,
  type?: string,
  initialPageSize = 50,
) => {
  return useInfiniteQuery({
    queryKey: ["infinite-roles", search, type],
    queryFn: ({ pageParam = 1 }) =>
      api.roles.getRoles({
        page: pageParam,
        take: initialPageSize,
        filter: { name: search, type, isActive: true },
      }),
    getNextPageParam: (lastPage) => {
      if (lastPage.metadata.currentPage < lastPage.metadata.totalPages) {
        return lastPage.metadata.currentPage + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
  });
};
