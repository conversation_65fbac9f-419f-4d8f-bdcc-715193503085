import { useEffect, useState } from "react";

export const ROLLBACK_ACTIONS = {
  moveFolder: "MOVE_FOLDER",
  moveDocument: "MOVE_DOCUMENT",
} as const;

type Action =
  | {
      type: typeof ROLLBACK_ACTIONS.moveFolder;
      oldFolderId: null | string;
      newFolderId: null | string;
      folderId: string;
    }
  | {
      type: typeof ROLLBACK_ACTIONS.moveDocument;
      oldFolderId: string;
      newFolderId: string;
      documentId: string;
    };

type Option = {
  isAllowKeyboard?: boolean;
  onRollBack: (action: Action) => void;
};

export const useActionHistory = ({ onRollBack, isAllowKeyboard }: Option) => {
  const [actions, setActions] = useState<Action[]>([]);

  const latestAction = actions.length ? actions[actions.length - 1] : null;

  const addAction = (action: Action) => {
    setActions([...actions, action]);
  };

  const clearAction = () => setActions([]);

  const rollback = () => {
    if (!latestAction) return;
    setActions((prev) => prev.slice(0, -1));
    onRollBack(latestAction);
  };

  useEffect(() => {
    if (!isAllowKeyboard || !latestAction) return;
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "z") {
        event.preventDefault();
        rollback();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [actions]);

  return {
    actions,
    addAction,
    clearAction,
    rollback,
  };
};
