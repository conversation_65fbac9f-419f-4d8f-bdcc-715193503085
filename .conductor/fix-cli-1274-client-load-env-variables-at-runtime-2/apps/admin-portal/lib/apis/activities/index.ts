import <PERSON><PERSON><PERSON> from "../base";
import { MetadataParams } from "../types";
import {
  ActivityListResponse,
  ActivityProceduresResponse,
  CreateActivityPayload,
  CreateActivityProcedurePayload,
  ReorderActivityPayload,
  UpdateActivityPayload,
  UpdateActivityStatusPayload,
} from "./types";

class ActivitiesApi extends BaseApi {
  constructor() {
    super("/activities", true);
  }

  // Get activity's procedures
  async getActivityProcedures(id: string) {
    return this.http.get<ActivityProceduresResponse>(`/${id}/procedures`);
  }

  async list(
    id: string,
    params?: MetadataParams,
  ): Promise<ActivityListResponse> {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get(`/study/${id}?${paramUrl}`);
  }

  async create(payload: CreateActivityPayload) {
    return this.http.post("/", payload);
  }

  async import(payload: { formData: FormData; id: string }) {
    return this.http.post(`/study/${payload.id}/import`, payload.formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  async createActivityProcedures(
    id: string,
    payload: CreateActivityProcedurePayload,
  ) {
    return this.http.post(`/${id}/procedures`, payload);
  }

  async addProcedureToActivity(activityId: string, procedureIds: string[]) {
    return this.http.patch(`/${activityId}/procedures/`, {
      procedureIds,
    });
  }

  async updateActivityStatus(id: string, payload: UpdateActivityStatusPayload) {
    return this.http.patch(`/${id}/status`, payload);
  }

  async update(id: string, payload: UpdateActivityPayload) {
    console.log("=====> payload", payload);
    return this.http.put(`/${id}`, payload);
  }

  async removeProcedureFromActivity(activityId: string, procedureId: string) {
    return this.http.delete(`/${activityId}/procedures/${procedureId}`);
  }
  async reorderActivity(payload: ReorderActivityPayload) {
    return this.http.patch<boolean>(`/study/${payload.studyId}/reorder`, {
      orderedIds: payload.orderedIds,
    });
  }
}

export const activities = new ActivitiesApi();
export * from "./types";
