import { Procedure } from "../procedures";
import { ListBaseResponse } from "../types";

export interface Activity {
  id: string;
  name: string;
  description: string;
  isSystem: boolean;
  isActive: boolean;
  studyId: string;
  activityProcedures: {
    id: string;
    createdDate: Date;
    lastUpdatedDate: Date;
    activityId: string;
    procedureId: string;
    procedure: ProcedureDetail;
  }[];
}

export type ProcedureDetail = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  description: null;
  isActive: boolean;
  studyId: null;
};

export type ActivityProcedure = Procedure;

export type ActivityProceduresResponse = ListBaseResponse<Procedure>;
export type ActivityListResponse = ListBaseResponse<Activity>;

export type UpdateActivityStatusPayload = {
  isActive: boolean;
};

export type UpdateActivityPayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId?: string;
  isSystem?: boolean;
};

export type CreateActivityPayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId: string;
};

export type RemoveProcedureFromActivityPayload = {
  activityId: string;
  procedureId: string;
};

export type CreateActivityProcedurePayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  studyId: string;
  encounterId: string;
};

export type AddActivityProcedurePayload = {
  activityId: string;
  procedureId: string;
};

export type ReorderActivityPayload = {
  studyId: string;
  orderedIds: string[];
};
