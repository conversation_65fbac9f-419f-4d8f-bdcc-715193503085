import {
  ORIGINS,
  TMF_CORE,
} from "@/components/features/settings/artifact-categories/tabs/artifact-categories/modal-artifact-categories";

import { IIT_STUDY_ARTIFACTS } from "../../../components/features/settings/artifact-categories/tabs/artifact-categories/modal-artifact-categories";
import { CategorySummary } from "../category-ai-summary";
import { ISFRefModel } from "../isf-ref-models";
import { ListBaseResponse, MetadataParams } from "../types";

type TMFRefModel = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  tmfRefModel: string;
  description: string | null;
};

export type CategoryVersion = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  version: number;
  effectiveDate: string;
  notes: string;
  summary: null | CategorySummary;
};

export type CategoryVersionListResponse = ListBaseResponse<CategoryVersion>;

export type AddCategoryVersionPayload = {
  version: number;
  effectiveDate: string;
  notes?: string | null;
};

export type UpdateCategoryVersionPayload = AddCategoryVersionPayload & {
  id: string;
};

export type ArtifactCategory = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  tmfRefModelId: string;
  isfRefModelId: string;
  tmfZoneNumber: string | null;
  tmfZoneName: string | null;
  tmfSectionNumber: string | null;
  tmfSectionName: string | null;
  tmfRecordGroupNumber: string | null;
  tmfRecordGroupName: string | null;
  isfZoneNumber: string | null;
  isfZoneName: string | null;
  isfSectionNumber: string | null;
  isfSectionName: string | null;
  isfRecordGroupNumber: string | null;
  isfRecordGroupName: string | null;
  recordType: string;
  alternativeNames: string | null;
  description: string | null;
  requiresSignature: boolean | null;
  expires: boolean | null;
  inspectableRecord: boolean | null;
  includesPHI: boolean | null;
  tmfCore: TMFCore | null;
  isTMF: boolean;
  isISF: boolean;
  iitStudyArtifacts: IITStudyArtifact;
  isActive: boolean;
  origin: Origin;
  categoryVersionId: string;
  categoryVersion: CategoryVersion;
  tmfRefModel: TMFRefModel;
  isfRefModel: ISFRefModel;
};

export type ArtifactCategoriesResponse = ListBaseResponse<ArtifactCategory> & {
  metadata: {
    version: string;
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
};

export type Origin = (typeof ORIGINS)[number]["value"] | null;
export type TMFCore = (typeof TMF_CORE)[number] | null;
export type IITStudyArtifact = (typeof IIT_STUDY_ARTIFACTS)[number];

export type CreateArtifactCategoryPayload = {
  tmfRefModelId: string;
  isfRefModelId: string;
  categoryVersionId: string;

  tmfZoneNumber?: string | null;
  tmfZoneName?: string | null;
  tmfSectionNumber?: string | null;
  tmfSectionName?: string | null;
  tmfRecordGroupNumber?: string | null;
  tmfRecordGroupName?: string | null;

  isfZoneNumber?: string | null;
  isfZoneName?: string | null;
  isfSectionNumber?: string | null;
  isfSectionName?: string | null;
  isfRecordGroupNumber?: string | null;
  isfRecordGroupName?: string | null;

  recordType: string;
  alternativeNames?: string | null;
  description?: string | null;
  requiresSignature?: boolean | null;
  expires?: boolean | null;
  inspectableRecord?: boolean | null;
  includesPHI?: boolean | null;

  isTMF: boolean;
  isISF: boolean;
  origin?: Origin;

  tmfCore?: TMFCore;
  iitStudyArtifacts?: IITStudyArtifact;
  isActive?: boolean;
};

export type UpdateArtifactCategoryPayload = CreateArtifactCategoryPayload;

export type ArtifactCategoriesFilter = MetadataParams & {
  tmfZoneName?: string;
  tmfSectionName?: string;
  tmfRecordGroupName?: string;
  isfZoneName?: string;
  isfSectionName?: string;
  isfRecordGroupName?: string;
  recordType?: string;
  alternativeNames?: string;
  isTMF?: boolean;
  isISF?: boolean;
  version?: string;
  isActive?: boolean;
  versionLatest?: boolean;
  requiresSignature?: boolean;
  expires?: boolean;
  inspectableRecord?: boolean;
  includesPHI?: boolean;
};

export type ArtifactVersion = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  version: string;
  effectiveDate: Date;
  notes: string;
};

export type IsfZone = {
  id: string;
  name: string;
};

export type IsfSection = IsfZone;

export type Artifact = {
  id: string;
  name: string;
  subartifact: string;
};

export type AutoCategorizePayload = {
  filenames: string[];
  artifactCategoryVersionId: string;
  isTMF?: boolean;
};

type CategorizationMetadata = {
  confidenceLevel: number;
  reason: string;
};

type Folder = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  studyId: string;
  siteId: string;
  zoneNumber: string;
  sectionNumber: string;
  recordGroupNumber: string;
  parentDirectoryId: string;
  isEditable: boolean;
};

export type AutoCategorizeResponse = Record<
  string,
  {
    category: ArtifactCategory | null;
    categorizationMetadata: CategorizationMetadata;
    folder: Folder;
  } | null
>;
