/* Types of payload */
export type LoginPayload = {
  username: string;
  password: string;
};

export type ResetPasswordPayload = {
  token: string;
  newPassword: string;
};

export type UserType = "family" | "site" | "cro" | "aro" | "clincove";

type UserProfile = {
  id: string;
  name: string;
  type: UserType;
  currentGroup: {
    id: string;
    type: string;
  };
};

export type User = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  isActive: boolean;
  currentProfile: UserProfile;
  status: string;
  userId: string;
};

type UserPermissions = {
  id: string;
  action: string;
  description: string;
  permissionSubject: {
    id: string;
    name: string;
    description: string;
  };
};

/* Type of response */
export type LoginResponse = {
  expiresIn: string;
  user: User;
  permissions: UserPermissions[];
};

export type AuthenticatedUser = {
  authenticated: boolean;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  currentProfile: UserProfile;
};

export type UpdatePasswordPayload = {
  currentPassword: string;
  newPassword: string;
};
