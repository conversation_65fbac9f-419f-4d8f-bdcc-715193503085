import Base<PERSON>pi from "../base";
import { ListBaseResponse, MetadataParams } from "../types";
import {
  AiFailureRateResponse,
  AiSuccessRateResponse,
  AiSuccessRateTrendResponse,
  AutoCategorizationParams,
  AutoCategorizationRecord,
  ConversionRateResponse,
  MethodDistributionResponse,
  MethodTrendsResponse,
} from "./types";

export class CategorizationAnalyticsApi extends BaseApi {
  constructor() {
    super("/categorization-analytics", true);
  }

  public async getMethodDistribution(params?: AutoCategorizationParams) {
    return this.http.get<MethodDistributionResponse[]>(
      "/method-distribution",
      params,
    );
  }

  public async getMethodTrends(params?: AutoCategorizationParams) {
    return this.http.get<MethodTrendsResponse>("/method-trends", params);
  }

  public async getDrillDownTable(params?: MetadataParams) {
    return this.http.get<ListBaseResponse<AutoCategorizationRecord>>(
      "/drill-down-table",
      params,
    );
  }

  public async getAiSuccessRate(params?: AutoCategorizationParams) {
    return this.http.get<AiSuccessRateResponse>("/ai-success-rate", params);
  }

  public async getFailureRate(params?: AutoCategorizationParams) {
    return this.http.get<AiFailureRateResponse>("/failure-rate", params);
  }

  public async getConversionRate(params?: AutoCategorizationParams) {
    return this.http.get<ConversionRateResponse>("/conversion-rate", params);
  }

  public async getAiSuccessRateTrend(params?: AutoCategorizationParams) {
    return this.http.get<AiSuccessRateTrendResponse>(
      "/ai-success-rate-trend",
      params,
    );
  }
}

export const categorizationAnalyticsApi = new CategorizationAnalyticsApi();
export * from "./types";
