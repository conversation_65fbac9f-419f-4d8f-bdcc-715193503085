import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  Codelist,
  CodelistListResponse,
  CreateCodelistPayload,
  UpdateCodelistPayload,
} from "./types";

export class CodeListDefinitionsApi extends BaseApi {
  constructor() {
    super("/codelist-definitions", true);
  }

  public async list(
    studyId: string,
    params?: MetadataParams,
  ): Promise<CodelistListResponse> {
    return this.http.get<CodelistListResponse>(`/studies/${studyId}`, params);
  }

  public async get(id: string): Promise<Codelist> {
    return this.http.get<Codelist>(`/${id}`);
  }

  public async create(payload: CreateCodelistPayload): Promise<Codelist> {
    return this.http.post<Codelist>("", payload);
  }

  public async update(payload: UpdateCodelistPayload): Promise<Codelist> {
    return this.http.put<Codelist>(`/${payload.id}`, payload);
  }

  public async archive(id: string): Promise<Codelist> {
    return this.http.patch<Codelist>(`/${id}/archive`);
  }

  public async publish(id: string): Promise<Codelist> {
    return this.http.patch<Codelist>(`/${id}/publish`);
  }

  public async delete(id: string): Promise<void> {
    return this.http.delete<void>(`/${id}`);
  }

  public async newVersion(id: string): Promise<Codelist> {
    return this.http.post<Codelist>(`/${id}/new-version`);
  }
}

export const codeListDefinition = new CodeListDefinitionsApi();
