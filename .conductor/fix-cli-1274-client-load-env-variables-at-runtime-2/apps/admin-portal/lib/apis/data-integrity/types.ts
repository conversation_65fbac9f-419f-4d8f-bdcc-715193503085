import type { ListBaseResponse } from "../types";

export type OrphanedRecordStatistic = {
  activeStudiesNoProtocol: number;
  activeSitesNoAssignedUser: number;
  publishedProtocolsNoStudy: number;
  patientsAssignedInactiveStudyOrSite: number;
  tasksAssignInactiveUsersOrGroups: number;
};

export type NoTMFISFStudy = {
  id: string;
  name: string;
};

export type NoTMFISFStudiesResponse = ListBaseResponse<NoTMFISFStudy>;

export type StudyNoProtocol = {
  id: string;
  name: string;
};

export type StudyNoProtocolResponse = ListBaseResponse<StudyNoProtocol>;

export type SiteNoAssignedUser = {
  id: string;
  name: string;
};

export type SiteNoAssignedUserResponse = ListBaseResponse<SiteNoAssignedUser>;

export type ProtocolNoStudy = {
  id: string;
  name: string;
};

export type ProtocolNoStudyResponse = ListBaseResponse<ProtocolNoStudy>;

export type PatientInactive = {
  id: string;
  name: string;
  siteId: string;
  studyId: string;
};

export type PatientInactiveResponse = ListBaseResponse<PatientInactive>;

export type TaskInactive = {
  id: string;
  name: string;
  studyId: string;
  siteId: string;
};

export type TaskInactiveResponse = ListBaseResponse<TaskInactive>;

export type NoCategoryArtifact = {
  id: string;
  title: string;
  siteId: string;
  studyId: string;
  isfFolderId: string | null;
  tmfFolderId: string | null;
};

export type NoCategoryArtifactsResponse = ListBaseResponse<NoCategoryArtifact>;
