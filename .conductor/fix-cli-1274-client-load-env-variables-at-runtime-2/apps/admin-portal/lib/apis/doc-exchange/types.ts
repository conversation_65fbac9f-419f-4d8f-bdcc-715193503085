import { ActivityType } from "@/components/ui/badges/activity-badge";
import { DocumentType } from "@/components/ui/badges/document-type-badge";

import { FileRecord } from "../../models/file-record";
import type { ListBaseResponse } from "../types";

export type DocExchangeFolder = {
  id: string;
  name: string;
  parentDirectoryId: string | null;
  createdAt: string;
  updatedAt: string;
  subfolders: DocExchangeFolder[];
  fileCount: number;
  isEditable: boolean;
};

export type DocExchangeFolderResponse = ListBaseResponse<DocExchangeFolder>;

export type CreateDocExchangeFolderPayload = {
  name: string;
  parentFolderId?: string | null;
  siteId: string;
};

export type MoveDocExchangeFolderPayload = {
  parentDirectory: string | null;
};

export type UpdateDocExchangeFolderPayload = {
  name: string;
  parentDirectory?: string | null;
};

export type DocExchangeDocument = {
  id: string;
  name: string;
  type: string;
  size: number;
  createdAt: string;
  updatedAt: string;
  downloadUrl: string;
  isArchived: boolean;
  title: string;
  createdDate: string;
  parentDirectoryId: string | null;
  status: "pending" | "completed" | "failed";
  fileRecordId: string;
  fileRecord: FileRecord;
};

export type DocExchangeDocumentResponse = ListBaseResponse<DocExchangeDocument>;

export type CreateDocExchangeDocumentPayload = {
  title: string;
  siteId: string;
  parentDirectoryId: string;
  fileType: string;
  extension: string;
  originationType: string;
};

export type UpdateDocExchangeDocumentPayload = {
  name: string;
};

export type SignedUploadDocExchangeResponse = {
  url: string;
  currentDocument: DocExchangeDocument;
};

export type SignedUploadVerifyDocExchangePayload = {
  hmac: string;
};

export type DocExchangeActivity = {
  id: string;
  action: ActivityType;
  createdDate: string;
  lastUpdatedDate: string;
  profile?: {
    id: string;
    name: string;
    user?: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
};

export type DocExchangeActivityResponse = ListBaseResponse<DocExchangeActivity>;

export type EditDocExchangeDocumentPayload = {
  title?: string;
  parentDirectoryId?: string | null;
};

export type UploadMultipleFilesPayload = {
  siteId: string;
  files: Array<{
    fileName: string;
    contentType: string;
    originationType: string;
  }>;
};

export type UploadMultipleFilesResponse = Record<
  string,
  {
    url: string;
    fileId: string;
    hmacKey: string;
  }
>;

export type UpdateStatusFilesPayload = {
  files: Array<{
    fileId: string;
    status: string;
  }>;
};
