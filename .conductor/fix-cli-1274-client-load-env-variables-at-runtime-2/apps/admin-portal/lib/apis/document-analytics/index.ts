import BaseApi from "../base";
import { MetadataParams } from "../types";
import type {
  AgingDocumentsFilters,
  AgingDocumentsResponse,
  DocumentAnalyticsFilters,
  DocumentDetailFilters,
  DocumentDetailResponse,
  DocumentsPendingReviewResponse,
  DocumentVolumeOverTimeResponse,
  InactiveSitesResponse,
  ISFArtifactStatusBreakdownResponse,
  SiteDocExchangeResponse,
  SitePerformanceResponse,
  SourceDocumentStatusFunnelResponse,
  TotalDocumentsResponse,
  UploadsByOriginationTypeResponse,
} from "./types";

export * from "./types";

class DocumentAnalyticsApi extends BaseApi {
  constructor() {
    super("/document-analytics", true);
  }

  public async getTotalDocuments(params: DocumentAnalyticsFilters = {}) {
    return this.http.get<TotalDocumentsResponse>(
      "/summary/total-documents",
      params,
    );
  }

  public async getDocumentsPendingReview(
    params: DocumentAnalyticsFilters = {},
  ) {
    return this.http.get<DocumentsPendingReviewResponse>(
      "/summary/documents-pending-review",
      params,
    );
  }

  public async getDocumentVolumeOverTime(
    params: DocumentAnalyticsFilters = {},
  ) {
    return this.http.get<DocumentVolumeOverTimeResponse>(
      "/summary/document-volume-over-time",
      params,
    );
  }

  public async getUploadsByOriginationType(
    params: DocumentAnalyticsFilters = {},
  ) {
    return this.http.get<UploadsByOriginationTypeResponse>(
      "/summary/uploads-by-origination-type",
      params,
    );
  }

  // Document Detail Methods
  public async getTotalDocumentsDetail(params: DocumentDetailFilters = {}) {
    return this.http.get<DocumentDetailResponse>(
      "/detail/total-documents",
      params,
    );
  }

  public async getDocumentsPendingReviewDetail(
    params: DocumentDetailFilters = {},
  ) {
    return this.http.get<DocumentDetailResponse>(
      "/detail/documents-pending-review",
      params,
    );
  }

  // Document Workflow & Bottlenecks Methods
  public async getSourceDocumentStatusFunnel(
    params: DocumentAnalyticsFilters = {},
  ) {
    return this.http.get<SourceDocumentStatusFunnelResponse>(
      "/workflow/source-document-status-funnel",
      params,
    );
  }

  public async getISFArtifactStatusBreakdown(
    params: DocumentAnalyticsFilters = {},
  ) {
    return this.http.get<ISFArtifactStatusBreakdownResponse>(
      "/workflow/isf-artifact-status-breakdown",
      params,
    );
  }

  public async getAgingDocuments(params: AgingDocumentsFilters = {}) {
    return this.http.get<AgingDocumentsResponse>(
      "/workflow/aging-documents",
      params,
    );
  }

  public async getSiteActivityLeaderboard(params: MetadataParams = {}) {
    return this.http.get<SitePerformanceResponse>(
      "/leaderboard/site-activity-leaderboard",
      params,
    );
  }

  public async getInactiveSites(params: MetadataParams = {}) {
    return this.http.get<InactiveSitesResponse>(
      "/leaderboard/inactive-sites",
      params,
    );
  }

  public async getSiteDocExchange(params: MetadataParams = {}) {
    return this.http.get<SiteDocExchangeResponse>(
      "/leaderboard/site-doc-exchanges",
      params,
    );
  }
}

export const documentAnalytics = new DocumentAnalyticsApi();
