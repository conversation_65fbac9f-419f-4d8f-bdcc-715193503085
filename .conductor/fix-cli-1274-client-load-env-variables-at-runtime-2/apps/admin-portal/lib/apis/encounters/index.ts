import Base<PERSON><PERSON> from "../base";
import {
  AddEncounterActivityPayload,
  AddEncounterProcedurePayload,
  EncounterActivitiesResponse,
  EncounterProceduresResponse,
  RemoveActivityFromEncounterPayload,
} from "./types";

class EncountersApi extends BaseApi {
  constructor() {
    super("/encounters", true);
  }

  async getEncounterActivities(id: string) {
    return this.http.get<EncounterActivitiesResponse>(`/${id}/activities`);
  }

  async getEncounterProcedure(id: string) {
    return this.http.get<EncounterProceduresResponse>(`/${id}/procedures`);
  }

  async createEncounterProcedure(
    id: string,
    payload: AddEncounterProcedurePayload,
  ) {
    return this.http.post(`/${id}/procedures`, payload);
  }

  async removeActivityFromEncounter(
    payload: RemoveActivityFromEncounterPayload,
  ) {
    return this.http.delete(
      `/${payload.encounterId}/activities/${payload.activityId}`,
    );
  }

  async createEncounterActivity(
    id: string,
    payload: AddEncounterActivityPayload,
  ) {
    return this.http.post(`/${id}/activities`, payload);
  }

  async createExistingEncounterActivity(
    encounterId: string,
    activityIds: string[],
  ) {
    return this.http.patch(`/${encounterId}/activities`, {
      activityIds,
    });
  }

  async addActivityToEncounter(encounterId: string, activityId: string) {
    return this.http.post(`/${encounterId}/activities/${activityId}`);
  }
}

export const encounters = new EncountersApi();
export * from "./types";
