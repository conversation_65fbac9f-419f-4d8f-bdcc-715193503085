import { Activity } from "../activities/types";
import { Procedure } from "../procedures";
import { Protocol } from "../protocols";
import { ListBaseResponse } from "../types";

export type Encounter = {
  id: string;
  name: string;
  order: number;
  publishedDate?: string;
  publishedBy?: boolean;
  visitType?: {
    id: string;
    name: string;
  };
  protocols: Protocol[];
};

export type EncounterActivity = Activity;
export type EncounterProcedure = Procedure;

export type EncounterActivitiesResponse = ListBaseResponse<EncounterActivity>;
export type EncounterProceduresResponse = ListBaseResponse<EncounterProcedure>;
export type AddEncounterProcedurePayload = {
  name: string;
  description: string;
  isActive: boolean;
};
export type AddEncounterActivityPayload = {
  name: string;
  description?: string;
  isActive?: boolean;
  groupId?: string;
};

export type RemoveActivityFromEncounterPayload = {
  encounterId: string;
  activityId: string;
};
