import BaseApi from "../base";
import { MetadataParams } from "../types";
import type { EntitlementsListResponse } from "./types";

class EntitlementsApi extends BaseApi {
  constructor() {
    super("/entitlements", true);
  }

  public async list(
    params?: MetadataParams,
  ): Promise<EntitlementsListResponse> {
    const query = params ? this.generateQueryParams(params) : "";
    return this.http.get<EntitlementsListResponse>(`/?${query}`);
  }
}

export const entitlements = new EntitlementsApi();

export * from "./types";
