import <PERSON><PERSON><PERSON> from "../base";
import type { MetadataParams } from "../types";
import type {
  ArchiveFieldPayload,
  CreateFieldPayload,
  FieldLibraryItem,
  FieldLibraryListResponse,
  PublishFieldPayload,
  UpdateFieldPayload,
} from "./types";

export class FieldLibrary<PERSON>pi extends BaseApi {
  constructor() {
    super("/field-library", true);
  }

  public async list(
    params?: MetadataParams,
  ): Promise<FieldLibraryListResponse> {
    return this.http.get<FieldLibraryListResponse>("", params);
  }

  public async get(id: string): Promise<FieldLibraryItem> {
    return this.http.get<FieldLibraryItem>(`/${id}`);
  }

  public async create(payload: CreateFieldPayload): Promise<FieldLibraryItem> {
    return this.http.post<FieldLibraryItem>("", payload);
  }

  public async update(payload: UpdateFieldPayload): Promise<FieldLibraryItem> {
    return this.http.put<FieldLibraryItem>(`/${payload.id}`, payload);
  }

  public async archive(
    id: string,
    payload?: ArchiveFieldPayload,
  ): Promise<FieldLibraryItem> {
    return this.http.patch<FieldLibraryItem>(`/${id}/archive`, payload);
  }

  public async publish(
    id: string,
    payload?: PublishFieldPayload,
  ): Promise<FieldLibraryItem> {
    return this.http.patch<FieldLibraryItem>(`/${id}/publish`, payload);
  }
}
export const fieldLibrary = new FieldLibraryApi();
