import { FIELD_TYPES } from "@/components/features/studies/study-detail/tabs/form-studio/fields/columns";
import type { ListBaseResponse } from "@/lib/apis/types";

export type FieldStatus = "DRAFT" | "PUBLISHED" | "ARCHIVED";

export type FieldConfig = {
  allowPartial?: string;
  disablePastDates?: boolean;
  disableFutureDates?: boolean;
  maxLength?: number;
  min?: number;
  max?: number;
  unitOfMeasure?: string;
  layoutDirection?: "horizontal" | "vertical";
  decimalPlaces?: number;
  codeListId?: string;
  isDisplayOnForm?: boolean;
};

export type CdashMetadata = {
  domain?: string;
  cdashVersion?: string;
  questionText?: string;
  variableName?: string;
};

export type FieldLibraryItem = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  fieldName: string;
  fieldType: (typeof FIELD_TYPES)[number];
  displayName: string;
  shortCode: string;
  description?: string;
  status: FieldStatus;
  version: number;
  config?: FieldConfig;
  cdashMetadata?: CdashMetadata;
};

export type FieldLibraryListResponse = ListBaseResponse<FieldLibraryItem>;

export type ArchiveFieldPayload = {
  reason?: string;
};

export type PublishFieldPayload = {
  notes?: string;
};

export type CreateFieldPayload = {
  fieldName: string;
  studyId: string;
  fieldType: (typeof FIELD_TYPES)[number];
  displayName: string;
  description?: string;
  config?: FieldConfig;
  cdashMetadata?: CdashMetadata;
};

export type UpdateFieldPayload = CreateFieldPayload & {
  id: string;
};
