import { DocumentStatus } from "@/components/ui/badges/ebinder-document-badge";

import { FileRecord } from "../../models/file-record";
import { ArtifactCategory } from "../artifact-categories";
import type { ListBaseResponse } from "../types";

export type Folder = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  studyId: string;
  siteId: string;
  parentDirectoryId: string | null;
  isDirectory: boolean;
  subfolders: Folder[];
  fileCount: number;
  isEditable: boolean;
};

export type Comment = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  text: string;
};

export type CreateFolderPayload = {
  name: string;
  parentId?: string;
  studyId: string;
  siteId: string;
};

export type MoveFolderPayload = {
  parentDirectory: string | null;
  folderId: string;
  studyId: string;
  siteId: string;
  isRollback?: boolean;
};

export type UpdateFolderPayload = {
  name: string;
  parentId?: string;
  studyId: string;
  siteId: string;
};

export type FolderBinder = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  title: string;
  description: string;
  siteId: string;
  studyId: string;
  isActive: boolean;
  archived: boolean;
  expiryDate: string;
  summary?: string;
  tags?: unknown;
  version: [number, number, number];
  currentVersion?: {
    id: string;
    createdDate: string;
    lastUpdatedDate: string;
    fileRecordId: string;
    fileRecord: FileRecord;
    comment?: string;
    comments: Comment[];
    versionNumber: [number, number, number];
  };
  documentType: {
    id: string;
    createdDate: string;
    lastUpdatedDate: string;
    name: string;
    description?: string;
  };
  status: {
    id: string;
    createdDate: string;
    lastUpdatedDate: string;
    name: DocumentStatus;
    description?: string;
  };
  category: ArtifactCategory;
  extension: string;
  processingStatus: string;
  folderPath: string;
};

export type FolderBinderListResponse = ListBaseResponse<FolderBinder>;

export type EBinderFolder = {
  id: string;
  name: string;
  fileCount?: number;
  subfolders?: EBinderFolder[];
};

export type FolderResponse = {
  results: EBinderFolder[];
};

export type ArchiveFolderPayload = {
  folderId: string;
  studyId: string;
  siteId: string;
};

export type EbinderDocument = FolderBinder;
export type EbinderDocumentListResponse = ListBaseResponse<EbinderDocument>;
