import { ListBaseResponse, SiteStudyParams } from "../types";

export type CreateTemplatePayload = SiteStudyParams & {
  name: string;
};

export type ImportTemplatePayload = SiteStudyParams & {
  templateId: string;
};

export type IsfTemplate = {
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  name: string;
  isActive: boolean;
};

export type ListTemplateResponse = ListBaseResponse<IsfTemplate>;

export type CreateTemplateResponse = {
  directories: Directory[];
  id: string;
  createdDate: Date;
  lastUpdatedDate: Date;
  isActive: boolean;
  name: string;
};

export type Directory = {
  name: string;
  files: File[];
  directories: Directory[];
};

export type File = {
  title: string;
  description: null | string;
  categoryId: null | string;
  statusId: null | string;
};

export type ImportTemplateResponse = {
  success: boolean;
  message: string;
};

export type IsfTemplateFolderResponse = {
  directories: Directory[];
};
export type IsfTemplateFileResponse = {
  name: string | null;
  files: File[];
  directories: Directory[];
};

export type CreateFolderPayload = {
  parentDirectoryName?: string;
  name: string;
};

export type UpdateFolderPayload = {
  folderName: string;
  newFolderName: string;
};

export type CreateFilePayload = {
  parentDirectoryName: string;
  title: string;
  description?: string;
};

export type UpdateFilePayload = {
  parentDirectoryName: string;
  title: string;
  titleToUpdate: string;
  description?: string;
};

export type MoveFilePayload = {
  title: string;
  newParentDirectoryName: string;
  oldParentDirectoryName: string;
};
