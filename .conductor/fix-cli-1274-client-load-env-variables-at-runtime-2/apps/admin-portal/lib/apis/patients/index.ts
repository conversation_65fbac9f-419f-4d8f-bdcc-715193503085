import BaseApi from "../base";
import type { Visit, VisitListResponse } from "../patient-visits";
import type { MetadataParams } from "../types";
import type {
  AdverseEvent,
  AdverseEventListResponse,
  ConcomitantMeds,
  ConcomitantMedsListResponse,
  CreateAdverseEventPayload,
  CreateConcomitantMedPayload,
  CreateMedicalHistoryPayload,
  CreatePatientPayload,
  CreatePatientVisitPayload,
  MedicalHistory,
  MedicalHistoryListResponse,
  Patient,
  PatientListResponse,
  SchedulePatientVisitPayload,
  UpdatePatientPayload,
  UpdatePatientVisitPayload,
} from "./types";
import type {
  Consent,
  ConsentListResponse,
  ConsentSignedUploadResponse,
  ConsentSignedUrlUploadPayload,
  UpdateConsentPayload,
} from "./types/consent";
import type {
  AddPatientVisitPayload,
  EditVisitPayload,
  ReorderPatientVisitsPayload,
} from "./types/visit";

class PatientApi extends BaseApi {
  constructor() {
    super("/patients", true);
  }

  // Patients APIs
  public async patients(params: MetadataParams) {
    const paramUrl = this.generateQueryParams(params);

    return this.http.get<PatientListResponse>(`?${paramUrl}`);
  }

  public async create(data: CreatePatientPayload) {
    return this.http.post<Patient>("/", data);
  }

  public async update(id: string, data: UpdatePatientPayload) {
    return this.http.put<Patient>(`/${id}`, data);
  }

  public async patient(id: string): Promise<Patient> {
    return this.http.get<Patient>(`/${id}`);
  }

  public async delete(id: string) {
    return this.http.delete<Patient>(`/${id}`);
  }

  public async schedulePatientVisit(
    id: string,
    visitId: string,
    data: SchedulePatientVisitPayload,
  ) {
    return this.http.put<Patient>(`/${id}/patient-visits/${visitId}`, data);
  }

  public async updatePatientVisit(
    patientId: string,
    visitId: string,
    data: UpdatePatientVisitPayload,
  ) {
    return this.http.put<Patient>(
      `/${patientId}/patient-visits/${visitId}`,
      data,
    );
  }

  // Patient Visits APIs
  public async patientVisits(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<VisitListResponse>(
      `/${id}/patient-visits?${paramUrl}`,
    );
  }

  public async createPatientVisit(id: string, data: CreatePatientVisitPayload) {
    return this.http.post<Visit>(`/${id}/patient-visits`, data);
  }

  public async addPatientVisit(id: string, data: AddPatientVisitPayload) {
    return this.http.post<Visit>(`/${id}/patient-visits`, data);
  }

  public async deletePatientVisit(id: string, visitId: string) {
    return this.http.delete<Visit>(`/${id}/patient-visits/${visitId}`);
  }

  public async editPatientVisit(
    id: string,
    visitId: string,
    data: EditVisitPayload,
  ) {
    return this.http.put<Visit>(`/${id}/patient-visits/${visitId}`, data);
  }

  public async patientVisit(id: string, visitId: string) {
    return this.http.get<Visit>(`/${id}/patient-visits/${visitId}`);
  }

  public async reorderPatientVisits(
    id: string,
    data: ReorderPatientVisitsPayload,
  ) {
    return this.http.put<Visit>(`/${id}/patient-visits/reorder`, data);
  }

  // Patient Consents APIs
  public async patientConsents(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<ConsentListResponse>(`/${id}/consents?${paramUrl}`);
  }

  public async consentSignedUrlUpload(
    id: string,
    payload: ConsentSignedUrlUploadPayload,
  ) {
    return this.http.post<ConsentSignedUploadResponse>(
      `/${id}/consents/signed-url-upload`,
      payload,
    );
  }

  public async consentVerifyUpload(
    id: string,
    consentId: string,
    payload: { hmac: string },
  ) {
    return this.http.put<{ url: string }>(
      `/${id}/consents/${consentId}/verify-upload`,
      payload,
    );
  }

  public async deleteConsent(id: string, consentId: string) {
    return this.http.delete<Consent>(`/${id}/consents/${consentId}`);
  }

  public async updateConsent(
    id: string,
    consentId: string,
    payload: UpdateConsentPayload,
  ) {
    return this.http.put<Consent>(`/${id}/consents/${consentId}`, payload);
  }

  public async getConsentById(id: string, consentId: string) {
    return this.http.get<Consent>(`/${id}/consents/${consentId}`);
  }

  public async consentSignedUrlDownload(id: string, consentId: string) {
    return this.http.get<{ url: string }>(
      `/${id}/consents/${consentId}/signed-url-download`,
    );
  }

  // Patient Adverse Events APIs
  public async createAdverseEvent(
    id: string,
    payload: CreateAdverseEventPayload,
  ) {
    return this.http.post<AdverseEvent>(`/${id}/adverse-events`, payload);
  }

  public async adverseEvents(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<AdverseEventListResponse>(
      `/${id}/adverse-events?${paramUrl}`,
    );
  }

  // Patient Medical History APIs
  public async createMedicalHistory(
    id: string,
    payload: CreateMedicalHistoryPayload,
  ) {
    return this.http.post<MedicalHistory>(`/${id}/medical-history`, payload);
  }

  public async medicalHistories(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<MedicalHistoryListResponse>(
      `/${id}/medical-histories?${paramUrl}`,
    );
  }

  // Patient Concomitant Meds APIs
  public async createConcomitantMed(
    id: string,
    payload: CreateConcomitantMedPayload,
  ) {
    return this.http.post<ConcomitantMeds>(`/${id}/concomitant-meds`, payload);
  }

  public async concomitantMeds(id: string, params: MetadataParams = {}) {
    const paramUrl = this.generateQueryParams(params);
    return this.http.get<ConcomitantMedsListResponse>(
      `/${id}/concomitant-meds?${paramUrl}`,
    );
  }
}
export const patients = new PatientApi();

export * from "./types";
