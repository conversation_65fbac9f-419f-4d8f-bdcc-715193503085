import type { ListBaseResponse } from "../../types";
import type { Patient } from "./patient";

export type ConcomitantMeds = {
  name: string;
  quantity: number;
  units: string;
  frequency: string;
  route: string;
  status: string;
  startDate: string;
  endDate: string;
  patientId: string;
  patient: Patient;
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
};

export type CreateConcomitantMedPayload = {
  name: string;
  quantity: number;
  units: string;
  frequency: string;
  route: string;
  status: string;
  startDate: string;
  endDate: string;
};

export type ConcomitantMedsListResponse = ListBaseResponse<ConcomitantMeds>;
