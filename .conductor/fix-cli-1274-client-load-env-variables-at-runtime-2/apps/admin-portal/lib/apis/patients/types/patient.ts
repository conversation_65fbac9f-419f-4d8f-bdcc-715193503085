import type { Visit } from "../../patient-visits";
import { Sponsor } from "../../sponsors/types";
import type { Study } from "../../studies";
import type { ListBaseResponse } from "../../types";

type Site = {
  id: string;
  name: string;
};

export type PatientStatuses =
  | "preScreening"
  | "screening"
  | "enrolled"
  | "withdrawn"
  | "complete"
  | "screenFailed";

export const PATIENT_STATUSES: Array<PatientStatuses> = [
  "preScreening",
  "screening",
  "enrolled",
  "withdrawn",
  "complete",
  "screenFailed",
];

export const PATIENT_STATUSES_MAP: Record<PatientStatuses, string> = {
  preScreening: "Pre-Screening",
  screening: "Screening",
  enrolled: "Enrolled",
  withdrawn: "Withdrawn",
  complete: "Complete",
  screenFailed: "Screen Failed",
};

export type Patient = {
  id: string;
  createdDate: string;
  lastUpdatedDate: string;
  name: string;
  name2: string | null;
  dateOfBirth: string;
  notes: string | null;
  status: PatientStatuses;
  studyId: string;
  isActive: boolean;
  archived: boolean;
  site: Site;
  study: Study;
  sponsor: Sponsor;
  sex: string;
  enrollmentDate: string;
  lastVisit?: Visit;
  nextVisit?: Visit;
  firstName: string;
  lastName: string;
  currentProtocolId?: string;
  currentProtocol?: {
    id: string;
    name: string;
    version: string;
  };
  studyArmId?: string;
  studyArm?: {
    id: string;
    name: string;
  };
  shortId?: string;
  dayOneStartDate?: string;
};

export type PatientListResponse = ListBaseResponse<Patient>;

export type CreatePatientPayload = {
  name: string;
  name2?: string;
  studyId: string;
  siteId: string;
  notes?: string;
  status: string;
  dateOfBirth: string;
  sex: string;
  firstName: string;
  lastName: string;
  studyArmId?: string;
};

export type UpdatePatientPayload = {
  name?: string;
  dateOfBirth?: string | null;
  sex?: string;
  firstName?: string;
  lastName?: string;
  status?: string;
  currentProtocolId?: string;
  studyArmId?: string | null;
  enrollmentDate?: string | null;
  dayOneStartDate?: string | null;
};

export type SchedulePatientVisitPayload = {
  name?: string;
  visitStatus?: string;
  visitDate: string;
};
