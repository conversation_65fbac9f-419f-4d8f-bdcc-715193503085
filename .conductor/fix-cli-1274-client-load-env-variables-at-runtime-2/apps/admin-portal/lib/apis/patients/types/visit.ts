export type CreatePatientVisitPayload = {
  patientId: string;
  name: string;
  previousPatientVisitId?: string | null;
  visitStatus: string;
  visitDate?: string | null;
  protocolVisitId: string;
};

export type UpdatePatientVisitPayload = {
  name?: string;
  visitStatus?: string;
  visitDate?: string | null;
  protocolVisitId?: string;
  previousPatientVisitId?: string | null;
  visitOrder?: number;
};

export type ReorderPatientVisitsPayload = {
  visitIds: string[];
};

export type AddPatientVisitPayload = {
  encounterId?: string; // for pre-defined visit
  name?: string;
  visitStatusId?: string;
  description?: string;
  visitTypeId?: string;
  epochId?: string;
  studyArmId?: string;
  visitDay?: number;
  visitWindowStart?: number;
  visitWindowEnd?: number;
  activities?: {
    name: string;
    procedures?: {
      name: string;
    }[];
  }[];
  visitDate?: string | null;
};

export type EditVisitPayload = Partial<AddPatientVisitPayload>;
