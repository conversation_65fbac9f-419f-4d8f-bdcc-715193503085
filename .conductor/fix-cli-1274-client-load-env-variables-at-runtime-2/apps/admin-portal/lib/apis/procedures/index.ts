import Base<PERSON>pi from "../base";
import { MetadataParams } from "../types";
import {
  CreateProcedurePayload,
  ProcedureListResponse,
  ProceduresResponse,
  ReorderProceduresPayload,
  UpdateProcedurePayload,
  UpdateProcedureStatusPayload,
} from "./types";

class ProceduresApi extends BaseApi {
  constructor() {
    super("/procedures", true);
  }

  async getProcedure(id: string) {
    return this.http.get<ProceduresResponse>(`/${id}`);
  }

  async list(
    id: string,
    params?: MetadataParams,
  ): Promise<ProcedureListResponse> {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<ProceduresResponse>(`/study/${id}?${paramUrl}`);
  }

  async updateProcedureStatus(
    id: string,
    payload: UpdateProcedureStatusPayload,
  ) {
    return this.http.patch(`/${id}/status`, payload);
  }

  async create(payload: CreateProcedurePayload) {
    return this.http.post("/", payload);
  }

  async import(payload: { formData: FormData; id: string }) {
    return this.http.post(`/study/${payload.id}/import`, payload.formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  }

  async update(id: string, payload: UpdateProcedurePayload) {
    return this.http.put(`/${id}`, payload);
  }
  async reorderProcedures(payload: ReorderProceduresPayload) {
    return this.http.patch<boolean>(`/study/${payload.studyId}/reorder`, {
      orderedIds: payload.orderedIds,
    });
  }
}

export const procedures = new ProceduresApi();
export * from "./types";
