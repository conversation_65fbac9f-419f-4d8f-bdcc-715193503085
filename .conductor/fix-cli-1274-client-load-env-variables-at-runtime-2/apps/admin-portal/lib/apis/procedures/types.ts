import { Study } from "../studies";
import { ListBaseResponse } from "../types";

export interface Procedure {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  createdDate: string;
  lastUpdatedDate: string;
  studyId: string;
  study: Study;
}

export type ProceduresResponse = ListBaseResponse<Procedure>;
export type UpdateProcedureStatusPayload = {
  isActive: boolean;
};

export type ProcedureListResponse = ListBaseResponse<Procedure>;

export type UpdateProcedurePayload = {
  name: string;
  description?: string;
  isActive: boolean;
  studyId: string;
};

export type CreateProcedurePayload = {
  name: string;
  description?: string;
  isActive: boolean;
  studyId: string;
};

export type ReorderProceduresPayload = {
  studyId: string;
  orderedIds: string[];
};
