import BaseApi from "../base";
import type { MetadataParams } from "../types";
import type {
  AddPromptVariablePayload,
  PromptVariable,
  PromptVariableListResponse,
  ResolverListResponse,
  UpdatePromptVariablePayload,
} from "./types";

class PromptVariableApi extends BaseApi {
  constructor() {
    super("/ai-prompt-variable", true);
  }

  public async create(payload: AddPromptVariablePayload) {
    return this.http.post<PromptVariable>("/", payload);
  }

  public async list(params?: MetadataParams) {
    const paramUrl = params ? this.generateQueryParams(params) : "";
    return this.http.get<PromptVariableListResponse>(`?${paramUrl}`);
  }

  public async update(payload: UpdatePromptVariablePayload) {
    return this.http.patch<PromptVariable>(`/${payload.id}`, payload);
  }

  public async delete(id: string) {
    return this.http.delete(`/${id}`);
  }

  public async listResolvers() {
    return this.http.get<ResolverListResponse>(`/resolver-functions`);
  }
}

export const promptVariables = new PromptVariableApi();
export * from "./types";
